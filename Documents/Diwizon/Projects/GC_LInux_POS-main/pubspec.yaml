name: go_charge
description: "Go Charge India"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+22

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_screenutil: ^5.9.0
  draggable_bottom_sheet: ^1.0.2
  syncfusion_flutter_sliders: ^28.1.39
  form_field_validator: ^1.1.0
  # provider: ^6.0.5
  # dartz: ^0.10.1
  amplify_flutter: ^2.6.0
  # amplify_auth_cognito: ^2.4.1
  # amplify_authenticator: ^2.1.3
  amplify_api: ^2.6.0
  go_router: ^14.2.7
  # amplify_datastore: ^2.4.1
  hive: ^2.2.3
  path_provider: ^2.1.2
  get: ^4.6.6
  sms_autofill: ^2.3.1
  loading_animation_widget: ^1.2.1
  google_fonts: ^6.2.1
  # google_maps_flutter: ^2.6.0
  # geolocator: ^12.0.0
  url_launcher: ^6.2.5
  # qr_code_scanner: ^1.0.1
  scanning_effect: ^1.0.4
  share_plus: ^10.0.0
  shimmer: ^3.0.0
  lottie: ^3.1.0
  # razorpay_flutter: ^1.3.6
  http: ^1.2.1
  intl: ^0.20.1
  printing: ^5.12.0
  pdf: ^3.10.8
  string_validator: ^1.1.0
  # battery_plus: ^6.0.3
  # battery_indicator: ^0.1.2
  indian_currency_to_word: ^1.0.0
  crypto: ^3.0.6
  battery_indicator:
    git:
      url: https://github.com/Mohammadwabeel/battery_indicator.git
      ref: master
  phonepe_payment_sdk: ^2.0.3
  package_info_plus: ^8.1.1
  # flutter_linux_webview: ^0.1.3
  # webview_flutter: ^4.11.0
  # webview_flutter_web: ^0.2.3+4
  webview_flutter: ^4.10.0
  phonepe_pg_htkc: ^1.0.2
  flutter_inappwebview: ^6.1.5
  

dependency_overrides:
  package_info_plus: ^8.0.2
  web: ^1.0.0
  # webview_flutter_platform_interface: ^1.8.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/connectors/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages