allprojects {
    repositories {
        google()
        mavenCentral()
           maven {
            url  "https://phonepe.mycloudrepo.io/public/repositories/phonepe-intentsdk-android"
       }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
