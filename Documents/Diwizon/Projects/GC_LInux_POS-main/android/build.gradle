// allprojects {
//     repositories {
//         google()
//         mavenCentral()
//            maven {
//             url  "https://phonepe.mycloudrepo.io/public/repositories/phonepe-intentsdk-android"
//        }
//     }
// }

// rootProject.buildDir = "../build"
// subprojects {
//     project.buildDir = "${rootProject.buildDir}/${project.name}"
// }
// subprojects {
//     project.evaluationDependsOn(":app")
// }

// tasks.register("clean", Delete) {
//     delete rootProject.buildDir
// }

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.2.2"
        classpath 'com.google.gms:google-services:4.4.0' // if using Firebase
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(":app")
}
