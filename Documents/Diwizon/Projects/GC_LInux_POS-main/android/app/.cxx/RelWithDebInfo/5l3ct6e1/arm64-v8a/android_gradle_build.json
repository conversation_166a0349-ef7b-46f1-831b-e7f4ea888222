{"buildFiles": ["/Users/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Diwizon/Projects/GC_LInux_POS-main/android/app/.cxx/RelWithDebInfo/5l3ct6e1/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Diwizon/Projects/GC_LInux_POS-main/android/app/.cxx/RelWithDebInfo/5l3ct6e1/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/Sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}