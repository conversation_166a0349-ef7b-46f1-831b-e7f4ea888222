{"buildFiles": ["/Users/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Diwizon/Projects/GC_LInux_POS-main/android/app/.cxx/Debug/z6o3b3n5/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Diwizon/Projects/GC_LInux_POS-main/android/app/.cxx/Debug/z6o3b3n5/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}