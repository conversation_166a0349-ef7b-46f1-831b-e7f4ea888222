{"api": {"gocharge": {"dependsOn": [], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}], "defaultAuthentication": {"apiKeyConfig": {"apiKeyExpirationDays": 30, "description": "api key description"}, "authenticationType": "API_KEY"}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"gocharge": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OPTIONAL", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": [], "socialProviders": [], "usernameAttributes": ["PHONE_NUMBER"], "verificationMechanisms": ["PHONE_NUMBER"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "function": {"autoCancelBooking": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "bookingCheckSchedular": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "cancelCharging": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "chargeSessionLambda": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "checkRfid": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "createPreBooking": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "deleteOldLogs": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "getChargerCount": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "getPaymentDetails": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "gochargenpmLayer": {"build": true, "providerPlugin": "awscloudformation", "service": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "insertLogs": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "issueLamda": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "postTester": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "razorOrder": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "refundEvents": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "refundOrder": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateChargePercent": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateChargerStatus": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateConfiguration": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateConnectorStatus": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateLastHeartbeat": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateListVersion": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateMeterStopValue": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateMeterValues": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateTransId": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "verifyPayment": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "verifyPayout": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_autoCancelBooking_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "autoCancelBooking"}]}, "AMPLIFY_function_autoCancelBooking_s3Key": {"usedBy": [{"category": "function", "resourceName": "autoCancelBooking"}]}, "AMPLIFY_function_bookingCheckSchedular_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "bookingCheckSchedular"}]}, "AMPLIFY_function_bookingCheckSchedular_s3Key": {"usedBy": [{"category": "function", "resourceName": "bookingCheckSchedular"}]}, "AMPLIFY_function_cancelCharging_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "cancelCharging"}]}, "AMPLIFY_function_cancelCharging_s3Key": {"usedBy": [{"category": "function", "resourceName": "cancelCharging"}]}, "AMPLIFY_function_chargeSessionLambda_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "chargeSessionLambda"}]}, "AMPLIFY_function_chargeSessionLambda_s3Key": {"usedBy": [{"category": "function", "resourceName": "chargeSessionLambda"}]}, "AMPLIFY_function_checkRfid_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "checkRfid"}]}, "AMPLIFY_function_checkRfid_s3Key": {"usedBy": [{"category": "function", "resourceName": "checkRfid"}]}, "AMPLIFY_function_createPreBooking_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "createPreBooking"}]}, "AMPLIFY_function_createPreBooking_s3Key": {"usedBy": [{"category": "function", "resourceName": "createPreBooking"}]}, "AMPLIFY_function_deleteOldLogs_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "deleteOldLogs"}]}, "AMPLIFY_function_deleteOldLogs_s3Key": {"usedBy": [{"category": "function", "resourceName": "deleteOldLogs"}]}, "AMPLIFY_function_getChargerCount_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "get<PERSON>harger<PERSON>ount"}]}, "AMPLIFY_function_getChargerCount_s3Key": {"usedBy": [{"category": "function", "resourceName": "get<PERSON>harger<PERSON>ount"}]}, "AMPLIFY_function_getPaymentDetails_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "getPaymentDetails"}]}, "AMPLIFY_function_getPaymentDetails_s3Key": {"usedBy": [{"category": "function", "resourceName": "getPaymentDetails"}]}, "AMPLIFY_function_gochargenpmLayer_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_gochargenpmLayer_s3Key": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_insertLogs_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "insertLogs"}]}, "AMPLIFY_function_insertLogs_s3Key": {"usedBy": [{"category": "function", "resourceName": "insertLogs"}]}, "AMPLIFY_function_issueLamda_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "issueLamda"}]}, "AMPLIFY_function_issueLamda_s3Key": {"usedBy": [{"category": "function", "resourceName": "issueLamda"}]}, "AMPLIFY_function_postTester_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "postTester"}]}, "AMPLIFY_function_postTester_s3Key": {"usedBy": [{"category": "function", "resourceName": "postTester"}]}, "AMPLIFY_function_razorOrder_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "razorOrder"}]}, "AMPLIFY_function_razorOrder_s3Key": {"usedBy": [{"category": "function", "resourceName": "razorOrder"}]}, "AMPLIFY_function_refundEvents_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "refundEvents"}]}, "AMPLIFY_function_refundEvents_s3Key": {"usedBy": [{"category": "function", "resourceName": "refundEvents"}]}, "AMPLIFY_function_refundOrder_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "refundOrder"}]}, "AMPLIFY_function_refundOrder_s3Key": {"usedBy": [{"category": "function", "resourceName": "refundOrder"}]}, "AMPLIFY_function_updateChargePercent_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateChargePercent"}]}, "AMPLIFY_function_updateChargePercent_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateChargePercent"}]}, "AMPLIFY_function_updateChargerStatus_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateChargerStatus"}]}, "AMPLIFY_function_updateChargerStatus_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateChargerStatus"}]}, "AMPLIFY_function_updateConfiguration_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateConfiguration"}]}, "AMPLIFY_function_updateConfiguration_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateConfiguration"}]}, "AMPLIFY_function_updateConnectorStatus_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateConnectorStatus"}]}, "AMPLIFY_function_updateConnectorStatus_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateConnectorStatus"}]}, "AMPLIFY_function_updateLastHeartbeat_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateLastHeartbeat"}]}, "AMPLIFY_function_updateLastHeartbeat_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateLastHeartbeat"}]}, "AMPLIFY_function_updateListVersion_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateListVersion"}]}, "AMPLIFY_function_updateListVersion_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateListVersion"}]}, "AMPLIFY_function_updateMeterStopValue_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateMeterStopValue"}]}, "AMPLIFY_function_updateMeterStopValue_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateMeterStopValue"}]}, "AMPLIFY_function_updateMeterValues_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateMeterValues"}]}, "AMPLIFY_function_updateMeterValues_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateMeterValues"}]}, "AMPLIFY_function_updateTransId_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateTransId"}]}, "AMPLIFY_function_updateTransId_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateTransId"}]}, "AMPLIFY_function_verifyPayment_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "verifyPayment"}]}, "AMPLIFY_function_verifyPayment_s3Key": {"usedBy": [{"category": "function", "resourceName": "verifyPayment"}]}, "AMPLIFY_function_verifyPayout_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "verifyPayout"}]}, "AMPLIFY_function_verifyPayout_s3Key": {"usedBy": [{"category": "function", "resourceName": "verifyPayout"}]}}, "storage": {}}