{"version": "1", "cognitoConfig": {"identityPoolName": "testAuthIdentityPool", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "gochar6d26b5a1", "userPoolName": "gocharge", "autoVerifiedAttributes": ["phone_number"], "mfaConfiguration": "OPTIONAL", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": [], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": [], "userpoolClientReadAttributes": [], "userpoolClientLambdaRole": "gochar6d26b5a1_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "6d26b5a1", "resourceName": "gocharge", "authSelections": "identityPoolAndUserPool", "serviceName": "Cognito", "usernameAttributes": ["phone_number"], "useDefault": "manual", "userPoolGroups": false, "userPoolGroupList": [], "adminQueries": false, "thirdPartyAuth": false, "authProviders": [], "usernameCaseSensitive": false, "useEnabledMfas": true}}