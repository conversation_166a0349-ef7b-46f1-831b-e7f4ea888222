
const AWS = require('aws-sdk');
const dayjs = require('dayjs');

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

let body;
exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);

    return await deleteOldLogs();
};



const deleteOldLogs = async () => {
    // Initialize the DynamoDB Document Client
    const dynamodb = new AWS.DynamoDB.DocumentClient({
        region: 'ap-south-1',
        accessKeyId: '********************',
        secretAccessKey: 'qjD/5rIo2eGVQvZMe+Nv7uyHfzLg+Qn07/tED2TU'
    });

    const TABLE_NAME = 'ChargerLogs-r6cw5zqo7zb37hhq7w4ympiugy-prod';
    try {
        // Calculate the date 20 days ago
        const twentyDaysAgo = dayjs().subtract(1, 'day').toISOString();

        // Define the scan parameters with filter for createdAt < twentyDaysAgo
        const scanParams = {
            TableName: TABLE_NAME,
            FilterExpression: '#createdAt < :twentyDaysAgo',
            ExpressionAttributeNames: {
                '#createdAt': 'createdAt'
            },
            ExpressionAttributeValues: {
                ':twentyDaysAgo': twentyDaysAgo
            }
        };

        let itemsToDelete = [];
        let lastEvaluatedKey = null;

        // Paginated scan to get all items older than 20 days
        do {
            const data = await dynamodb.scan(scanParams).promise();
            itemsToDelete = itemsToDelete.concat(data.Items);
            lastEvaluatedKey = data.LastEvaluatedKey;
            scanParams.ExclusiveStartKey = lastEvaluatedKey;
        } while (lastEvaluatedKey);

        // Delete the old items
        for (const item of itemsToDelete) {
            const deleteParams = {
                TableName: TABLE_NAME,
                Key: {
                    id: item.id // Assuming 'id' is the primary key
                }
            };
            await dynamodb.delete(deleteParams).promise();
        }

        body = {
            message: `Deleted ${itemsToDelete.length} items older than 20 days`
        };
    } catch (error) {
        console.error('Error deleting old logs:', error);
        statusCode = 500;
        body = {
            error: error.message
        };
    }


    console.log(`Total number of active items in the table: ${itemCount}`);

    return body;
};