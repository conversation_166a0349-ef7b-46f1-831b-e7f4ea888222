

import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = process.env.API_GOCHARGE_GRAPHQLAPIENDPOINTOUTPUT;
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

export const handler = async (events) => {
  console.log(`EVENT: ${JSON.stringify(events)}`);
  const event = {
    "response": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
  const jsondata = JSON.parse(event);
  if (jsondata['response']) {
    try {
      let bufferObj = Buffer.from(jsondata['response'], "base64");
      let decodedString = bufferObj.toString("utf8");
      console.log("The decoded string:", decodedString);
      const paymentResponse = JSON.parse(decodedString);
      console.log("paymentResponse: ", paymentResponse);
    } catch (error) {
      console.log("Error Decoding: ", error);
    }
  }
};