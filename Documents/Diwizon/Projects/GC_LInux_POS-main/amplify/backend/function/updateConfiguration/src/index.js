import {
  default as fetch,
  Request,
} from "node-fetch";

import AWS from "aws-sdk";

const dynamodb = new AWS.DynamoDB.DocumentClient();
const GRAPHQL_ENDPOINT =
  "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

export const handler = async (event) => {
  console.log(`Stop trans EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body, response, data;

  let configurationKey;
  let document_Id;

  let cpId;
  const configs = [];

  try {
    cpId = event.cpId;
    configurationKey = event.configurationKey;
    console.log("cpId", cpId);
    console.log("configurationKey", configurationKey, typeof configurationKey);
    configurationKey = Object.values(configurationKey);
    console.log("configurationKey", configurationKey, typeof configurationKey);

    configurationKey.forEach(item => {
      configs.push(JSON.stringify(item));
    });
    console.log("configs", configs, typeof configs);

    let configurationItems = [];
    const params = {
      TableName: "Configuration-r6cw5zqo7zb37hhq7w4ympiugy-prod", // Specify your DynamoDB table name

      FilterExpression: "#cpId = :cpId", // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        "#cpId": "cpId",
      },
      ExpressionAttributeValues: {
        ":cpId": cpId,
      },
    };

    // Call DynamoDB scan operation to read data for the specified ID
    do {
      data = await dynamodb.scan(params).promise();
      configurationItems = configurationItems.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    // console.log(data);
    if (configurationItems.length > 0) {
      body = configurationItems[0];
      document_Id = body.id;
      // Update Configuration
      console.log("Updating Configuration...");
      const variables = {
        id: document_Id, configs: configs, cpId: cpId
      };

      const query = /* GraphQL */ `
      mutation MyMutation($id: ID!, $configs: [AWSJSON], $cpId: String) {
        updateConfiguration(
          input: {id: $id, configs: $configs, cpId: $cpId}
        ) {
          configs
          cpId
          id
        }
      }`;

      const graphQLRequest = new Request(GRAPHQL_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": GRAPHQL_API_KEY,
        },
        body: JSON.stringify({
          query: query,
          variables,
        }),
      });

      try {
        response = await fetch(graphQLRequest);
        const responseBody = await response.json();
        body = responseBody.data;
        console.log(body);
      } catch (error) {
        console.error("Error executing GraphQL request:", error);
        statusCode = 400;
        body = {
          errors: [
            {
              message: error.message,
              stack: error.stack,
            },
          ],
        };
      }

    } else {
      /// Create New Configuration 
      console.log("Creating new...");
      const variables = {
        configs: configs, cpId: cpId
      };

      const query = /* GraphQL */ `
      mutation MyMutation($configs: [AWSJSON], $cpId: String) {
        createConfiguration(
          input: {configs: $configs, cpId: $cpId}
        ) {
          configs
          cpId
          id
        }
      }`;

      const graphQLRequest = new Request(GRAPHQL_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": GRAPHQL_API_KEY,
        },
        body: JSON.stringify({
          query: query,
          variables,
        }),
      });

      try {
        response = await fetch(graphQLRequest);
        const responseBody = await response.json();
        console.log(responseBody);
        body = responseBody.data;
      } catch (error) {
        console.error("Error executing GraphQL request:", error);
        statusCode = 400;
        body = {
          errors: [
            {
              message: error.message,
              stack: error.stack,
            },
          ],
        };
      }
    }

  } catch (error) {
    // Return error response if an error occurs
    console.error("Error reading data from DynamoDB:", error);
    statusCode = 400;
    body = {
      errors: [
        {
          message: error.message,
          stack: error.stack,
        },
      ],
    };
  }

  return {
    statusCode,
    body: JSON.stringify(body),
  };
};
