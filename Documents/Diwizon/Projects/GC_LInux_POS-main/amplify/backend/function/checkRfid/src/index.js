import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

export const handler = async (event) => {
  const bodyObject = JSON.parse(event.body);
  console.log("Body", bodyObject);
  const rfid = bodyObject.rfid;

  // Define parameters for DynamoDB query operation to get the latest RFID entry
  let items = [];
  const params = {
    TableName: 'RFIDSchema-r6cw5zqo7zb37hhq7w4ympiugy-prod',
    FilterExpression: '#rfidValue = :rfidValue', // Filter expression to check if givenDate is between startDate and endDate
    ExpressionAttributeNames: {
      '#rfidValue': 'rfidValue'
    },
    ExpressionAttributeValues: {
      ':rfidValue': rfid
    }
  };

  // Call DynamoDB query operation to read data for the specified RFID

  do {
    const data = await dynamodb.scan(params).promise();
    console.log(data);
    items = items.concat(data.Items);
    params.ExclusiveStartKey = data.LastEvaluatedKey;
  } while (params.ExclusiveStartKey);

  if (items.length > 0) {
    const body = items[0];
    const userId = body.userId;
    const rfidExpires = body.expires; // Save expires to return later

    // Define parameters for retrieving user data
    const userParams = {
      TableName: 'EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod',
      KeyConditionExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id',
      },
      ExpressionAttributeValues: {
        ':id': userId
      }
    };

    // Call DynamoDB query operation to read user data
    const userData = await dynamodb.query(userParams).promise();
    console.log(userData);

    if (userData.Items.length > 0) {
      const currentUser = userData.Items[0];
      const balance = currentUser.balance;
      console.log("Balance is", balance);

      if (balance > 700) {
        // await updateUserBalance(currentUser.id, -700);
        return rfidExpires;
      } else {
        return {
          statusCode: 200,
          body: JSON.stringify(null),
        };
      }
    } else {
      return {
        statusCode: 200,
        body: JSON.stringify(null),
      };
    }
  }

  // If any condition fails, return null
  return {
    statusCode: 200,
    body: JSON.stringify(null),
  };
};
