{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.12.2\",\"stackType\":\"function-LambdaLayer\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}, "description": {"Type": "String", "Default": ""}, "runtimes": {"Type": "List<String>"}}, "Resources": {"LambdaLayerVersionb86f276f": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"CompatibleRuntimes": {"Ref": "runtimes"}, "Content": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Description": {"Ref": "description"}, "LayerName": {"Fn::Sub": ["gochargenpmLayer-${env}", {"env": {"Ref": "env"}}]}}, "DeletionPolicy": "Delete", "UpdateReplacePolicy": "<PERSON><PERSON>"}, "LambdaLayerPermissionPrivateb86f276f": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": {"Ref": "LambdaLayerVersionb86f276f"}, "Principal": {"Ref": "AWS::AccountId"}}}}, "Outputs": {"Arn": {"Value": {"Ref": "LambdaLayerVersionb86f276f"}}}}