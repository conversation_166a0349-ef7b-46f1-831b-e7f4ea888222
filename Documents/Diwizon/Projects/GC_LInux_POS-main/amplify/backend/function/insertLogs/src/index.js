import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

import AWS from 'aws-sdk';


// Create a new DynamoDB DocumentClient
// const dynamodb = new AWS.DynamoDB.DocumentClient();


/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
let statusCode = 200;
export const handler = async (event) => {
  console.log(`Read Logs EVENT: ${JSON.stringify(event)}`);

  let body,
    response,
    data,
    cpId,
    Event, Log, TimeStamp, logSequence;

  try {
    console.log(event);
    cpId = event.CP_ID;
    Event = event.Event;
    Log = event.Log;
    TimeStamp = event.TimeStamp ?? new Date();
    logSequence = event.logSequence;

    console.log("MsG DatA", event);

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from Lambda Function:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  const variables = {
    cpId: cpId, Event: Event, Log: Log, TimeStamp: TimeStamp, logSequence: logSequence
  };


  const query = /* GraphQL */ `
  mutation MyMutation ($cpId: String!, $Event: String, $Log: String, $TimeStamp: AWSDateTime, $logSequence: String) {
    createChargerLogs(input: {cpId: $cpId, Event: $Event, Log: $Log, TimeStamp: $TimeStamp, logSequence: $logSequence}) {
      updatedAt
      id
      createdAt
      TimeStamp
      Log
      Event
      cpId
      logSequence
    }
  }
  `;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    console.log("Running Mutation...")
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.error(body);
  }



  return {
    statusCode,
    body: JSON.stringify(body)
  };
};

/* SELECT topic(1) as cpId, get(*, 3).meterStop as meterStop, get(*, 3).timestamp as timestamp, get(*, 3).idTag as idTag, get(*, 3) as MSG_BODY FROM '+/in' where get(*, 2) = 'StopTransaction' */