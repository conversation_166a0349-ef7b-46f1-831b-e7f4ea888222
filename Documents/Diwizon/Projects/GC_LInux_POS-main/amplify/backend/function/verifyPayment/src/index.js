import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import razorpay from 'razorpay';
import AWS from 'aws-sdk';


const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

const dynamodb = new AWS.DynamoDB.DocumentClient();

// This razorpayInstance will be used to 
// access any resource from razorpay
const keyId = "***********************"; // "rzp_test_cn6p8HqmFvpWSW"
const keySecret = "2IsMZvfk9kPz2iB4loELYUfj"; // "kouKRBYkLFI51GTS9ntQUX8f"
const razorpayInstance = new razorpay({
  key_id: keyId,
  key_secret: keySecret
});

/* 
import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import razorpay from 'razorpay';
import AWS from 'aws-sdk';


const GRAPHQL_ENDPOINT = "";
const GRAPHQL_API_KEY = "";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

const dynamodb = new AWS.DynamoDB.DocumentClient();

// This razorpayInstance will be used to 
// access any resource from razorpay
const keyId = ""; // ""
const keySecret = ""; // ""
const razorpayInstance = new razorpay({
  key_id: keyId,
  key_secret: keySecret
});
 */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
let data, jsondata;
let bId, statusCode, body, response, orderData;
let transRef, reason, method, amount, timeStamp, uId, transDocId, payment_Id, notes;
export const handler = async (event) => {
  try {
    jsondata = event.body;
    console.log(event.body);
    console.log("jsondata['response'] ", jsondata['response']);
    if (jsondata['response']) {
      try {
        let bufferObj = Buffer.from(event.body, "base64");
        let decodedString = bufferObj.toString("utf8");
        console.log("The decoded string:", decodedString);
        const paymentResponse = JSON.parse(decodedString);
        console.log("paymentResponse: ", paymentResponse);
      } catch (error) {
        console.log("Error Decoding: ", error);
      }
    }

    data = JSON.parse(jsondata);
    console.log(data.event);
    // if (data['event'] == "order.captured") {
    try {
      console.log("In fetch");
      orderData = await razorpayInstance.orders.fetch(data['payload']['payment']['entity']['order_id']);
      if (orderData['notes']['bId']) {
        console.log("Notes added");
        notes = orderData['notes'];
      }
      console.log("orderData", orderData);
      console.log("orderData", orderData['notes'].length, orderData['notes'].length == 0);
      console.log("orderData", orderData['notes']['bId']);
    } catch (error) {
      console.log(error);
    }
    // }
    /* data['event'] == "payment.captured" */
    console.log("notes->", notes, notes ? data['event'] == "payment.captured" : data['event'] == "order.paid")
    if (notes ? data['event'] == "payment.captured" : data['event'] == "order.paid") {
      console.log("paid");
      const payload = data['payload']['payment']['entity'];
      notes = notes ?? payload['notes'];
      bId = notes['bId'];
      transRef = payload['id'];
      reason = notes['type'];
      uId = notes['uId'];
      method = payload['method'];
      amount = payload['amount'];
      payment_Id = payload['id'];
      timeStamp = payload['created_at'];
      console.log(bId);
      // ------------- //
      // Check if the transaction was added manually 

      const scannedTransItems = [];

      const transParams = {
        TableName: 'Transaction-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

        FilterExpression: '#transRef = :transRef', // Filter expression to check if givenDate is between startDate and endDate
        ExpressionAttributeNames: {
          '#transRef': 'transRef'
        },
        ExpressionAttributeValues: {
          ':transRef': transRef
        }
      };

      // Call DynamoDB get operation to read data for the specified ID

      do {
        const transData = await dynamodb.scan(transParams).promise();
        console.log("transData", transData);
        scannedTransItems = scannedTransItems.concat(transData.Items);
        transParams.ExclusiveStartKey = transData.LastEvaluatedKey;
      } while (transParams.ExclusiveStartKey);

      if (scannedTransItems.length > 0) {
        const transDataMap = scannedTransItems[0];
        if (transDataMap.transRef == transRef && transDataMap.amount == amount) {
          console.log("The amount was added manually!");
          return { "msg": "Added Manually!" };
        }
      }
      // ------------- //

      if (payload['error_code']) {
        console.error(payload);
      }
      if (reason == "Wallet"/*  && data['event'] == "payment.captured" */) {
        console.log("This is Wallet Transaction....");
        await updateUserBalance(uId, amount);
        await updateTrans(bId, payload, notes);
      } else if (reason == "Dues"/*  && data['event'] == "payment.captured" */) {
        await clearDues(bId);
      } else {
        let walletAmountUsed = 0;
        const chargingData = await getChargingData(bId);
        transDocId = chargingData.transDocId;
        console.log("B4", "transDocId", transDocId, typeof transDocId);
        if (chargingData.amountFromWallet != null) {
          console.log("WalletAmountUsed", walletAmountUsed);
          if (chargingData.amountFromWallet > 0) {
            walletAmountUsed = chargingData.amountFromWallet;
            console.log("WalletAmountUsed", walletAmountUsed);
            // if (data['event'] == "order.paid") {
            await updateUserBalance(uId, -chargingData.amountFromWallet * 100);
            // }
          }
        }

        let created_at = convertToAWSDateTime(timeStamp);

        try {
          // const invoiceNo = await getSetInvoiceNo(chargingData.station_id);
          const variables = {
            id: bId, isPaid: true, payment_Id: payment_Id, paymentTime: new Date(),
            // invoiceNo: invoiceNo
          };

          //Add Values from DB and Event here TODO by Arbaaz
          const query = /* GraphQL */ `
        mutation MyMutation($id: ID!, $isPaid: Boolean!, $payment_Id: String, $paymentTime: AWSDateTime, $invoiceNo: Int) {
          updateChargingTable(
            input: {id: $id, isPaid: $isPaid, payment_Id: $payment_Id, paymentTime: $paymentTime, invoiceNo: $invoiceNo}
          ) {
            createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
          }
        }`;

          /** @type {import('node-fetch').RequestInit} */
          const options = {
            method: 'POST',
            headers: {
              'x-api-key': GRAPHQL_API_KEY,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, variables })
          };

          const request = new Request(GRAPHQL_ENDPOINT, options);

          console.log("Running Mutation...")
          response = await fetch(request);
          console.log(response);
          body = await response.json();
          if (body.errors) statusCode = 400;
          console.log(body);
        } catch (error) {
          statusCode = 400;
          body = {
            errors: [
              {
                status: response.status,
                message: error.message,
                stack: error.stack
              }
            ]
          };
          console.error(body);
        }


        try {
          const variables = {
            id: transDocId, transRef: transRef, uId: uId, method: walletAmountUsed == 0 ? method : `Wallet + ${method}`, dateTime: created_at,
            bookingId: chargingData.booking_id, amount: parseFloat(amount.toString()) / 100, status: "Successful", walletAmountUsed: walletAmountUsed
          };
          console.log(variables);

          const query = /* GraphQL */ `
          mutation MyMutation($id: ID!, $transRef: String!, $method: String!, $dateTime: AWSDateTime!, $bookingId: String!, $amount: Float!,$walletAmountUsed: Float!, $status: String!) {
            updateTransaction(
              input: {id: $id, transRef: $transRef, method: $method, dateTime: $dateTime, bookingId: $bookingId, amount: $amount,walletAmountUsed: $walletAmountUsed, status: $status}
            ) {
              createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
            }
          }`;

          /** @type {import('node-fetch').RequestInit} */
          const transacOptions = {
            method: 'POST',
            headers: {
              'x-api-key': GRAPHQL_API_KEY,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, variables })
          };

          const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

          console.log("Running Update transaction Mutation...");
          const response2 = await fetch(transacReq);
          console.log(response2);
          const body2 = await response2.json();
          if (body2.errors) statusCode = 400;
          console.log(body2);

        } catch (error) {
          console.log("Inside Update Transaction Mutation Catch");
          statusCode = 400;
          const bodyy = {
            errors: [
              {
                status: response.status,
                message: error.message,
                stack: error.stack
              }
            ]
          };
          console.error(bodyy);
        }

      }
    }


  } catch (error) {
    console.log("Error verifying order", error);
    return {
      body: JSON.stringify(error)
    };
  }
};


async function updateTrans(tId, payload, notes) {
  try {
    console.log("In updateTrans..");
    const bId = notes['bId'];
    const transRef = payload['id'];
    const reason = notes['type'];
    const uId = notes['uId'];
    const method = payload['method'];
    const amount = payload['amount'];
    const payment_Id = payload['id'];
    const timeStamp = payload['created_at'];

    const created_at = convertToAWSDateTime(timeStamp);

    try {
      const variables = {
        id: tId, transRef: transRef, uId: uId, method: method, dateTime: created_at, bookingId: "", amount: parseFloat(amount.toString()) / 100, status: "Successful"
      };
      console.log(variables);

      const query = /* GraphQL */ `
      mutation MyMutation($id: ID!, $transRef: String!, $method: String!, $dateTime: AWSDateTime!, $bookingId: String!, $amount: Float!, $status: String!) {
        updateTransaction(
          input: {id: $id, transRef: $transRef, method: $method, dateTime: $dateTime, bookingId: $bookingId, amount: $amount, status: $status}
        ) {
          createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
        }
      }`;

      /** @type {import('node-fetch').RequestInit} */
      const transacOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

      console.log("Running Update transaction Mutation...");
      const response2 = await fetch(transacReq);
      console.log(response2);
      const body2 = await response2.json();
      if (body2.errors) statusCode = 400;
      console.log(body2);

    } catch (error) {
      console.log("Inside Update Transaction Mutation Catch");
      statusCode = 400;
      const bodyy = {
        errors: [
          {
            status: response.status,
            message: error.message,
            stack: error.stack
          }
        ]
      };
      console.error(bodyy);
    }

  } catch (error) {
    console.log(error);
  }
}


async function getChargingData(bId) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let chargingTableItems = [];
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#bId = :booking_id', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#bId': 'id'
      },
      ExpressionAttributeValues: {
        ':booking_id': bId
      }
    };

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      chargingTableItems = chargingTableItems.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (chargingTableItems.length > 0) {
      body = chargingTableItems[0];
      return body;
    }

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}

async function updateUserBalance(uId, addon) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: "EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': uId
      }
    };

    console.log("USER ID is", uId);

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      console.log("OLD Balance is ", body.balance);
      const newBalance = body.balance + (addon / 100);
      try {
        const variables = { id: body.id, newBalance: newBalance };
        console.log(variables);

        const query = /* GraphQL */ `
      mutation MyMutation($id: ID!,$newBalance: Float!) {
        updateEndUser(
          input: {id: $id,balance: $newBalance}
        ) {
          id
          user_fullname
          dob
          joining_date
          email
          contact
          balance
          default_vehicle_id
          favs
          uId
          deviceId
          createdAt
          updatedAt
        }
      }`;

        /** @type {import('node-fetch').RequestInit} */
        const transacOptions = {
          method: 'POST',
          headers: {
            'x-api-key': GRAPHQL_API_KEY,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ query, variables })
        };

        const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

        console.log("Running Upate end user Mutation...");
        const response2 = await fetch(transacReq);
        console.log(response2);
        const body2 = await response2.json();
        if (body2.errors) statusCode = 400;
        console.log(body2);
      } catch (error) {
        console.log("Inside Create Transaction Mutation Catch");
        statusCode = 400;
        const bodyy = {
          errors: [
            {
              status: response.status,
              message: error.message,
              stack: error.stack
            }
          ]
        };
        console.error(bodyy);
      }
    }



  } catch (error) {
    // Return error response if an error occurs
    console.error('Error in updateUserBalance():', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}


function convertToAWSDateTime(timestamp) {
  const date = new Date(timestamp * 1000); // Multiply by 1000 to convert seconds to milliseconds
  const awsDateTime = date.toISOString().split('.')[0] + 'Z'; // Remove milliseconds and add 'Z' to indicate UTC time
  return awsDateTime;
}


function checkCurrentFinYearDate(currentFinYear) {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  // Determine the financial year start date
  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  // Convert currentFinYear to a Date object if it's not already one
  const currentFinYearDate = new Date(currentFinYear);

  // Check if currentFinYearDate is the same as finYearStart
  const finDateUpdated = currentFinYearDate.getTime() === finYearStart.getTime();

  return finDateUpdated;
}


function getCurrentFinancialYearStartDate() {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  return finYearStart;
}

async function clearDues(uId) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: "DueRequest-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': uId
      }
    };

    console.log("Dues ID is", uId);

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      for (const item of items[0].dues) {
        try {
          const variables = { id: item, overchargeDueCleared: true };
          console.log(variables);

          const query = /* GraphQL */ `
        mutation MyMutation($id: ID!, $overchargeDueCleared: Boolean!) {
          updateChargingTable(
            input: {id: $id, overchargeDueCleared: $overchargeDueCleared}
          ) {
            createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
          }
        }`;

          /** @type {import('node-fetch').RequestInit} */
          const transacOptions = {
            method: 'POST',
            headers: {
              'x-api-key': GRAPHQL_API_KEY,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, variables })
          };

          const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

          console.log("Running Upate end user Mutation...");
          const response2 = await fetch(transacReq);
          console.log(response2);
          const body2 = await response2.json();
          if (body2.errors) statusCode = 400;
          console.log(body2);
        } catch (error) {
          console.log("Inside Create Transaction Mutation Catch");
          statusCode = 400;
          console.error(error);
        }
      }
    }



  } catch (error) {
    // Return error response if an error occurs
    console.error('Error in:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}
