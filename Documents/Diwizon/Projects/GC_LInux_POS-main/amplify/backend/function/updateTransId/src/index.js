import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';


const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();


/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENTTTTTT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let MeterValue;
  let document_Id;
  let transaction_Id;
  let timeStamp;
  let booking_id;
  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    const bodyObject = JSON.parse(event.body);
    console.log("Body", bodyObject);
    transaction_Id = bodyObject.transId;
    booking_id = bodyObject.bookingId;
    MeterValue = bodyObject.meterStart;
    timeStamp = bodyObject.timeStamp;
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#booking_id = :booking_id', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#booking_id': 'booking_id',
      },
      ExpressionAttributeValues: {
        ':booking_id': booking_id
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    do {
      data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      document_Id = body.id;

      console.log("TRANS....", transaction_Id);
      const variables = {
        id: document_Id, transactionId: transaction_Id, startedAtTime: timeStamp
      };

      //Add Values from DB and Event here TODO by Arbaaz
      const query = /* GraphQL */ `
      mutation MyMutation($id: ID!, $transactionId: Int!, $startedAtTime: AWSDateTime!) {
        updateChargingTable(
          input: {id: $id, transactionId: $transactionId, startedAtTime: $startedAtTime}
        ) {
          createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
        }
      }`;

      /** @type {import('node-fetch').RequestInit} */
      const options = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const request = new Request(GRAPHQL_ENDPOINT, options);

      try {
        console.log("Running Mutation...");
        response = await fetch(request);
        console.log(response);
        body = await response.json();
        if (body.errors) statusCode = 400;
        console.log(body);
      } catch (error) {
        statusCode = 400;
        body = {
          errors: [
            {
              status: response.status,
              message: error.message,
              stack: error.stack
            }
          ]
        };
        console.error(body);
      }

      return {
        statusCode,
        body: JSON.stringify(body)
      };
    } else {
      await createChargingWithRfid(transaction_Id, booking_id, timeStamp, bodyObject.charge_point_id, bodyObject.connectorId, MeterValue);
      return {};
    }


  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
};


async function createChargingWithRfid(transaction_Id, rfid, timeStamp, chargerId, connectorId, MeterValue) {
  const stopTransacJson = {
    "connectorId": connectorId,
    "idTag": rfid,
    "transactionId": transaction_Id,
    "action": "RemoteStopTransaction",
    "chargerId": chargerId
  }
  try {
    console.log("MeterStartWatt ", MeterValue);
    console.log("transaction_Id ", transaction_Id);
    console.log("rfid ", rfid);
    console.log("timeStamp ", timeStamp);
    let userId, amountFromWallet, booking_id, charging_fee, connector_no, vehical_number, vehicle_id, userName, userContact,
      tax_amount, taxPercent, isIgst, start_time, end_time, invoiceNo, stationName, stationId, paymentTime, payment_Id, pricePerKw,
      city, estimatedUnits, estimatedDuration, cpoName, rfidExpires, transDocId;
    let currentUser, charger, station, cpo, cpoTax, default_vehicle_id, userVehicle, chargingAmount, connectorCount, capacity;
    booking_id = generateRandomId();
    console.log("Booking Id: ", booking_id);
    connector_no = connectorId;



    // Get RFID Data
    let rfidItems = [];
    const params = {
      TableName: 'RFIDSchema-r6cw5zqo7zb37hhq7w4ympiugy-prod',
      FilterExpression: '#rfidValue = :rfidValue', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#rfidValue': 'rfidValue'
      },
      ExpressionAttributeValues: {
        ':rfidValue': rfid
      }
    };

    // Call DynamoDB query operation to read data for the specified RFID

    do {
      const rfidData = await dynamodb.scan(params).promise();
      console.log(rfidData);
      rfidItems = rfidItems.concat(rfidData.Items);
      params.ExclusiveStartKey = rfidData.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);


    if (rfidItems.length > 0) {
      const rfidBody = rfidItems[0];

      // Get User Data
      userId = rfidBody.userId;
      rfidExpires = rfidBody.expires; // Save expires to return later

      // Define parameters for retrieving user data
      const userParams = {
        TableName: 'EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod',
        KeyConditionExpression: '#id = :id',
        ExpressionAttributeNames: {
          '#id': 'id',
        },
        ExpressionAttributeValues: {
          ':id': userId
        }
      };

      // Call DynamoDB query operation to read user data
      const userData = await dynamodb.query(userParams).promise();
      console.log(userData);
      if (userData.Items.length > 0) {
        currentUser = userData.Items[0];
        userName = currentUser.user_fullname;
        userContact = currentUser.contact;
        default_vehicle_id = currentUser.default_vehicle_id;
        const balance = currentUser.balance;
        console.log("Balance is ", balance);


        // await updateUserBalance(currentUser.id, -700);

        // Get Charger Details
        let chargerItems = [];
        const chargerParams = {
          TableName: 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name
          FilterExpression: '#chargingPointId = :chargingPointId',
          ExpressionAttributeNames: {
            '#chargingPointId': 'chargingPointId',
          },
          ExpressionAttributeValues: {
            ':chargingPointId': chargerId,
          }
        };

        // Call DynamoDB scan operation to read data for the specified ID

        do {
          let chargerData = await dynamodb.scan(chargerParams).promise();
          chargerItems = chargerItems.concat(chargerData.Items);
          chargerParams.ExclusiveStartKey = chargerData.LastEvaluatedKey;
        } while (chargerParams.ExclusiveStartKey);

        if (chargerItems.length > 0) {
          console.log("Found chargerData");
          charger = chargerItems[0];
          pricePerKw = charger.pricePerKW;
          // Get Station Data
          let stationItems = [];
          const stationParams = {
            TableName: "Station-r6cw5zqo7zb37hhq7w4ympiugy-prod",
            FilterExpression: '#id = :id',
            ExpressionAttributeNames: {
              '#id': 'id'
            },
            ExpressionAttributeValues: {
              ':id': charger.stationId
            }
          };

          // Call DynamoDB get operation to read data for the specified ID
          do {
            const stationData = await dynamodb.scan(stationParams).promise();
            stationItems = stationItems.concat(stationData.Items);
            stationParams.ExclusiveStartKey = stationData.LastEvaluatedKey;
          } while (stationParams.ExclusiveStartKey);


          if (stationItems.length > 0) {
            console.log("Found stationData");
            station = stationItems[0];
            stationId = station.id;
            stationName = station.station_name;
            isIgst = station.igst;
            city = station.city;

            // Get Connectors No 
            // Get Charger Details
            let connectorItems = [];
            const connectorParams = {
              TableName: 'Connector-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name
              FilterExpression: '#chargerId = :chargerId',
              ExpressionAttributeNames: {
                '#chargerId': 'chargerId',
              },
              ExpressionAttributeValues: {
                ':chargerId': charger.id,
              }
            };

            // Call DynamoDB scan operation to read data for the specified ID
            do {
              let connectorData = await dynamodb.scan(connectorParams).promise();
              connectorItems = connectorItems.concat(connectorData.Items);
              connectorParams.ExclusiveStartKey = connectorData.LastEvaluatedKey;
            } while (connectorParams.ExclusiveStartKey);

            if (connectorItems.length > 0) {
              console.log("Found connectorData");
              connectorCount = connectorItems.length;
              // Get CPO
              let cpoItems = [];
              const cpoParams = {
                TableName: "CPO-r6cw5zqo7zb37hhq7w4ympiugy-prod",
                FilterExpression: '#id = :id',
                ExpressionAttributeNames: {
                  '#id': 'id'
                },
                ExpressionAttributeValues: {
                  ':id': station.cpoId
                }
              };


              do {
                const cpoData = await dynamodb.scan(cpoParams).promise();
                cpoItems = cpoItems.concat(cpoData.Items);
                cpoParams.ExclusiveStartKey = cpoData.LastEvaluatedKey;
              } while (cpoParams.ExclusiveStartKey);

              if (cpoItems.length > 0) {
                console.log("Found cpoData");
                cpo = cpoItems[0];
                cpoName = cpo.name;
                console.log(cpo);

                // Get Tax
                let taxesItems = [];
                const taxParams = {
                  TableName: "Taxes-r6cw5zqo7zb37hhq7w4ympiugy-prod",
                  FilterExpression: '#id = :id',
                  ExpressionAttributeNames: {
                    '#id': 'id'
                  },
                  ExpressionAttributeValues: {
                    ':id': cpo.taxId
                  }
                };


                do {
                  const taxData = await dynamodb.scan(taxParams).promise();
                  taxesItems = taxesItems.concat(taxData.Items);
                  taxParams.ExclusiveStartKey = taxData.LastEvaluatedKey;
                } while (taxParams.ExclusiveStartKey);

                if (taxesItems.length > 0) {
                  console.log("Found taxData");
                  cpoTax = taxesItems[0];
                  taxPercent = cpoTax.rate;

                  // Get Charger Capacity 
                  let chargerCapacityItems = [];
                  const capacityParams = {
                    TableName: "ChargerCapacity-r6cw5zqo7zb37hhq7w4ympiugy-prod",
                    FilterExpression: '#id = :id',
                    ExpressionAttributeNames: {
                      '#id': 'id'
                    },
                    ExpressionAttributeValues: {
                      ':id': charger.capacityId
                    }
                  };


                  do {
                    const capacityData = await dynamodb.scan(capacityParams).promise();
                    chargerCapacityItems = chargerCapacityItems.concat(capacityData.Items);
                    capacityParams.ExclusiveStartKey = capacityData.LastEvaluatedKey;
                  } while (capacityParams.ExclusiveStartKey);

                  if (chargerCapacityItems.length > 0) {
                    console.log("Found capacityData");
                    capacity = chargerCapacityItems[0];

                    // Get Default Vehicle
                    let userVehicleItems = [];
                    const vehicleParams = {
                      TableName: "UserVehicle-r6cw5zqo7zb37hhq7w4ympiugy-prod",
                      FilterExpression: '#id = :id',
                      ExpressionAttributeNames: {
                        '#id': 'id'
                      },
                      ExpressionAttributeValues: {
                        ':id': default_vehicle_id
                      }
                    };
                    let vehicleData;
                    if (default_vehicle_id) {
                      do {
                        vehicleData = await dynamodb.scan(vehicleParams).promise();
                        userVehicleItems = userVehicleItems.concat(vehicleData.Items);
                        vehicleParams.ExclusiveStartKey = vehicleData.LastEvaluatedKey;
                      } while (vehicleParams.ExclusiveStartKey);
                    } else {
                      vehicleData = null;
                    }

                    if (vehicleData == null ? false : userVehicleItems.length > 0) {
                      console.log("Found vehicleData");
                      // Calculate Amount to Charge
                      userVehicle = userVehicleItems[0];
                      const batteryCapacity = userVehicle.batteryCapacity;
                      if (!batteryCapacity) chargingAmount = 700;
                      chargingAmount = 700;
                    } else {
                      // No vehicle linked so take ₹700
                      chargingAmount = 700;
                    }

                    // Check if has enough balance & Proceed
                    if (balance >= chargingAmount) {
                      //-------------------------------------------------//
                      //--> Process Wallet Deduction & Create Booking <--//  
                      //-------------------------------------------------//
                      amountFromWallet = chargingAmount;
                      const chargerCapacity = capacity.charger_capacity;
                      const capacityPerMin = (chargerCapacity / connectorCount) / 60;
                      estimatedUnits = chargingAmount / (charger.pricePerKW);
                      estimatedDuration = estimatedUnits / capacityPerMin;
                      tax_amount = (cpoTax.rate) / 100 * chargingAmount;
                      // Get Invoice  No..
                      // invoiceNo = await getSetInvoiceNo(cpo);
                      // console.log("invoice No", invoiceNo);

                      // Define the GraphQL query for createChargingTable
                      const chargeVariables = {
                        // id: uuidv4(),
                        transactionId: transaction_Id,  // Ensure this is an integer
                        MeterStartWatt: MeterValue,
                        amountFromWallet: parseFloat(amountFromWallet),
                        booking_id: booking_id || null, // Optional field; include null if no value
                        rfid: rfid,
                        booking_type: "Amount",
                        chargePointId: chargerId,
                        chargerId: charger.id,
                        charging_fee: chargingAmount,
                        city: city,
                        connector_no: parseInt(connectorId), // Ensure this is an integer
                        vehical_number: vehical_number,
                        vehicle_id: vehicle_id,
                        user_id: userId,
                        userName: userName,
                        userContact: userContact,
                        tax_amount: parseFloat(tax_amount),
                        taxPercent: parseFloat(taxPercent),
                        status: "Active",
                        transDocId: transDocId,
                        station_id: stationId,
                        stationName: stationName,
                        start_time: new Date(), // Must be in ISO 8601 format if provided
                        end_time: addMinutes(new Date(), estimatedDuration),
                        pricePerKw: parseFloat(pricePerKw),
                        payment_Id: payment_Id,
                        paymentTime: new Date(), // Ensure ISO 8601 format
                        isPaid: true,
                        isIgst: isIgst,
                        // invoiceNo: parseInt(invoiceNo), // Ensure this is an integer
                        gstName: "",
                        geoState: station.geoState,
                        estimatedUnits: parseFloat(estimatedUnits),
                        estimatedDuration: parseFloat(estimatedDuration),
                        // createdAt: timeStamp, // ISO 8601 format
                        startedAtTime: new Date(), // ISO 8601 format
                        cpoName: cpoName
                      };

                      // Define the GraphQL query for createChargingTable

                      try {

                        const balanceData = await updateUserBalance(currentUser.id, -chargingAmount);
                        console.log(balanceData);
                        if (balanceData == true) {
                          let transResp = await createUserRfidTransaction(currentUser.id, booking_id, amountFromWallet, currentUser.user_fullname, currentUser.contact, currentUser.balance);
                          console.log(transResp);
                          let chargeResp = await createChargingTable(chargeVariables);
                          console.log(chargeResp);
                        } else {
                          console.error("Error during deduction"); remoteStopTransac(stopTransacJson);
                        }
                        return {
                          statusCode: 200,
                          body: JSON.stringify({ "success": true }),
                        };
                      } catch (error) {
                        console.error("Error during mutation", error); remoteStopTransac(stopTransacJson);
                      }

                    } else {
                      // Insufficient balance!
                      console.log("Insufficient balance!"); remoteStopTransac(stopTransacJson);
                      return;
                    }
                  } else {
                    // capacity Not Found!
                    console.log("capacity Not Found!"); remoteStopTransac(stopTransacJson);
                    return;
                  }
                } else {
                  // cpo Tax Not Found!
                  console.log("Cpo Tax Not Found!"); remoteStopTransac(stopTransacJson);
                  return;
                }
              } else {
                // cpo Not Found!
                console.log("Cpo Not Found!"); remoteStopTransac(stopTransacJson);
                return;
              }
            } else {
              // Connectors Not Found!
              console.log("Connectors Not Found! for ", chargerId, "-", charger.id); remoteStopTransac(stopTransacJson);
              return;
            }
          } else {
            // Charger Not Found!
            console.log("Station Not Found!"); remoteStopTransac(stopTransacJson);
            return;
          }
        } else {
          // Charger Not Found!
          console.log("Charger Not Found!"); remoteStopTransac(stopTransacJson);
          return;
        }
      } else {
        // No User found!
        console.log("No User found!"); remoteStopTransac(stopTransacJson);
        return;
      }
    } else {
      // No Rfid found!
      console.log("No Rfid found!"); remoteStopTransac(stopTransacJson);
      return;
    }
  } catch (error) {
    console.log('Error in createChargingWithRfid', error);
    await remoteStopTransac(stopTransacJson);
    return error;
  }
}



// const chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
function generateRandomId(length = 12) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars.charAt(Math.floor(Math.random() * chars.length)))
    .join('')
    .toUpperCase();
}




async function createChargingTable(data) {
  try {
    console.log(data);

    // Define the GraphQL query using a variable for the input
    const query = /* GraphQL */ `
      mutation MyMutation($input: CreateChargingTableInput!) {
        createChargingTable(input: $input) {
          id
          booking_id
          start_time
          end_time
          status
          connector_no
          CurrentMeterWatt
          city
          charging_fee
          payment_status
          createdAt
          tax_amount
          vehical_number
          chargePointId
          user_id
          station_id
          vehicle_id
          charging_percent
          MeterStartWatt
          MeterEndWatt
          booking_type
          compareValue
          pricePerKw
          geoState
          isPaid
          chargerId
          transactionId
          estimatedDuration
          estimatedUnits
          startedAtPercent
          stopedAtPercent
          unitsBurned
          costOfConsump
          refundedAmount
          startedAtTime
          stopedAtTime
          amountFromWallet
          transDocId
          payment_Id
          paymentTime
          lastCommand
          gstin
          gstName
          userName
          userContact
          overchargeDueCleared
          invoiceNo
          dueAmount
          stationName
          cpoName
          taxPercent
          isIgst
          rfid
          baseAmount
        }
      }
    `;

    // Define the request options, including variables with the `data` object passed as `input`
    const options = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables: { input: data }, // Pass `data` as the `input` variable here
      }),
    };

    const request = new Request(GRAPHQL_ENDPOINT, options);

    console.log("Running Mutation...");
    const response = await fetch(request);
    const responseBody = await response.json();
    console.log(responseBody);

    if (!responseBody.errors) {
      // Mutation successful, return response
      return {
        statusCode: 200,
        body: JSON.stringify(responseBody),
      };
    }

    return {
      statusCode: 400,
      body: JSON.stringify(responseBody.errors),
    };

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error creating charging:', error);
    return error;
  }
}

async function updateUserBalance(uId, addon) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let userBody;
    let endUserItems = [];
    const params = {
      TableName: "EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': uId
      }
    };

    console.log("USER ID is", uId);

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      endUserItems = endUserItems.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (endUserItems.length > 0) {
      userBody = endUserItems[0];
      console.log("OLD Balance is ", userBody.balance);
      const newBalance = userBody.balance + addon;
      try {
        const variables = { id: userBody.id, newBalance: newBalance };
        console.log(variables);

        const query = /* GraphQL */ `
      mutation MyMutation($id: ID!,$newBalance: Float!) {
        updateEndUser(
          input: {id: $id,balance: $newBalance}
        ) {
          id
          user_fullname
          dob
          joining_date
          email
          contact
          balance
          default_vehicle_id
          favs
          uId
          deviceId
          createdAt
          updatedAt
        }
      }`;

        /** @type {import('node-fetch').RequestInit} */
        const transacOptions = {
          method: 'POST',
          headers: {
            'x-api-key': GRAPHQL_API_KEY,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ query, variables })
        };

        const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

        console.log("Running Update end user Mutation...");
        const response2 = await fetch(transacReq);
        console.log(response2);
        const body2 = await response2.json();
        console.log(body2);
        if (body2.errors) {
          statusCode = 400;
          return false;
        } else {
          return true;
        }
      } catch (error) {
        console.log(error);
        return false;
      }
    }
  } catch (error) {
    console.log(error);
    return false;
  }
}

async function createUserRfidTransaction(userId, booking_id, amount, uName, contact, userBalance) {
  let statusCode = 200;
  let response;
  try {
    let variables = {
      uId: userId, method: "Wallet-RFID", dateTime: new Date(), bookingId: booking_id, currentBalance: userBalance,
      status: "Successful", reason: "Booking", userName: uName, userContact: contact,
      amount: amount
    };

    console.log("In create transaction....", userId, variables);

    const query = /* GraphQL */ `
      mutation MyMutation($uId: String!,$method: String, $dateTime: AWSDateTime, $bookingId: String, $status: String, $reason: String, $userName: String, $userContact: String, $amount: Float, $currentBalance: Float) {
        createTransaction(
          input: {uId: $uId, method: $method, dateTime: $dateTime, bookingId: $bookingId, status: $status, reason :$reason , userName :$userName, userContact :$userContact, amount :$amount , currentBalance :$currentBalance }
        ) {
          createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
        }
      }
      `;

    /** @type {import('node-fetch').RequestInit} */
    const transacOptions = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables })
    };

    const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

    console.log("Running create transaction Mutation...");
    response = await fetch(transacReq);
    console.log(response);
    const body = await response.json();
    // if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    console.log("Inside create Transaction Mutation Catch");
    console.error(error);
  }
}

function addMinutes(date, minutes) {
  return new Date(date.getTime() + minutes * 60000);
}




async function remoteStopTransac(jsonString) {
  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    console.log("In remoteStopTransac.......");
    const raw = JSON.stringify(jsonString);
    console.log("Body of req", raw);

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      // redirect: "follow"
    };

    const response = await fetch("https://5jjulse62zjpkrjqlkicpjelcm0uzysx.lambda-url.ap-south-1.on.aws/", requestOptions)
    // fetch("https://5usyuo2wyafeodfpsphzk65gfu0adzcl.lambda-url.ap-south-1.on.aws/", requestOptions)
    console.log("Response", response);
  } catch (error) {
    console.error("Error in stop trans", error);
  }
}