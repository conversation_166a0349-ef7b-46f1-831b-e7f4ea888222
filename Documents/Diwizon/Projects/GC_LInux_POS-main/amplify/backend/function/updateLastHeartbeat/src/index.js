import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let charger_document_Id;
  let stationId;
  const { CP_ID, last_heart_beat } = event;

  try {
    console.log(`Charger ID: ${CP_ID}`);
    let items = [];
    const chargerParams = {
      TableName: 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name
      FilterExpression: '#chargingPointId = :chargingPointId',
      ExpressionAttributeNames: {
        '#chargingPointId': 'chargingPointId',
      },
      ExpressionAttributeValues: {
        ':chargingPointId': CP_ID,
      }
    };

    // Call DynamoDB scan operation to read data for the specified ID

    do {
      data = await dynamodb.scan(chargerParams).promise();
      items = items.concat(data.Items);
      chargerParams.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (chargerParams.ExclusiveStartKey);

    if (items.length > 0) {
      console.log(data);
      body = items[0];
      charger_document_Id = body.id;
      stationId = body.stationId; // Extract stationId from Charger data
    }
    console.log(`Database Data returned: ${body}`);
  } catch (error) {
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  // Convert Unix timestamp to ISO 8601 format
  const isoTime = new Date(last_heart_beat).toISOString();

  const variables = {
    id: charger_document_Id,
    last_heart_beat: isoTime, // Use converted ISO time
    stationId: stationId, // Add stationId for the station update
  };

  const query = /* GraphQL */`
    mutation UpdateChargerAndStation($id: ID!, $last_heart_beat: AWSDateTime!, $stationId: ID!) {
      updateCharger(input: {
        id: $id,
        last_heart_beat: $last_heart_beat,
      }) {
        id
              chargingPointId
              last_heart_beat
              service_date
              status
              pricePerKW
              manufactId
              capacityId
              stationId
              stationName
              simNo
              simCompany
              serialNo
              faulted
              invoicePrefix
              invoiceNo
              currentFinYear
              createdAt
              updatedAt
      }
      updateStation(input: {
        id: $stationId,
        last_heart_beat: $last_heart_beat,
      }) {
        id
        station_name
        latitude
        longitude
        landmark
        pincode
        contact_no
        email
        geoState
        city
        active
        cpoId
        address
        igst
        status
        last_heart_beat
        createdAt
        updatedAt
      }
    }
  `;

  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    console.log("IN TRY");
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    console.log(body);
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.log(`MUTATION ERROR: ${body} and ${error}`)
  }

  console.log("EXIT............");
  return {
    statusCode,
    body: JSON.stringify(body)
  };
};
