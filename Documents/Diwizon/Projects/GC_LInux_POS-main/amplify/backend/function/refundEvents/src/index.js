import crypto from '@aws-crypto/sha256-js';
import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';


const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

const dynamodb = new AWS.DynamoDB.DocumentClient();


/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);
  console.log(`Body: ${JSON.stringify(event.body)}`);
  let dataA = JSON.parse(event.body);
  // PaymentData



  console.log("In decoder...");
  let bufferObj = Buffer.from(dataA.response, "base64");
  let decodedString = bufferObj.toString("utf8");
  console.log("The decoded string:", decodedString);
  let paymentResponse = JSON.parse(decodedString);
  console.log("paymentResponse: ", paymentResponse);


  let items = [];
  const refundRecordParams = {
    TableName: 'RefundRecord-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

    FilterExpression: '#transactionId = :transactionId', // Filter expression to check if givenDate is between startDate and endDate
    ExpressionAttributeNames: {
      '#transactionId': 'transactionId'
    },
    ExpressionAttributeValues: {
      ':transactionId': paymentResponse['data']['transactionId']
    }
  }

  // Call DynamoDB get operation to read data for the specified ID

  do {
    const refundRecordData = await dynamodb.scan(refundRecordParams).promise();
    console.log("refundRecordData", refundRecordData);
    items = items.concat(refundRecordData.Items);
    refundRecordParams.ExclusiveStartKey = refundRecordData.LastEvaluatedKey;
  } while (refundRecordParams.ExclusiveStartKey);


  if (items.length > 0) {
    const refundRecordDataMap = items[0];

    if (paymentResponse['code'] === 'PAYMENT_SUCCESS') {
      const variables = {
        id: refundRecordDataMap.id,
        refunded: true,
        merchantId: paymentResponse['data']['merchantId'],
        merchantTransactionId: paymentResponse['data']['merchantTransactionId'],
        transactionId: paymentResponse['data']['transactionId'],
        amount: paymentResponse['data']['amount'] / 100,
        state: paymentResponse['data']['state'],
        responseCode: paymentResponse['data']['responseCode'],
        paymentInstrument: JSON.stringify(paymentResponse['data']['paymentInstrument']),
        code: paymentResponse['code'],
        message: paymentResponse['message'],
      };

      console.log("In update refund....", variables);

      const query = /* GraphQL */ `
          mutation MyMutation(
            $id: ID!,
            $refunded: Boolean,
            $merchantId: String,
            $merchantTransactionId: String,
            $transactionId: String,
            $amount: String,
            $state: String,
            $responseCode: String,
            $paymentInstrument: AWSJSON,
            $code: String,
            $message: String) {
            updateRefundRecord(
              input: { 
                id: $id,
                refunded:$refunded,
                merchantId:$merchantId,
                merchantTransactionId:$merchantTransactionId,
                transactionId:$transactionId,
                amount:$amount,
                state:$state,
                responseCode:$responseCode,
                paymentInstrument:$paymentInstrument,
                code:$code,
                message:$message,
              }
            ) {
              createdAt
              updatedAt
              id
              refunded
              merchantId
              merchantTransactionId
              transactionId
              amount
              state
              responseCode
              paymentInstrument
              code
              message
            }
          }
          `;

      /** @type {import('node-fetch').RequestInit} */
      const transacOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const refReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

      console.log("Running update refund record Mutation...");
      const response = await fetch(refReq);
      console.log(response);
      const body = await response.json();
      // if (body.errors) statusCode = 400;
      console.log(body);

    } else {
      // IN REFUND FAILED
      const variables = {
        id: refundRecordDataMap.id,
        code: paymentResponse['code'],
        message: paymentResponse['message']
      };

      console.log("In update refund....", variables);

      const query = /* GraphQL */ `
          mutation MyMutation(
            $id: ID!,
            $code: String,
            $message: String) {
            updateRefundRecord(
              input: { 
                id: $id,
                code:$code,
                message:$message,
              }
            ) {
              createdAt
              updatedAt
              id
              refunded
              merchantId
              merchantTransactionId
              transactionId
              amount
              state
              responseCode
              paymentInstrument
              code
              message
            }
          }
          `;

      /** @type {import('node-fetch').RequestInit} */
      const transacOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const refReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

      console.log("Running update refund record failed Mutation...");
      const response = await fetch(refReq);
      console.log(response);
      const body = await response.json();
      // if (body.errors) statusCode = 400;
      console.log(body);
    }

  } else {
    console.log("No Record Found");
  }
  return null;
};