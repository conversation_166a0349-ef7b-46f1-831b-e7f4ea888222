
const AWS = require('aws-sdk');

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);

    return await getChargerCount();
};



const getChargerCount = async () => {
    // Initialize the DynamoDB Document Client
    const dynamodb = new AWS.DynamoDB.DocumentClient({
        region: 'ap-south-1',
        accessKeyId: '********************',
        secretAccessKey: 'qjD/5rIo2eGVQvZMe+Nv7uyHfzLg+Qn07/tED2TU'
    });

    const tableName = 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod';
    const filterExpression = '#isActive = :isActive';
    const expressionAttributeNames = {
        '#isActive': 'isActive'
    };
    const expressionAttributeValues = {
        ':isActive': true // Change to false if needed
    };

    let itemCount = 0;
    let lastEvaluatedKey = null;

    do {
        const params = {
            TableName: tableName,
            FilterExpression: filterExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ExclusiveStartKey: lastEvaluatedKey
        };

        try {
            const response = await dynamodb.scan(params).promise();
            itemCount += response.Count;
            lastEvaluatedKey = response.LastEvaluatedKey;
        } catch (error) {
            console.error('Error scanning DynamoDB table:', error);
            return;
        }
    } while (lastEvaluatedKey);

    console.log(`Total number of active items in the table: ${itemCount}`);

    return itemCount;
};