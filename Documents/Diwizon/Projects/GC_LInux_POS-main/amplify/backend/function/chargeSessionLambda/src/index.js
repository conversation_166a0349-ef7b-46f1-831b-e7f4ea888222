import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

const AWS = require('aws-sdk');

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

// const query = /* GraphQL */ `
//   mutation CREATE_TODO($input: CreateTodoInput!) {
//     createTodo(input: $input) {
//       id
//       name
//       createdAt
//     }
//   }
// `;

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let MeterValue;
  let document_Id;
  try {
    // Extract the ID from the event object
    let { CP_ID, meterTime } = event; //Todo by Arbaaz
    MeterValue = event.MeterValue;
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#startDate <= :meterTime AND #endDate >= :meterTime AND #chargePointId = :chargePointId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#startDate': 'start_time', // Specify the attribute name for startDate
        '#endDate': 'end_time', // Specify the attribute name for endDate
        '#chargePointId': 'chargePointId'
      },
      ExpressionAttributeValues: {
        ':meterTime': meterTime, // Specify the givenDate value
        ':chargePointId': CP_ID
      }
    };

    // Call DynamoDB get operation to read data for the specified ID

    do {
      data = await dynamodb.scan(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    // Return the retrieved data
    /*return {
        statusCode: 200,
        body: JSON.stringify(data.Item)
    };*/
    // body = data.Item;
    // document_Id = body.id;

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }


  const variables = {
    input: {
      name: 'Hello, Todo!'
    }
  };


  //Add Values from DB and Event here TODO by Arbaaz
  const query = /* GraphQL */ `
  mutation MyMutation {
    updateChargingTable(input: {id: ${document_Id}, current_meter_value: ${MeterValue}}) { 
      createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
    }
  }
  

  `;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);



  try {
    response = await fetch(request);
    body = await response.json();
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};