import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";

import AWS from 'aws-sdk';

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let document_Id;
  let percentValue;
  let MeterValue;
  let booking_type;
  //
  let estimatedUnits;
  let charging_fee;
  //
  let timeStamp;
  let MeterStart;
  let consumedWatt;
  let totalAmount;
  let pricePerKw;
  let connectorId;
  let socValue;
  let energyImportValue;
  let chargerId;
  let booking_id;
  let startedAtTime;
  let estimatedDuration;
  let transId;
  let timeStampOfMeterValue;

  try {
    // Extract the ID from the event object
    // let { CP_ID, MSG_BODY } = event;
    console.log("EvenT", event);
    // console.log(event2.body);

    // const eventBody = event2.body;
    // const event = JSON.parse(eventBody);

    // let { CP_ID, MSG_BODY } = event; //Todo by Arbaaz
    //MeterValue = event.MeterValue;
    // let jsonMessage = MSG_BODY;

    const jsonData = event;
    let jsonMessage = event.MSG_BODY;
    // Define parameters for DynamoDB get operation


    console.log("TRANSISkkdmcds ....", jsonData.MSG_BODY[3].transactionId);
    transId = jsonData.MSG_BODY[3].transactionId;
    // Loop through meterValue entries
    for (const meterValue of jsonData.MSG_BODY[3].meterValue) {
      // Loop through sampledValue entries within each meterValue
      for (const sampledValue of meterValue.sampledValue) {
        if (sampledValue.measurand === "SoC") {
          // if (sampledValue.measurand === "SoC" && sampledValue.location === "EV" && sampledValue.context === "Sample.Periodic") {
          socValue = sampledValue.value;
        } else if (sampledValue.measurand === "Energy.Active.Import.Register") {
          // } else if (sampledValue.measurand === "Energy.Active.Import.Register" && sampledValue.location === "Outlet") {
          energyImportValue = sampledValue.unit === "Wh" ? sampledValue.value : sampledValue.value * 1000;
          timeStampOfMeterValue = meterValue.timestamp;
        }
      }
    }

    console.log("TimeStampOfMeterValue:", timeStampOfMeterValue, typeof timeStampOfMeterValue);
    console.log("SoC value:", socValue);
    console.log("Energy.Active.Import.Register value:", energyImportValue)
    /* 
        // Iterate through meterValue objects
        jsonMessage.meterValue.forEach(meter => {
          console.log("meter", meter);
          // Iterate through sampledValue objects
          meter.sampledValue.forEach(sampled => {
            console.log("sampled", sampled);
            // Check if context is "Sample.Periodic" and unit is "Percent"
            if (sampled.context === "Sample.Periodic" && sampled.unit === "Percent") {
              console.log("Value:", sampled.value); // Output the value
              percentValue = sampled.value;
            }
            if (sampled.context === "Sample.Periodic" && sampled.unit === "Wh" && sampled.measurand === "Energy.Active.Import.Register") {
              console.log("Value:", sampled.value); // Output the value
              MeterValue = sampled.value;
            }
          });
        }); */
    let items = [];
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#transactionId = :transactionId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        // '#status': 'status',
        '#transactionId': 'transactionId',
      },
      ExpressionAttributeValues: {
        ':transactionId': transId,
        // ':status': 'Active'
      }
    };

    // Call DynamoDB get operation to read data for the specified ID

    do {
      data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      console.log(data);
      body = items[0];
      document_Id = body.id;
      booking_type = body.booking_type;
      estimatedUnits = body.estimatedUnits;
      charging_fee = body.charging_fee;
      MeterStart = body.MeterStartWatt;
      startedAtTime = body.startedAtTime;
      consumedWatt = (parseInt(energyImportValue, 10) - parseInt(MeterStart, 10)) / 1000;
      pricePerKw = body.pricePerKw;
      chargerId = body.chargePointId;
      booking_id = body.booking_id;
      estimatedDuration = body.estimatedDuration;
      connectorId = body.connector_no;
    } else {
      console.log("Booking not found!... ")
      return await remoteStopTransac({
        "transactionId": transId,
        "action": "RemoteStopTransaction",
        "chargerId": event.CP_ID
      });
    }
    totalAmount = parseFloat(consumedWatt * pricePerKw);

    console.log("BODY....", body);

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  function getMinutesDifference(startingTime, dateString) {
    // Parse the date strings using Date constructor (consider libraries for robust parsing)
    const startTime = new Date(startingTime);
    const endTime = new Date(dateString);

    // Check for invalid dates using isNaN
    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      console.error("Invalid date format. Please use a valid date string.");
      return null;  // Optional: Return null for invalid dates
    }

    // Calculate the difference in milliseconds
    const timeDiff = endTime.getTime() - startTime.getTime();

    // Convert to minutes and round using Math.round for accuracy
    const minutesDiff = Math.round(timeDiff / (1000 * 60));

    return minutesDiff;
  }

  console.log("DATES......:", startedAtTime, typeof startedAtTime, timeStampOfMeterValue, typeof timeStampOfMeterValue);

  // Example usage
  // const datetime1 = new Date(2024, 5, 1, 10, 10); // May 1, 2024, 10:10 AM
  // const datetime2 = new Date(2024, 5, 1, 11, 20); // May 1, 2024, 11:20 AM



  // const timestamp = "2024-04-30T06:58:19Z";
  // const formattedDate = formatTimestamp(timestamp);
  // console.log(formattedDate); // Output: May 1, 2024, 2:58 AM


  // const comparisonResult = compareDateTime(datetime1, datetime2);
  // console.log(comparisonResult); // Output: "Datetime1 after adding 20 minutes is earlier than datetime2"


  const stopTransacJson = {
    "connectorId": connectorId,
    "idTag": booking_id,
    "transactionId": transId,
    "action": "RemoteStopTransaction",
    "chargerId": chargerId
  }

  //TODO Handle Charge Completion here

  if (socValue === "100") {
    //StopTransaction
    console.log("IN soc 100");
    remoteStopTransac(stopTransacJson);
  }
  if (body.status === "Canceled") {
    console.log("IN status is cancelled..!!");
    remoteStopTransac(stopTransacJson);
  }

  if (booking_type === "Duration") {
    console.log("Type is Duration");
    const timeDifference = getMinutesDifference(startedAtTime, timeStampOfMeterValue);
    console.log("Started At ", startedAtTime, "Diff is ", timeDifference, "Est. Dur", estimatedDuration);
    if (timeDifference >= (estimatedDuration - .4)) {
      //Stop Transaction
      console.log("IN timeDifference >= estimatedDuration");
      remoteStopTransac(stopTransacJson);
    }
  } else if (booking_type === "Unit") {
    console.log("Type is Unit", estimatedUnits, "Consumed", consumedWatt);
    if (consumedWatt >= (estimatedUnits - .2)) {
      //Stop Transaction
      console.log("consumedWatt >= estimatedUnits");
      remoteStopTransac(stopTransacJson);
    }
  } else if (booking_type === "Amount") {
    console.log("Type is Amount", charging_fee, "total amt", totalAmount);
    if (totalAmount >= (charging_fee - 5)) {
      //StopTransaction
      console.log("totalAmount >= charging_fee");
      remoteStopTransac(stopTransacJson);
    }
  }


  const differenceAmount = parseFloat(charging_fee - totalAmount);
  let dueAmount = parseFloat(totalAmount - charging_fee);
  dueAmount = dueAmount > 0 ? dueAmount : 0;

  //Handle Update Mutation here

  const variables = {
    id: document_Id, CurrentMeterWatt: energyImportValue, charging_percent: socValue, costOfConsump: totalAmount,
    unitsBurned: consumedWatt, dueAmount: dueAmount, ...(body.status === 'Inactive' && { status: 'Active' })
  };
  console.log("Variables", variables);

  //Add Values from DB and Event here TODO by Arbaaz
  const query = body.status === 'Inactive' ?
  /* GraphQL */ `
  mutation MyMutation($id: ID!, $CurrentMeterWatt: Int!, $charging_percent: String!, $status: String!,$costOfConsump: Float!, $unitsBurned: Float!,  $dueAmount: Float!) {
    updateChargingTable(
      input: {id: $id CurrentMeterWatt: $CurrentMeterWatt, charging_percent: $charging_percent, status: $status, costOfConsump: $costOfConsump, unitsBurned: $unitsBurned, dueAmount: $dueAmount}
    ) {
      createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
    }
  }`
    : /* GraphQL */ `
  mutation MyMutation($id: ID!, $CurrentMeterWatt: Int!, $charging_percent: String!,$costOfConsump: Float!, $unitsBurned: Float!,  $dueAmount: Float!) {
    updateChargingTable(
      input: {id: $id CurrentMeterWatt: $CurrentMeterWatt, charging_percent: $charging_percent, costOfConsump: $costOfConsump, unitsBurned: $unitsBurned, dueAmount: $dueAmount}
    ) {
      createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
    }
  }`;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);



  try {

    console.log("RUNNING MUTAION...");
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    console.log(body);
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};

async function remoteStopTransac(jsonString) {
  const myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");
  console.log("In remoteStopTransac.......");
  const raw = JSON.stringify(jsonString);
  console.log("Body of req", raw);

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: raw,
    // redirect: "follow"
  };

  fetch("https://5jjulse62zjpkrjqlkicpjelcm0uzysx.lambda-url.ap-south-1.on.aws/", requestOptions)
    // fetch("https://5usyuo2wyafeodfpsphzk65gfu0adzcl.lambda-url.ap-south-1.on.aws/", requestOptions)
    .then((response) => response.text())
    .then((result) => console.log("Stop result", result))
    .catch((error) => console.error("Error in stop trans", error));
}



// import { default as fetch, Request } from 'node-fetch';

// const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
// const GRAPHQL_API_KEY = "da2-**************************";

// import AWS from 'aws-sdk';

// // Create a new DynamoDB DocumentClient
// const dynamodb = new AWS.DynamoDB.DocumentClient();

// /**
//  * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
//  */
// export const handler = async (event) => {
//   console.log(`EVENT: ${JSON.stringify(event)}`);

//   let statusCode = 200;
//   let body;
//   let response;
//   let data;
//   let document_Id;
//   let percentValue;
//   let MeterValue;
//   let booking_type;
//   //
//   let estimatedUnits;
//   let charging_fee;
//   //
//   let timeStamp;
//   let MeterStart;
//   let consumedWatt;
//   let totalAmount;
//   let pricePerKw;
//   let connectorId;
//   let socValue;
//   let energyImportValue;
//   let chargerId;
//   let booking_id;
//   let startedAtTime;
//   let estimatedDuration;
//   let transId;
//   let timeStampOfMeterValue;

//   try {
//     // Extract the ID from the event object
//     // let { CP_ID, MSG_BODY } = event;
//     console.log("EvenT", event);
//     // console.log(event2.body);

//     // const eventBody = event2.body;
//     // const event = JSON.parse(eventBody);

//     // let { CP_ID, MSG_BODY } = event; //Todo by Arbaaz
//     //MeterValue = event.MeterValue;
//     // let jsonMessage = MSG_BODY;

//     const jsonData = event;
//     let jsonMessage = event.MSG_BODY;
//     // Define parameters for DynamoDB get operation


//     console.log("TRANSISkkdmcds ....", jsonData.MSG_BODY[3].transactionId);
//     transId = jsonData.MSG_BODY[3].transactionId;
//     // Loop through meterValue entries
//     for (const meterValue of jsonData.MSG_BODY[3].meterValue) {
//       // Loop through sampledValue entries within each meterValue
//       for (const sampledValue of meterValue.sampledValue) {
//         if (sampledValue.measurand === "SoC" && sampledValue.location === "EV" && sampledValue.context === "Sample.Periodic") {
//           socValue = sampledValue.value;
//         } else if (sampledValue.measurand === "Energy.Active.Import.Register" && sampledValue.location === "Outlet") {
//           energyImportValue = sampledValue.value;
//           timeStampOfMeterValue = meterValue.timestamp;
//         }
//       }
//     }

//     console.log("TimeStampOfMeterValue:", timeStampOfMeterValue, typeof timeStampOfMeterValue);
//     console.log("SoC value:", socValue);
//     console.log("Energy.Active.Import.Register value:", energyImportValue)
//     /* 
//         // Iterate through meterValue objects
//         jsonMessage.meterValue.forEach(meter => {
//           console.log("meter", meter);
//           // Iterate through sampledValue objects
//           meter.sampledValue.forEach(sampled => {
//             console.log("sampled", sampled);
//             // Check if context is "Sample.Periodic" and unit is "Percent"
//             if (sampled.context === "Sample.Periodic" && sampled.unit === "Percent") {
//               console.log("Value:", sampled.value); // Output the value
//               percentValue = sampled.value;
//             }
//             if (sampled.context === "Sample.Periodic" && sampled.unit === "Wh" && sampled.measurand === "Energy.Active.Import.Register") {
//               console.log("Value:", sampled.value); // Output the value
//               MeterValue = sampled.value;
//             }
//           });
//         }); */

//     const params = {
//       TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

//       FilterExpression: '#transactionId = :transactionId', // Filter expression to check if givenDate is between startDate and endDate
//       ExpressionAttributeNames: {
//         // '#status': 'status',
//         '#transactionId': 'transactionId',
//       },
//       ExpressionAttributeValues: {
//         ':transactionId': transId,
//         // ':status': 'Active'
//       }
//     };

//     // Call DynamoDB get operation to read data for the specified ID
//     data = await dynamodb.scan(params).promise();
//     console.log(data);
//     if (data.Items.length > 0) {
//       console.log(data);
//       body = data.Items[0];
//       document_Id = body.id;
//       booking_type = body.booking_type;
//       estimatedUnits = body.estimatedUnits;
//       charging_fee = body.charging_fee;
//       MeterStart = body.MeterStartWatt;
//       startedAtTime = body.startedAtTime;
//       consumedWatt = (parseInt(energyImportValue, 10) - parseInt(MeterStart, 10)) / 1000;
//       pricePerKw = body.pricePerKw;
//       chargerId = body.chargePointId;
//       booking_id = body.booking_id;
//       estimatedDuration = body.estimatedDuration;
//       connectorId = body.connector_no;
//     } else {
//       console.log("Booking not found!... ")
//       return await remoteStopTransac({
//         "transactionId": transId,
//         "action": "RemoteStopTransaction",
//         "chargerId": event.CP_ID
//       });
//     }
//     totalAmount = parseFloat(consumedWatt * pricePerKw);

//     console.log("BODY....", body);

//   } catch (error) {
//     // Return error response if an error occurs
//     console.error('Error reading data from DynamoDB:', error);
//     statusCode = 400;
//     body = {
//       errors: [
//         {
//           status: response.status,
//           message: error.message,
//           stack: error.stack
//         }
//       ]
//     };
//   }

//   function getMinutesDifference(startingTime, dateString) {
//     // Parse the date strings using Date constructor (consider libraries for robust parsing)
//     const startTime = new Date(startingTime);
//     const endTime = new Date(dateString);

//     // Check for invalid dates using isNaN
//     if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
//       console.error("Invalid date format. Please use a valid date string.");
//       return null;  // Optional: Return null for invalid dates
//     }

//     // Calculate the difference in milliseconds
//     const timeDiff = endTime.getTime() - startTime.getTime();

//     // Convert to minutes and round using Math.round for accuracy
//     const minutesDiff = Math.round(timeDiff / (1000 * 60));

//     return minutesDiff;
//   }

//   console.log("DATES......:", startedAtTime, typeof startedAtTime, timeStampOfMeterValue, typeof timeStampOfMeterValue);

//   // Example usage
//   // const datetime1 = new Date(2024, 5, 1, 10, 10); // May 1, 2024, 10:10 AM
//   // const datetime2 = new Date(2024, 5, 1, 11, 20); // May 1, 2024, 11:20 AM



//   // const timestamp = "2024-04-30T06:58:19Z";
//   // const formattedDate = formatTimestamp(timestamp);
//   // console.log(formattedDate); // Output: May 1, 2024, 2:58 AM


//   // const comparisonResult = compareDateTime(datetime1, datetime2);
//   // console.log(comparisonResult); // Output: "Datetime1 after adding 20 minutes is earlier than datetime2"


//   const stopTransacJson = {
//     "connectorId": connectorId,
//     "idTag": booking_id,
//     "transactionId": transId,
//     "action": "RemoteStopTransaction",
//     "chargerId": chargerId
//   }

//   //TODO Handle Charge Completion here

//   if (socValue === "100") {
//     //StopTransaction
//     console.log("IN soc 100");
//     remoteStopTransac(stopTransacJson);
//   }
//   if (body.status === "Canceled") {
//     console.log("IN status is cancelled..!!");
//     remoteStopTransac(stopTransacJson);
//   }

//   if (booking_type === "Duration") {
//     console.log("Type is Duration");
//     const timeDifference = getMinutesDifference(startedAtTime, timeStampOfMeterValue);
//     console.log("Started At ", startedAtTime, "Diff is ", timeDifference, "Est. Dur", estimatedDuration);
//     if (timeDifference >= (estimatedDuration - .4)) {
//       //Stop Transaction
//       console.log("IN timeDifference >= estimatedDuration");
//       remoteStopTransac(stopTransacJson);
//     }
//   } else if (booking_type === "Unit") {
//     console.log("Type is Unit", estimatedUnits, "Consumed", consumedWatt);
//     if (consumedWatt >= (estimatedUnits - .2)) {
//       //Stop Transaction
//       console.log("consumedWatt >= estimatedUnits");
//       remoteStopTransac(stopTransacJson);
//     }
//   } else if (booking_type === "Amount") {
//     console.log("Type is Amount", charging_fee, "total amt", totalAmount);
//     if (totalAmount >= (charging_fee - 2)) {
//       //StopTransaction
//       console.log("totalAmount >= charging_fee");
//       remoteStopTransac(stopTransacJson);
//     }
//   }


//   const differenceAmount = parseFloat(charging_fee - totalAmount);
//   let dueAmount = parseFloat(totalAmount - charging_fee);
//   dueAmount = dueAmount > 0 ? dueAmount : 0;

//   //Handle Update Mutation here

//   const variables = {
//     id: document_Id, CurrentMeterWatt: energyImportValue, charging_percent: socValue, costOfConsump: totalAmount,
//     unitsBurned: consumedWatt, dueAmount: dueAmount, ...(body.status === 'Inactive' && { status: 'Active' })
//   };


//   //Add Values from DB and Event here TODO by Arbaaz
//   const query = body.status === 'Inactive' ?
//   /* GraphQL */ `
//   mutation MyMutation($id: ID!, $CurrentMeterWatt: Int!, $charging_percent: String!, $status: String!,$costOfConsump: Float!, $unitsBurned: Float!,  $dueAmount: Float!) {
//     updateChargingTable(
//       input: {id: $id CurrentMeterWatt: $CurrentMeterWatt, charging_percent: $charging_percent, status: $status, costOfConsump: $costOfConsump, unitsBurned: $unitsBurned, dueAmount: $dueAmount}
//     ) {
//       createdAt
//       updatedAt
//       id
//       booking_id
//       start_time
//       end_time
//       status
//       connector_no
//       CurrentMeterWatt
//       city
//       charging_fee
//       payment_status
//       createdAt
//       tax_amount
//       vehical_number
//       chargePointId
//       user_id
//       station_id
//       vehicle_id
//       charging_percent
//       MeterStartWatt
//       MeterEndWatt
//       booking_type
//       compareValue
//       pricePerKw
//       geoState
//       isPaid
//       chargerId
//       transactionId
//       estimatedDuration
//       estimatedUnits
//       startedAtPercent
//       stopedAtPercent
//       unitsBurned
//       costOfConsump
//       refundedAmount
//       startedAtTime
//       stopedAtTime
//       payment_Id
//       paymentTime
//       lastCommand
//       gstin
//           userName
//           userContact
//           overchargeDueCleared
//           invoiceNo
//     }
//   }`
//     : /* GraphQL */ `
//   mutation MyMutation($id: ID!, $CurrentMeterWatt: Int!, $charging_percent: String!,$costOfConsump: Float!, $unitsBurned: Float!,  $dueAmount: Float!) {
//     updateChargingTable(
//       input: {id: $id CurrentMeterWatt: $CurrentMeterWatt, charging_percent: $charging_percent, costOfConsump: $costOfConsump, unitsBurned: $unitsBurned, dueAmount: $dueAmount}
//     ) {
//       createdAt
//       updatedAt
//       id
//       booking_id
//       start_time
//       end_time
//       status
//       connector_no
//       CurrentMeterWatt
//       city
//       charging_fee
//       payment_status
//       createdAt
//       tax_amount
//       vehical_number
//       chargePointId
//       user_id
//       station_id
//       vehicle_id
//       charging_percent
//       MeterStartWatt
//       MeterEndWatt
//       booking_type
//       compareValue
//       pricePerKw
//       geoState
//       isPaid
//       chargerId
//       transactionId
//       estimatedDuration
//       estimatedUnits
//       startedAtPercent
//       stopedAtPercent
//       unitsBurned
//       costOfConsump
//       refundedAmount
//       startedAtTime
//       stopedAtTime
//       payment_Id
//       paymentTime
//       lastCommand
//       gstin
//           userName
//           userContact
//           overchargeDueCleared
//           invoiceNo
//     }
//   }`;


//   /** @type {import('node-fetch').RequestInit} */
//   const options = {
//     method: 'POST',
//     headers: {
//       'x-api-key': GRAPHQL_API_KEY,
//       'Content-Type': 'application/json'
//     },
//     body: JSON.stringify({ query, variables })
//   };

//   const request = new Request(GRAPHQL_ENDPOINT, options);



//   try {

//     console.log("RUNNING MUTAION...");
//     response = await fetch(request);
//     console.log(response);
//     body = await response.json();
//     console.log(body);
//     if (body.errors) statusCode = 400;
//   } catch (error) {
//     statusCode = 400;
//     body = {
//       errors: [
//         {
//           status: response.status,
//           message: error.message,
//           stack: error.stack
//         }
//       ]
//     };
//   }

//   return {
//     statusCode,
//     body: JSON.stringify(body)
//   };
// };

// async function remoteStopTransac(jsonString) {
//   const myHeaders = new Headers();
//   myHeaders.append("Content-Type", "application/json");
//   console.log("In remoteStopTransac.......");
//   const raw = JSON.stringify(jsonString);
//   console.log("Body of req", raw);

//   const requestOptions = {
//     method: "POST",
//     headers: myHeaders,
//     body: raw,
//     // redirect: "follow"
//   };

//   fetch("https://5jjulse62zjpkrjqlkicpjelcm0uzysx.lambda-url.ap-south-1.on.aws/", requestOptions)
//     // fetch("https://5usyuo2wyafeodfpsphzk65gfu0adzcl.lambda-url.ap-south-1.on.aws/", requestOptions)
//     .then((response) => response.text())
//     .then((result) => console.log("Stop result", result))
//     .catch((error) => console.error("Error in stop trans", error));
// }