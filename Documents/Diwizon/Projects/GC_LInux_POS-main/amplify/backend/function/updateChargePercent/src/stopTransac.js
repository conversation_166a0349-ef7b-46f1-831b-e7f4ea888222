/* import { default as fetch, Request } from 'node-fetch';

export async function remoteStopTransac(jsonString) {
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    console.log("In remoteStopTransac.......");
    const raw = JSON.stringify(jsonString);

    const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
    };

    fetch("https://wf5wi72b7llpks5emdfiuj3umq0tpneu.lambda-url.ap-south-1.on.aws/", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
} */