import crypto from 'crypto';
import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;
import razorpay from 'razorpay';
import { v4 as uuidv4 } from 'uuid';
import AWS from 'aws-sdk';

const dynamodb = new AWS.DynamoDB.DocumentClient();


// const Razorpay = require('razorpay');
export const handler = async (event) => {

  try {
    console.log("Event", event);
    const data = JSON.parse(event.body);
    const phonePe = data['pg'] == "PhonePe";
    if (phonePe) {
      const url = 'https://api.phonepe.com/apis/hermes/pg/v1/refund';

      // Sample payload
      const payload = {
        "merchantId": "M22861WGHBNS0",
        "merchantUserId": "GOCHARGE123",
        "originalTransactionId": data['transRef'],
        "merchantTransactionId": uuidv4(),
        "amount": data['amount'] * 100,
        "callbackUrl": "https://nhnkf4yu6ygj6m6lgt7pbneib40pzxmr.lambda-url.ap-south-1.on.aws/"
      };

      // Salt information
      const saltKey = '3dc974a1-27cc-49bc-b19c-055b606845cf';
      const saltIndex = '1'; // 

      // Convert payload to base64 string
      const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');

      // Function to generate the required hash
      function generateHash(base64Payload, saltKey, saltIndex) {
        const concatenatedString = base64Payload + '/pg/v1/refund' + saltKey;
        const hash = crypto.createHash('sha256').update(concatenatedString).digest('hex');
        return `${hash}###${saltIndex}`;
      }

      // Generate the hash using the base64 encoded payload
      const hash = generateHash(base64Payload, saltKey, saltIndex);

      // Configure options for the fetch request
      const options = {
        method: 'POST',
        headers: {
          'X-VERIFY': hash,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          request: base64Payload, // sending the base64-encoded payload
        })
      };

      // Execute the request
      const resp = await fetch(url, options);
      const respData = await resp.json();
      console.log(respData);
      await createRefundRecord(respData, data);
      return respData;
    } else {
      // Replace with your actual Razorpay credentials stored securely (e.g., Secrets Manager)
      const keyId = "***********************"; // "rzp_test_cn6p8HqmFvpWSW"
      const keySecret = "2IsMZvfk9kPz2iB4loELYUfj"; // "kouKRBYkLFI51GTS9ntQUX8f"
      const razorpayInstance = new razorpay({
        key_id: keyId,
        key_secret: keySecret
      });

      const refund = await razorpayInstance.payments.refund(data['paymentId'], {
        amount: data['amount'] * 100
      });

      console.log("Refund created:", refund);
      return {
        statusCode: 200,
        body: JSON.stringify({ message: "Refund initiated successfully!", refund }),
      };
    }


  } catch (error) {
    console.error("Error initiating refund:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Failed to initiate refund!" }),
    };
  }
};

async function createRefundRecord(params, paramsData) {
  try {
    let chargingData;
    try {
      let items = [];
      const chargeParams = {
        TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

        FilterExpression: '#pgTransRef = :pgTransRef', // Filter expression to check if givenDate is between startDate and endDate
        ExpressionAttributeNames: {
          '#pgTransRef': 'pgTransRef'
        },
        ExpressionAttributeValues: {
          ':pgTransRef': params['data']['merchantTransactionId']
        }
      }

      // Call DynamoDB get operation to read data for the specified ID

      do {
        const chargeData = await dynamodb.scan(chargeParams).promise();
        console.log("charge", chargeData);
        items = items.concat(chargeData.Items);
        chargeParams.ExclusiveStartKey = chargeData.LastEvaluatedKey;
      } while (chargeParams.ExclusiveStartKey);

      if (items.length > 0) {
        chargingData = items[0];
      }
    } catch (error) {
      console.log("Error getting charging data", error);
    }

    let variables = {
      refunded: false,
      merchantId: params['data']['merchantId'],
      merchantTransactionId: params['data']['merchantTransactionId'],
      transactionId: params['data']['transactionId'],
      amount: params['data']['amount'] / 100,
      state: params['data']['state'],
      responseCode: params['data']['responseCode'],
      paymentInstrument: null,
      code: params['code'],
      message: params['message'],
      uId: paramsData['user_id'],
      userName: paramsData['userName'],
      userContact: paramsData['userContact'],
    };


    console.log("In create refund....", variables);

    const query = /* GraphQL */ `
      mutation MyMutation(
        $refunded: Boolean,
        $merchantId: String,
        $merchantTransactionId: String,
        $transactionId: String,
        $amount: String,
        $state: String,
        $responseCode: String,
        $paymentInstrument: AWSJSON,
        $code: String,
        $message: String,
        $uId: String,
        $userName: String,
        $userContact: String
        ) {
        createRefundRecord(
          input: { 
            refunded:$refunded,
            merchantId:$merchantId,
            merchantTransactionId:$merchantTransactionId,
            transactionId:$transactionId,
            amount:$amount,
            state:$state,
            responseCode:$responseCode,
            paymentInstrument:$paymentInstrument,
            code:$code,
            message:$message,
            uId:$uId,
            userName:$userName,
            userContact:$userContact
          }
        ) {
          createdAt
          updatedAt
          id
          refunded
          merchantId
          merchantTransactionId
          transactionId
          amount
          state
          responseCode
          paymentInstrument
          code
          message
          uId
          userName
          userContact
        }
      }
      `;

    /** @type {import('node-fetch').RequestInit} */
    const transacOptions = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables })
    };

    const refReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

    console.log("Running create refund record Mutation...");
    const response = await fetch(refReq);
    console.log(response);
    const body = await response.json();
    // if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    console.log(error);
  }
}