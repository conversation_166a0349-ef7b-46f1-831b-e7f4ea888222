import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';


const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

let dataA, jsondata;
let pgTransRef, statusCode, body, response, orderData, paymentResponse;
let transRef, reason, method, amount, timeStamp, uId, transDocId, payment_Id;
export const handler = async (event) => {
  try {
    jsondata = event.body;
    console.log(event);
    dataA = JSON.parse(jsondata);
    // PaymentData
    try {
      console.log("In decoder...");
      let bufferObj = Buffer.from(dataA.response, "base64");
      let decodedString = bufferObj.toString("utf8");
      console.log("The decoded string:", decodedString);
      paymentResponse = JSON.parse(decodedString);
      console.log("paymentResponse: ", paymentResponse);
      // Assign values
      pgTransRef = paymentResponse['data']['merchantTransactionId'];
      transRef = paymentResponse['data']['transactionId'];
      method = paymentResponse['data']['paymentInstrument']['type'];
      amount = paymentResponse['data']['amount'] / 100;
      payment_Id = paymentResponse['data']['transactionId'];
      timeStamp = new Date();
    } catch (error) {
      console.log("Error Decoding: ", error);
    }

    let paymentDataItems = [];
    const payDataParams = {
      TableName: 'PaymentData-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#bId = :bId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#bId': 'bId'
      },
      ExpressionAttributeValues: {
        ':bId': pgTransRef
      }
    }

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const paymentData = await dynamodb.scan(payDataParams).promise();
      paymentDataItems = paymentDataItems.concat(paymentData.Items);
      payDataParams.ExclusiveStartKey = paymentData.LastEvaluatedKey;
    } while (payDataParams.ExclusiveStartKey);

    // console.log(dataA);
    // if (data['event'] == "order.captured") {
    try {
      // RUN Get Order API to verify status.
    } catch (error) {
      console.log(error);
    }
    // }
    /* data['event'] == "payment.captured" */
    if (paymentDataItems.length > 0) {
      const paymentDataData = paymentDataItems[0];
      reason = paymentDataData['type'];
      uId = paymentDataData['uId'];

      if (paymentResponse['code'] == "PAYMENT_SUCCESS") {
        console.log("paid");
        console.log(pgTransRef);
        // ------------- //
        // Check if the transaction was added manually 
        let transactionItems = [];
        const transParams = {
          TableName: 'Transaction-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

          FilterExpression: '#transRef = :transRef', // Filter expression to check if givenDate is between startDate and endDate
          ExpressionAttributeNames: {
            '#transRef': 'transRef'
          },
          ExpressionAttributeValues: {
            ':transRef': transRef
          }
        }

        // Call DynamoDB get operation to read data for the specified ID

        do {
          const transData = await dynamodb.scan(transParams).promise();
          console.log("transData", transData);
          transactionItems = transactionItems.concat(transData.Items);
          transParams.ExclusiveStartKey = transData.LastEvaluatedKey;
        } while (transParams.ExclusiveStartKey);

        if (transactionItems.length > 0) {
          const transDataMap = transactionItems[0];
          if (transDataMap.transRef == transRef && transDataMap.amount == amount) {
            console.log("The amount was added manually!");
            return { "msg": "Added Manually!" };
          }
        }
        // ------------- //

        if (reason == "Wallet"/*  && data['event'] == "payment.captured" */) {
          console.log("This is Wallet Transaction....");
          await updateUserBalance(uId, amount);
          await updateTrans(pgTransRef, transRef, uId, reason, method, amount, payment_Id, timeStamp);
        } else if (reason == "Dues"/*  && data['event'] == "payment.captured" */) {
          await clearDues(pgTransRef);
        } else {
          let walletAmountUsed = 0;
          const chargingData = await getChargingData(pgTransRef);
          transDocId = chargingData.transDocId;
          console.log("B4", "transDocId", transDocId, typeof transDocId);
          if (chargingData.amountFromWallet != null) {
            console.log("WalletAmountUsed", walletAmountUsed);
            if (chargingData.amountFromWallet > 0) {
              walletAmountUsed = chargingData.amountFromWallet;
              console.log("WalletAmountUsed", walletAmountUsed);
              // if (data['event'] == "order.paid") {
              await updateUserBalance(uId, -chargingData.amountFromWallet);
              // }
            }
          }

          let created_at = new Date();
          // let created_at = convertToAWSDateTime(timeStamp);

          try {
            // const invoiceNo = await getSetInvoiceNo(chargingData.station_id);
            // console.log("Invoice No: ", invoiceNo);
            const variables = {
              id: chargingData.id, isPaid: true, payment_Id: payment_Id, paymentTime: created_at,
              // invoiceNo: invoiceNo
            };

            //Add Values from DB and Event here TODO by Arbaaz
            const query = /* GraphQL */ `
        mutation MyMutation($id: ID!, $isPaid: Boolean!, $payment_Id: String, $paymentTime: AWSDateTime, $invoiceNo: Int) {
          updateChargingTable(
            input: {id: $id, isPaid: $isPaid, payment_Id: $payment_Id, paymentTime: $paymentTime, invoiceNo: $invoiceNo}
          ) {
            createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
          }
        }`;

            /** @type {import('node-fetch').RequestInit} */
            const options = {
              method: 'POST',
              headers: {
                'x-api-key': GRAPHQL_API_KEY,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ query, variables })
            };

            const request = new Request(GRAPHQL_ENDPOINT, options);

            console.log("Running Mutation...")
            response = await fetch(request);
            console.log(response);
            body = await response.json();
            if (body.errors) statusCode = 400;
            console.log(body);
          } catch (error) {
            statusCode = 400;
            body = {
              errors: [
                {
                  status: response.status,
                  message: error.message,
                  stack: error.stack
                }
              ]
            };
            console.error(body);
          }


          try {
            const variables = {
              id: transDocId, transRef: transRef, uId: uId, method: walletAmountUsed == 0 ? method : `Wallet + ${method}`, dateTime: created_at,
              bookingId: chargingData.booking_id, amount: parseFloat(amount.toString()), status: "Successful", walletAmountUsed: walletAmountUsed
            };
            console.log(variables);

            const query = /* GraphQL */ `
          mutation MyMutation($id: ID!, $transRef: String!, $method: String!, $dateTime: AWSDateTime!, $bookingId: String!, $amount: Float!,$walletAmountUsed: Float!, $status: String!) {
            updateTransaction(
              input: {id: $id, transRef: $transRef, method: $method, dateTime: $dateTime, bookingId: $bookingId, amount: $amount,walletAmountUsed: $walletAmountUsed, status: $status}
            ) {
              createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
            }
          }`;

            /** @type {import('node-fetch').RequestInit} */
            const transacOptions = {
              method: 'POST',
              headers: {
                'x-api-key': GRAPHQL_API_KEY,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ query, variables })
            };

            const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

            console.log("Running Update transaction Mutation...");
            const response2 = await fetch(transacReq);
            console.log(response2);
            const body2 = await response2.json();
            if (body2.errors) statusCode = 400;
            console.log(body2);


            // Schedule booking checker 
            const stepFunctions = new AWS.StepFunctions();


            const params = {
              stateMachineArn: 'arn:aws:states:ap-south-1:************:stateMachine:MyStateMachine-m6mq59uq6',
              input: JSON.stringify({ "bookingId": chargingData.booking_id, "uId": uId })
            };

            try {
              const response = await stepFunctions.startExecution(params).promise();
              console.log('State machine execution started:', response);
            } catch (err) {
              console.error('Error starting state machine:', err);
            }

          } catch (error) {
            console.log("Inside Update Transaction Mutation Catch");
            statusCode = 400;
            const bodyy = {
              errors: [
                {
                  status: response.status,
                  message: error.message,
                  stack: error.stack
                }
              ]
            };
            console.error(bodyy);
          }

        }
      }
    }


  } catch (error) {
    console.log("Error verifying order", error);
    return {
      body: JSON.stringify(error)
    };
  }
};


async function updateTrans(pgTransRef, transRef, uId, reason, method, amount, payment_Id, timeStamp) {

  try {
    console.log("In updateTrans..");
    let transactionItems = [];
    const transParams = {
      TableName: 'Transaction-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#pgTransRef = :pgTransRef', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#pgTransRef': 'pgTransRef'
      },
      ExpressionAttributeValues: {
        ':pgTransRef': pgTransRef
      }
    }

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const transData = await dynamodb.scan(transParams).promise();
      console.log("transData", transData);
      transactionItems = transactionItems.concat(transData.Items);
      transParams.ExclusiveStartKey = transData.LastEvaluatedKey;
    } while (transParams.ExclusiveStartKey);

    if (transactionItems.length > 0) {
      const transDataMap = transactionItems[0];
      // const bId = notes['bId'];
      // const transRef = payload['id'];
      // const reason = notes['type'];
      // const uId = notes['uId'];
      // const method = payload['method'];
      // const amount = payload['amount'];
      // const payment_Id = payload['id'];
      // const timeStamp = payload['created_at'];

      let created_at = new Date();
      // const created_at = convertToAWSDateTime(timeStamp);

      try {
        const variables = {
          id: transDataMap.id, transRef: transRef, uId: uId, method: method,
          dateTime: created_at, amount: parseFloat(amount.toString()), status: "Successful"
        };
        console.log(variables);

        const query = /* GraphQL */ `
      mutation MyMutation($id: ID!, $transRef: String!, $method: String!, $dateTime: AWSDateTime!,  $amount: Float!, $status: String!) {
        updateTransaction(
          input: {id: $id, transRef: $transRef, method: $method, dateTime: $dateTime,  amount: $amount, status: $status}
        ) {
          createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
        }
      }`;

        /** @type {import('node-fetch').RequestInit} */
        const transacOptions = {
          method: 'POST',
          headers: {
            'x-api-key': GRAPHQL_API_KEY,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ query, variables })
        };

        const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

        console.log("Running Update transaction Mutation...");
        const response2 = await fetch(transacReq);
        console.log(response2);
        const body2 = await response2.json();
        if (body2.errors) statusCode = 400;
        console.log(body2);

      } catch (error) {
        console.log("Inside Update Transaction Mutation Catch");
        statusCode = 400;
        const bodyy = {
          errors: [
            {
              status: response.status,
              message: error.message,
              stack: error.stack
            }
          ]
        };
        console.error(bodyy);
      }
    }


  } catch (error) {
    console.log(error);
  }
}


async function getChargingData(pgTransRef) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#pgTransRef = :pgTransRef',
      ExpressionAttributeNames: {
        '#pgTransRef': 'pgTransRef'
      },
      ExpressionAttributeValues: {
        ':pgTransRef': pgTransRef
      }
    };

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      return body;
    }

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}

async function updateUserBalance(uId, addon) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: "EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': uId
      }
    };

    console.log("USER ID is", uId);

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      console.log("OLD Balance is ", body.balance);
      const newBalance = body.balance + (addon);
      try {
        const variables = { id: body.id, newBalance: newBalance };
        console.log(variables);

        const query = /* GraphQL */ `
      mutation MyMutation($id: ID!,$newBalance: Float!) {
        updateEndUser(
          input: {id: $id,balance: $newBalance}
        ) {
          id
          user_fullname
          dob
          joining_date
          email
          contact
          balance
          default_vehicle_id
          favs
          uId
          deviceId
          createdAt
          updatedAt
        }
      }`;

        /** @type {import('node-fetch').RequestInit} */
        const transacOptions = {
          method: 'POST',
          headers: {
            'x-api-key': GRAPHQL_API_KEY,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ query, variables })
        };

        const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

        console.log("Running Upate end user Mutation...");
        const response2 = await fetch(transacReq);
        console.log(response2);
        const body2 = await response2.json();
        if (body2.errors) statusCode = 400;
        console.log(body2);
      } catch (error) {
        console.log("Inside Create Transaction Mutation Catch");
        statusCode = 400;
        const bodyy = {
          errors: [
            {
              status: response.status,
              message: error.message,
              stack: error.stack
            }
          ]
        };
        console.error(bodyy);
      }
    }



  } catch (error) {
    // Return error response if an error occurs
    console.error('Error in updateUserBalance():', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}


// async function getSetInvoiceNo(stationId) {
//   try {
//     // Extract the ID from the event object
//     // Define parameters for DynamoDB get operation
//     let items = [];
//     const params = {
//       TableName: "Station-r6cw5zqo7zb37hhq7w4ympiugy-prod",
//       FilterExpression: '#id = :id',
//       ExpressionAttributeNames: {
//         '#id': 'id'
//       },
//       ExpressionAttributeValues: {
//         ':id': stationId
//       }
//     };

//     // Call DynamoDB get operation to read data for the specified ID

//     do {
//       const data = await dynamodb.scan(params).promise();
//       console.log(data);
//       items = items.concat(data.Items);
//       params.ExclusiveStartKey = data.LastEvaluatedKey;
//     } while (params.ExclusiveStartKey);

//     if (items.length > 0) {
//       const stationData = items[0];

//       console.log(stationData);
//       const params2 = {
//         TableName: "CPO-r6cw5zqo7zb37hhq7w4ympiugy-prod",
//         FilterExpression: '#id = :id',
//         ExpressionAttributeNames: {
//           '#id': 'id'
//         },
//         ExpressionAttributeValues: {
//           ':id': stationData.cpoId
//         }
//       };

//       // Call DynamoDB get operation to read data for the specified ID
//       console.log("...1");
//       const data2 = await dynamodb.scan(params2).promise();
//       console.log("...2");
//       console.log(data2);
//       if (data2.Items.length > 0) {
//         console.log("...3");
//         const cpoData = data2.Items[0];
//         console.log(cpoData);
//         //
//         let oldInvoiceNo = cpoData.invoiceNo;
//         oldInvoiceNo ??= 0;
//         let newInvoiceNo = oldInvoiceNo + 1;
//         console.log(newInvoiceNo);
//         console.log("...4");
//         //
//         const finDateUpdated = cpoData.currentFinYear != null ? checkCurrentFinYearDate(cpoData.currentFinYear) : true;
//         console.log("finDateUpdated", finDateUpdated);
//         //
//         newInvoiceNo = finDateUpdated ? newInvoiceNo : 1;

//         try {
//           const variables = {
//             id: cpoData.id, invoiceNo: newInvoiceNo, currentFinYear: finDateUpdated ? (cpoData.currentFinYear == null ? getCurrentFinancialYearStartDate() : cpoData.currentFinYear) : getCurrentFinancialYearStartDate()
//           };
//           console.log(variables);

//           console.log("...5");
//           const query = /* GraphQL */ `
//           mutation MyMutation2($id: ID!, $invoiceNo: Int, $currentFinYear: AWSDateTime) {
//             updateCPO(
//               input: {id: $id, invoiceNo: $invoiceNo, currentFinYear: $currentFinYear}
//             ) { 
//               id
//               invoiceNo
//               currentFinYear
//             }
//           }`;
//           console.log("...6");

//           const cpoOptions = {
//             method: 'POST',
//             headers: {
//               'x-api-key': GRAPHQL_API_KEY,
//               'Content-Type': 'application/json'
//             },
//             body: JSON.stringify({ query, variables })
//           };

//           const cpoReq = new Request(GRAPHQL_ENDPOINT, cpoOptions);
//           console.log("...7");

//           console.log("Running Update Mutation...");
//           const response2 = await fetch(cpoReq);
//           console.log("...8");
//           console.log(response2);
//           const body2 = await response2.json();
//           if (body2.errors) statusCode = 400;
//           console.log(body2);
//           return newInvoiceNo;

//         } catch (error) {
//           console.log("Inside Update Mutation Catch");
//           statusCode = 400;
//           const bodyy = {
//             errors: [
//               {
//                 status: response.status,
//                 message: error.message,
//                 stack: error.stack
//               }
//             ]
//           };
//           console.error(bodyy);
//         }
//       }
//     }
//   } catch (error) {
//     // Return error response if an error occurs
//     console.error('Error reading data from DynamoDB:', error);
//     statusCode = 400;
//     body = {
//       errors: [
//         {
//           status: response.status,
//           message: error.message,
//           stack: error.stack
//         }
//       ]
//     };
//   }
// }


function convertToAWSDateTime(timestamp) {
  const date = new Date(timestamp * 1000); // Multiply by 1000 to convert seconds to milliseconds
  const awsDateTime = date.toISOString().split('.')[0] + 'Z'; // Remove milliseconds and add 'Z' to indicate UTC time
  return awsDateTime;
}


function checkCurrentFinYearDate(currentFinYear) {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  // Determine the financial year start date
  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  // Convert currentFinYear to a Date object if it's not already one
  const currentFinYearDate = new Date(currentFinYear);

  // Check if currentFinYearDate is the same as finYearStart
  const finDateUpdated = currentFinYearDate.getTime() === finYearStart.getTime();

  return finDateUpdated;
}


function getCurrentFinancialYearStartDate() {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  return finYearStart;
}

async function clearDues(uId) {
  try {
    // Extract the ID from the event object
    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: "DueRequest-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': uId
      }
    };

    console.log("Dues ID is", uId);

    // Call DynamoDB get operation to read data for the specified ID

    do {
      const data = await dynamodb.scan(params).promise();
      console.log(data);
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      body = items[0];
      for (const item of items[0].dues) {
        try {
          const variables = { id: item, overchargeDueCleared: true };
          console.log(variables);

          const query = /* GraphQL */ `
        mutation MyMutation($id: ID!, $overchargeDueCleared: Boolean!) {
          updateChargingTable(
            input: {id: $id, overchargeDueCleared: $overchargeDueCleared}
          ) {
            createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
          }
        }`;

          /** @type {import('node-fetch').RequestInit} */
          const transacOptions = {
            method: 'POST',
            headers: {
              'x-api-key': GRAPHQL_API_KEY,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, variables })
          };

          const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

          console.log("Running Upate end user Mutation...");
          const response2 = await fetch(transacReq);
          console.log(response2);
          const body2 = await response2.json();
          if (body2.errors) statusCode = 400;
          console.log(body2);
        } catch (error) {
          console.log("Inside Create Transaction Mutation Catch");
          statusCode = 400;
          console.error(error);
        }
      }
    }



  } catch (error) {
    // Return error response if an error occurs
    console.error('Error in:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }
}



/* 
2024-11-09T04:42:31.600Z	a4b153f1-6f1f-4f9c-b317-90f0193269ea	INFO	The decoded string: {
    "success": true,
    "code": "PAYMENT_SUCCESS",
    "message": "Your payment is successful.",
    "data": {
        "merchantId": "M22861WGHBNS0",
        "merchantTransactionId": "THL87NXDJKJ9ARP54H2Y",
        "transactionId": "T2411091012269465815267",
        "amount": 100,
        "state": "COMPLETED",
        "responseCode": "SUCCESS",
        "paymentInstrument": {
            "type": "UPI",
            "utr": "************",
            "upiTransactionId": "IBL27fb49266cf24858843dc93a1d556c9a",
            "cardNetwork": null,
            "accountType": "SAVINGS"
        },
        "feesContext": {
            "amount": 0
        }
    }
}
*/