import AWS from 'aws-sdk';

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const dynamodb = new AWS.DynamoDB.DocumentClient();

export const handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    console.log('Received event:', JSON.stringify(event, null, 2));
    // Access the first record in the event
    if (event.Records) {
        return;
        const record = event.Records[0];
        const newImageKey = record.dynamodb.Keys.id.S;
        const transParams = {
            TableName: "Transaction-r6cw5zqo7zb37hhq7w4ympiugy-prod",
            FilterExpression: '#id = :id',
            ExpressionAttributeNames: {
                '#id': 'id'
            },
            ExpressionAttributeValues: {
                ':id': newImageKey
            }
        };

        // Call DynamoDB get operation to read data for the specified ID
        const data = await dynamodb.scan(transParams).promise();
        console.log(data);
        if (data.Items.length > 0) {
            console.log("...3");
            const transData = data.Items[0];

            // Extract specific fields from the NewImage
            const status = transData.status;
            const bookingId = transData.bookingId;
            const uId = transData.uId;
            console.log(`New transaction: ${status}, ${bookingId}, ${uId}`);
            if (transData.method !== "Wallet") {
                console.log("Wallet booking sch not required now");
                return;
            }

            if (status !== "Successful" || bookingId == "") {
                console.log("Returing: No need to check booking");
                return;
            }
            const stepFunctions = new AWS.StepFunctions();

            const params = {
                stateMachineArn: 'arn:aws:states:ap-south-1:************:stateMachine:MyStateMachine-m6mq59uq6',
                input: JSON.stringify({ "bookingId": bookingId, "uId": uId })
            };

            try {
                const response = await stepFunctions.startExecution(params).promise();
                console.log('State machine execution started:', response);
            } catch (err) {
                console.error('Error starting state machine:', err);
            }
        }

    } else {
        const bodyObject = JSON.parse(event.body);
        console.log("Body", bodyObject);
        const bookingId = bodyObject.bookingId;
        const uId = bodyObject.userId;
        const stepFunctions = new AWS.StepFunctions();


        const params = {
            stateMachineArn: 'arn:aws:states:ap-south-1:************:stateMachine:MyStateMachine-m6mq59uq6',
            input: JSON.stringify({ "bookingId": bookingId, "uId": uId })
        };

        try {
            const response = await stepFunctions.startExecution(params).promise();
            console.log('State machine execution started:', response);
        } catch (err) {
            console.error('Error starting state machine:', err);
        }
    }


    return;
};
