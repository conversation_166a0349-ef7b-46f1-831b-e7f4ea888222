import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let document_Id;
  let connectors;
  let charger_document_Id;
  let stationId;

  try {
    // Extract the ChargePoint ID from the event object
    const { CP_ID, status, connectorId } = event;

    console.log(`Charger ID and other attributes: ${CP_ID} and ${connectorId}`);

    let items = [];
    const chargerParams = {
      TableName: 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name
      FilterExpression: '#chargingPointId = :chargingPointId',
      ExpressionAttributeNames: {
        '#chargingPointId': 'chargingPointId',
      },
      ExpressionAttributeValues: {
        ':chargingPointId': CP_ID,
      }
    };

    // Call DynamoDB scan operation to read data for the specified ID

    do {
      data = await dynamodb.scan(chargerParams).promise();
      items = items.concat(data.Items);
      chargerParams.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (chargerParams.ExclusiveStartKey);

    if (items.length > 0) {
      // console.log(data);
      body = items[0];
      stationId = body.stationId;
      charger_document_Id = body.id;
    }

    let connectorItems = [];
    const params = {
      TableName: 'Connector-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name
      FilterExpression: '#chargerId = :chargerId',
      ExpressionAttributeNames: {
        '#chargerId': 'chargerId'
      },
      ExpressionAttributeValues: {
        ':chargerId': charger_document_Id
      }
    };


    do {
      data = await dynamodb.scan(params).promise();
      connectorItems = connectorItems.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (connectorItems.length > 0) {
      console.log(data);
      connectors = connectorItems;
      const connector = connectors.find(element => element.connector_number == connectorId);
      if (connector) {
        document_Id = connector.id;
        console.log(document_Id);
      }
    }
    console.log(`Database Data returned: ${body} and ${document_Id}`);
  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
  }

  // Update charger status
  let chargerStatus = "Available";
  const isAvailable = connectors.filter(element => element.connectorStatus == "Available").length > 0;
  const isCharging = connectors.filter(element => element.connectorStatus == "Charging").length == connectors.length;
  chargerStatus = isAvailable ? "Available" : isCharging ? "Busy" : "Not Available";
  const isFaulted = connectors.filter(element => element.connectorStatus == "Faulted").length > 0;
  const isAllFaulted = connectors.filter(element => element.connectorStatus == "Faulted").length >= connectors.length;
  if (isAllFaulted) chargerStatus = "Faulted";
  console.log('chargerStatus', chargerStatus);

  const variables = {
    id: charger_document_Id,
    status: chargerStatus,
    faulted: isFaulted,
  };

  // GraphQL mutation
  const query = /* GraphQL */ `
    mutation MyMutation($id: ID!, $status: String!, $faulted: Boolean!) {
      updateCharger(input: {
        id: $id,
        status: $status,
        faulted: $faulted,
      }) {
        id
              chargingPointId
              last_heart_beat
              service_date
              status
              pricePerKW
              manufactId
              capacityId
              stationId
              stationName
              simNo
              simCompany
              serialNo
              faulted
              invoicePrefix
              invoiceNo
              currentFinYear
              createdAt
              updatedAt
      }
    }`;

  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    console.log("IN TRY");
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    console.log(body);
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    console.log(`MUTATION ERROR: ${error}`);
  }
  await updateStationStatus(stationId);
  console.log("EXIT............");
  return {
    statusCode,
    body: JSON.stringify(body)
  };
};


async function updateStationStatus(staionId) {
  try {
    console.log(staionId);
    let chargers = [];
    let items = [];
    const params = {
      TableName: 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod',
      FilterExpression: '#stationId = :stationId',
      ExpressionAttributeNames: {
        '#stationId': 'stationId'
      },
      ExpressionAttributeValues: {
        ':stationId': staionId
      }
    };


    do {
      let data = await dynamodb.scan(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      // console.log(data);
      chargers = items;
    }
    let stationStatus = "Available";
    const isAvailable = chargers.filter(element => element.status == "Available").length > 0;
    const isCharging = chargers.filter(element => element.status == "Charging" || element.status == "Preparing").length == chargers.length;
    stationStatus = isAvailable ? "Available" : isCharging ? "Busy" : "Not Available";
    const isAllFaulted = chargers.filter(element => element.status == "Faulted").length >= chargers.length;
    if (isAllFaulted) stationStatus = "Faulted";
    console.log("stationStatus", stationStatus);

    const variables = {
      id: staionId,
      status: stationStatus,
    };

    // GraphQL mutation
    const query = /* GraphQL */ `
    mutation MyMutation($id: ID!, $status: String!) {
      updateStation(input: {
        id: $id,
        status: $status,
      }) {
        id
        station_name
        latitude
        longitude
        landmark
        pincode
        contact_no
        email
        geoState
        city
        active
        cpoId
        address
        igst
        status
        last_heart_beat
        createdAt
        updatedAt
      }
    }`;

    const options = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables })
    };

    const request = new Request(GRAPHQL_ENDPOINT, options);

    try {
      console.log("IN TRY station status");
      let response = await fetch(request);
      console.log(response);
    } catch (error) {
      console.log(`MUTATION ERROR: ${error}`);
    }
  } catch (error) {
    console.log('Error reading data from DynamoDBss:', error);
  }
}