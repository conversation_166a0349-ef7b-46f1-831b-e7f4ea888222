import { default as fetch } from 'node-fetch';
import AWS from 'aws-sdk';

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  let statusCode = 200;
  let body;

  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);

    const data = event;
    // const data = JSON.parse(event.body);

    // Access the first record in the event
    const bookingId = data.bookingId;
    let items = [];
    const chargeParams = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#booking_id = :booking_id', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#booking_id': 'booking_id'
      },
      ExpressionAttributeValues: {
        ':booking_id': bookingId
      }
    }

    // Call DynamoDB get operation to read data for the specified ID
    do {
      const chargeData = await dynamodb.scan(chargeParams).promise();
      console.log("charge", chargeData);
      items = items.concat(chargeData.Items);
      chargeParams.ExclusiveStartKey = chargeData.LastEvaluatedKey;
    } while (chargeParams.ExclusiveStartKey);

    if (items.length > 0) {
      const chargeDataMap = items[0];

      const uId = data.uId;
      console.log(`New transaction: ${bookingId} ${uId}`);

      const response = await fetch("https://2iaceon3qy2pmmdcjogq6k7bia0iedck.lambda-url.ap-south-1.on.aws/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          bookingId: chargeDataMap.id,
          user_id: chargeDataMap.user_id,
          userName: chargeDataMap.userName,
          userContact: chargeDataMap.userContact,
          userId: uId,
          pg: "PhonePe",
        }),
      });

      if (response.status === 200) {
        return true;
      } else {
        return response.json();
      }
    }

  } catch (error) {
    console.error('Error occurred:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          message: error.message,
          stack: error.stack,
        },
      ],
    };
  }

  return {
    statusCode,
    body: JSON.stringify(body || { message: 'Success' }),
  };
};