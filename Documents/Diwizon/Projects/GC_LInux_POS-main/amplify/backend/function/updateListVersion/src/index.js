import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

let id, chargerId, listVersion, data;

export const handler = async (event) => {

  console.log(`EVENT: ${JSON.stringify(event)}`);

  let body = {};  // Initialize `body`
  let statusCode = 200;  // Default status code

  chargerId = event.CP_ID;
  listVersion = event.listVersion;

  try {
    let items = [];
    const params = {
      TableName: 'LocalList-r6cw5zqo7zb37hhq7w4ympiugy-prod',
      FilterExpression: '#chargingPointId = :chargingPointId',
      ExpressionAttributeNames: {
        '#chargingPointId': 'chargingPointId'
      },
      ExpressionAttributeValues: {
        ':chargingPointId': chargerId
      }
    };


    do {
      data = await dynamodb.scan(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      console.log(data);
      body = items[0];
      id = body.id;
      await updateListVersion(id, chargerId, listVersion);
    } else {
      await createListVersion(chargerId, listVersion);
    }

  } catch (error) {
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = { error: 'Could not process the request' };
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};

async function createListVersion(chargerId, listVersion) {
  const variables = {
    chargingPointId: chargerId,
    listVersion: listVersion
  };

  const query = `
  mutation MyMutation($chargingPointId: String!, $listVersion: String!) {
    createLocalList(input: {
      chargingPointId: $chargingPointId,
      listVersion: $listVersion,
    }) {
      chargingPointId
      listVersion
    }
  }`;

  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    let response = await fetch(request);
    let responseBody = await response.json();  // Parse the response body
    console.log(responseBody);
  } catch (error) {
    console.log(`MUTATION ERROR: ${error}`);
  }
}

async function updateListVersion(id, chargerId, listVersion) {
  let currDateTime = new Date().toISOString();

  const variables = {
    id: id,
    chargingPointId: chargerId,
    listVersion: listVersion,
    updatedTimeStamp: currDateTime
  };

  const query = `
    mutation MyMutation($id: ID!, $chargingPointId: String!, $listVersion: String!, $updatedTimeStamp: AWSDateTime) {
      updateLocalList(input: {
        id: $id,
        chargingPointId: $chargingPointId,
        listVersion: $listVersion,
        updatedTimeStamp: $updatedTimeStamp
      }) {
        id
        chargingPointId
        listVersion
        updatedTimeStamp
      }
    }`;

  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    let response = await fetch(request);
    let responseBody = await response.json();  // Parse the response body
    console.log(responseBody);
  } catch (error) {
    console.log(`MUTATION ERROR: ${error}`);
  }
}
