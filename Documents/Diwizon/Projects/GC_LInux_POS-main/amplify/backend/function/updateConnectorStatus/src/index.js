import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let connectorStatus;
  let document_Id;
  try {
    // Extract the ChargePoint ID from the event object
    let { CP_ID } = event;
    connectorStatus = event.status;
    let connectorId = event.connectorId;

    console.log(`Charger ID and other attributes: ${CP_ID} and ${connectorId}`);

    let chargerItems = [];
    const chargerParams = {
      TableName: 'Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#chargingPointId = :chargingPointId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#chargingPointId': 'chargingPointId',
      },
      ExpressionAttributeValues: {
        ':chargingPointId': CP_ID,
      }
    };

    // Call DynamoDB get operation to read data for the specified ID


    do {
      data = await dynamodb.scan(chargerParams).promise();
      chargerItems = chargerItems.concat(data.Items);
      chargerParams.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (chargerParams.ExclusiveStartKey);


    // Return the retrieved data
    /*return {
        statusCode: 200,
        body: JSON.stringify(data.Item)
    };*/
    let charger_document_Id;
    if (chargerItems.length > 0) {
      console.log(data);
      body = chargerItems[0];
      charger_document_Id = body.id;
    }
    // Call DynamoDB get operation to read data for the specified ID

    // Define parameters for DynamoDB get operation
    let items = [];
    const params = {
      TableName: 'Connector-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#chargerId = :chargerId AND #connectorId = :connectorId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#chargerId': 'chargerId',
        '#connectorId': 'connector_number',
      },
      ExpressionAttributeValues: {
        ':chargerId': charger_document_Id,
        ':connectorId': connectorId,
      }
    };



    do {
      data = await dynamodb.scan(params).promise();
      items = items.concat(data.Items);
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    } while (params.ExclusiveStartKey);

    if (items.length > 0) {
      console.log(data);
      body = items[0];
      document_Id = body.id;
    }
    console.log(`Database Data returned: ${body} and ${document_Id}`);
  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }


  const variables = {
    id: document_Id,
    connectorStatus: connectorStatus,
  };


  //Get values from Event and Charge Table to update the connector status
  const query = /* GraphQL */`
    mutation MyMutation($id: ID!, $connectorStatus: String!) {
  updateConnector(input: {  # Add the input argument here
    id: $id,
    connectorStatus: $connectorStatus,
  }) {
    id
    chargerId
    isActive
    connector_number
    connectorStatus
    typeId
    createdAt
    updatedAt
  }
}`;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);



  try {
    console.log("IN TRY");
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    console.log(body);
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.log(`MUTATION ERROR: ${body} and ${error}`)
  }

  console.log("EXIT............");
  return {
    statusCode,
    body: JSON.stringify(body)
  };
};