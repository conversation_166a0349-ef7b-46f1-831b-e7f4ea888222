import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

const query = /* GraphQL */ `
mutation MyMutation {
  createStation(
    input: {station_name: "APPPSYNC", pincode: "2321e1rrv", latitude: "23.23", longitude: "34.34", landmark: "qeqrqergrqttqt5", id: "qsdscc143r143wefw3" }
  ) {
    id
    landmark
    latitude
    longitude
    pincode
    createdAt
    franchiseownerID
    updatedAt
    station_name
  }
}

`;

// const query = /* GraphQL */ `
//   mutation CREATE_TODO($input: CreateTodoInput!) {
//     createTodo(input: $input) {
//       id
//       name
//       createdAt
//     }
//   }
// `;

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  const variables = {
    input: {
      name: 'Hello, Todo!'
    }
  };

  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  let statusCode = 200;
  let body;
  let response;

  try {
    response = await fetch(request);
    body = await response.json();
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};