import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import razorpay from 'razorpay';


const GRAPHQL_ENDPOINT = process.env.API_GOCHARGE_GRAPHQLAPIENDPOINTOUTPUT;
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;

// This razorpayInstance will be used to 
// access any resource from razorpay
const keyId = "***********************"; // "rzp_test_cn6p8HqmFvpWSW"
const keySecret = "2IsMZvfk9kPz2iB4loELYUfj"; // "kouKRBYkLFI51GTS9ntQUX8f"
const razorpayInstance = new razorpay({
  key_id: keyId,
  key_secret: keySecret
});

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

export const handler = async (event) => {
  try {
    const data = event.body;
    console.log(data);
    const body = JSON.parse(data);
    const resp = await razorpayInstance.orders.create({
      "amount": body['amount'],
      "currency": "INR",
      "receipt": body['receipt'],
      "notes": {
        "bId": body['bId'],
        "uId": body['uId'],
        "type": body['type']
      },
    });
    if (resp) {
      console.log(resp);
    }
    return resp;
  } catch (error) {
    console.log("Error creating order", error);
    return {
      body: JSON.stringify(error)
    };
  }
};