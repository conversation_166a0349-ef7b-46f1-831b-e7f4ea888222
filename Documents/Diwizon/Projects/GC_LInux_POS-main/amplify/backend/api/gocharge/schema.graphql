type SupportRequest @model @auth(rules: [{allow: public}]) {
  id: ID!
  category: String
  subCategory: String
  chargingStation: String
  remarks: String
  adminRemarks: String
  status: String
  code: String
  canCall: Boolean
  uId: String
  userName: String
  userContact: String
}

type RefundRecord @model @auth(rules: [{allow: public}]) {
  id: ID!
  refunded: Boolean
  merchantId: String
  merchantTransactionId: String
  transactionId: String
  amount: String
  state: String
  responseCode: String
  paymentInstrument: AWSJSON
  code: String
  message: String
  uId: String
  userName: String
  userContact: String
}

type PaymentData @model @auth(rules: [{allow: public}]) {
  id: ID!
  bId: String
  uId: String
  type: String
}

type LocalList @model @auth(rules: [{allow: public}]) {
  id: ID!
  chargerId: String
  chargingPointId: String
  listVersion: String
  updatedTimeStamp: AWSDateTime
  list: [AWSJSON]
}

type AdminUsers @model @auth(rules: [{allow: public}]) {
  id: ID!
  isCPO: Boolean
  uId: String
  phone: String
  name: String
  permits: [String]
}

type GoCaLogs @model @auth(rules: [{allow: public}]) {
  id: ID!
  cpId: String
  expireAt: Int
  # TimeStamp: AWSDateTime
  # Event: String
  Log: String
  logSequence: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type CPO @model @auth(rules: [{allow: public}]) {
  id: ID!
  name: String!
  taxId: String
  city: String!
  geoState: String!
  contact: String!
  email: String!
  joiningDate: AWSDateTime
  invoiceNo: Int
  invoicePrefix: String
  currentFinYear: AWSDateTime
  quickChargeLimit: Float
}

type Taxes @model @auth(rules: [{allow: public}]) {
  id: ID!
  gstno: String!
  geoState: String!
  rate: Float!
  cinNo: String
  hsn: String
}

type ChargeSessionHis @model @auth(rules: [{allow: public}]) {
  id: ID!
  eventType: String
  eventTime: AWSDateTime
}

type Connector @model @auth(rules: [{allow: public}]) {
  id: ID!
  connector_number: Int!
  isActive: Boolean
  connectorStatus: String
  typeId: String!
  chargerId: String!
}

type Charger @model @auth(rules: [{allow: public}]) {
  id: ID!
  chargingPointId: String!
  last_heart_beat: AWSDateTime
  service_date: AWSDate
  status: String
  pricePerKW: Float!
  manufactId: String
  capacityId: String!
  stationId: String!
  stationName: String
  simNo: String
  simCompany: String
  serialNo: String
  faulted: Boolean
  invoicePrefix: String
  invoiceNo: Int
  currentFinYear: AWSDateTime
}

type ConnectorType @model @auth(rules: [{allow: public}]) {
  id: ID!
  name: String!
  image: String
}

type ChargerManufacturer @model @auth(rules: [{allow: public}]) {
  id: ID!
  name: String!
  is_preffered: Boolean
}

type LocationType @model @auth(rules: [{allow: public}]) {
  id: ID!
  place: String
  is_active: Boolean
}

type InLog @model @auth(rules: [{allow: public}]) {
  id: ID!
  data: AWSJSON
  date: AWSDate
  charger_id: String
}

type ChargerCapacity @model @auth(rules: [{allow: public}]) {
  id: ID!
  charger_capacity: Float!
  perKW: Boolean!
  isDC: Boolean!
}

type RFIDSchema @model @auth(rules: [{allow: public}]) {
  id: ID!
  rfidValue: String!
  expires: AWSDate!
  isactive: Boolean
  userId: String!
  vehical_number: String
  favs: [String]
}

type PaymentRequestTable @model @auth(rules: [{allow: public}]) {
  id: ID!
  gateway: String
  emailId: String
  phoneNo: String
  customerId: String
  OrderId: String
  transactionAmount: String
  callbackurl: String
}

type ChargingTable @model @auth(rules: [{allow: public}]) {
  id: ID!
  booking_id: String
  start_time: AWSDateTime
  end_time: AWSDateTime
  status: String
  connector_no: Int
  CurrentMeterWatt: Int
  city: String
  charging_fee: Float
  payment_status: String
  createdAt: AWSDateTime
  tax_amount: Float
  vehical_number: String
  chargePointId: String
  user_id: String
  station_id: String
  vehicle_id: String
  charging_percent: String
  MeterStartWatt: Int
  MeterEndWatt: Int
  booking_type: String
  compareValue: String
  pricePerKw: Float
  geoState: String
  isPaid: Boolean
  chargerId: String
  transactionId: Int
  estimatedDuration: Float
  estimatedUnits: Float
  startedAtPercent: Int
  stopedAtPercent: Int
  unitsBurned: Float
  costOfConsump: Float
  refundedAmount: Float
  startedAtTime: AWSDateTime
  stopedAtTime: AWSDateTime
  amountFromWallet: Float
  transDocId: ID
  payment_Id: String
  paymentTime: AWSDateTime
  lastCommand: String
  gstin: String
  gstName: String
  userName: String
  userContact: String
  overchargeDueCleared: Boolean
  invoiceNo: Int
  dueAmount: Float
  stationName: String
  cpoName: String
  cpoId: String
  taxPercent: Float
  isIgst: Boolean
  igstAmount: Float
  sgstAmount: Float
  cgstAmount: Float
  baseAmount: Float
  invoiceId: String
  rfid: String
  pgTransRef: String
}

type ManufacturerTable @model @auth(rules: [{allow: public}]) {
  id: ID!
  manufacturer_name: String!
  manufacturer_type: String
  status: Boolean
  contact_person: String
  contact_number: String
  email_id: String
  contact_person_dept: String
  address: String
  country: String
  geoState: String
  city: String
  postcode: String
  contact_person_designation: String
  is_preferred: String
}

type Issues @model @auth(rules: [{allow: public}]) {
  id: ID!
  category: String
  sub_category: String!
  priority: String!
  description: String!
  mobile_no: String!
  attachments: String!
  status: Boolean!
  operator_name: String!
  userId: String
}

type Configuration @model @auth(rules: [{allow: public}]) {
  id: ID!
  configs: [AWSJSON]
  cpId: String
}

type Transaction @model @auth(rules: [{allow: public}]) {
  # id: ID! @index(
  #     name: "transactionByDate"
  #     queryField: "transactionByDate"
  #     sortKeyFields: ["createdAt"])
  id: ID!
  amount: Float
  method: String
  reason: String
  bookingId: String
  uId: String
  dateTime: AWSDateTime
  transRef: String
  pgTransRef: String
  status: String
  walletAmountUsed: Float
  currentBalance: Float
  userName: String
  userContact: String
  note: String
}

type Vehicle @model @auth(rules: [{allow: public}]) {
  id: ID!
  model: String!
  type: String
  manufactId: String!
  batteryCapacity: String!
}

type Station @model @auth(rules: [{allow: public}]) {
  id: ID!
  station_name: String!
  latitude: String!
  longitude: String
  landmark: String
  pincode: String
  contact_no: String
  email: String
  geoState: String!
  city: String!
  active: Boolean
  cpoId: String
  address: String
  igst: Boolean
  status: String
  last_heart_beat: AWSDateTime
}

type UserVehicle @model @auth(rules: [{allow: public}]) {
  id: ID!
  vehical_name: String!
  vehical_number: String!
  connector_type: String
  userId: String!
  batteryCapacity: String
}

enum WalletType {
  DEBIT
  CREDIT
}

type EndUser @model @auth(rules: [{allow: public}]) {
  id: ID! 
  user_fullname: String!
  dob: AWSDate
  joining_date: AWSDate
  email: String
  contact: String!
  balance: Float
  default_vehicle_id: String
  favs: [String]
  uId: String!
  deviceId: String
}

type DueRequest @model @auth(rules: [{allow: public}]) {
  id: ID!
  amount: Float
  uId: String
  dues: [String]
} 