{"prod": {"awscloudformation": {"AuthRoleName": "amplify-gocharge-prod-763eb-authRole", "UnauthRoleArn": "arn:aws:iam::339713123974:role/amplify-gocharge-prod-763eb-unauthRole", "AuthRoleArn": "arn:aws:iam::339713123974:role/amplify-gocharge-prod-763eb-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "UnauthRoleName": "amplify-gocharge-prod-763eb-unauthRole", "StackName": "amplify-gocharge-prod-763eb", "StackId": "arn:aws:cloudformation:ap-south-1:339713123974:stack/amplify-gocharge-prod-763eb/c55bbc70-6092-11ef-9fc8-021c39e9f385", "AmplifyAppId": "d23ocol71b6rw5"}, "categories": {"auth": {"gocharge": {}}, "function": {"autoCancelBooking": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/autoCancelBooking-7866514e7a5770646342-build.zip"}, "cancelCharging": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/cancelCharging-754d6278674b765a3570-build.zip"}, "chargeSessionLambda": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/chargeSessionLambda-486f5a4149503841777a-build.zip"}, "createPreBooking": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/createPreBooking-654767756b4148584877-build.zip"}, "deleteOldLogs": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/deleteOldLogs-53755364494b71714457-build.zip"}, "getChargerCount": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/getChargerCount-454b53552f73616f704c-build.zip"}, "getPaymentDetails": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/getPaymentDetails-37544478705133554d58-build.zip"}, "gochargenpmLayer": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/gochargenpmLayer-LambdaLayerVersionb86f276f-build.zip"}, "insertLogs": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/insertLogs-6c324f57636d61354679-build.zip"}, "issueLamda": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/issueLamda-66614e4670477a4e3864-build.zip"}, "postTester": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/postTester-444265686e5863307046-build.zip"}, "razorOrder": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/razorOrder-39386e527237395a4b64-build.zip"}, "refundEvents": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/refundEvents-4877644a4e52582b4676-build.zip"}, "refundOrder": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/refundOrder-65716235433862715247-build.zip"}, "updateChargePercent": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateChargePercent-4c52614b644c77314d52-build.zip"}, "updateChargerStatus": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateChargerStatus-6d697241575437696f66-build.zip"}, "updateConfiguration": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateConfiguration-5631747048636b334736-build.zip"}, "updateConnectorStatus": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateConnectorStatus-4e35343232763067326d-build.zip"}, "updateLastHeartbeat": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateLastHeartbeat-7036362b466471703462-build.zip"}, "updateListVersion": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateListVersion-32653743723653723855-build.zip"}, "updateMeterStopValue": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateMeterStopValue-5a517761367739714d69-build.zip"}, "updateMeterValues": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateMeterValues-5764413931435032536d-build.zip"}, "updateTransId": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/updateTransId-4443486946446d476b69-build.zip"}, "verifyPayment": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/verifyPayment-2b54622b65477a503230-build.zip"}, "checkRfid": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/checkRfid-7861414a7646755a6542-build.zip"}, "verifyPayout": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/verifyPayout-495347785961756d2b4c-build.zip"}, "bookingCheckSchedular": {"deploymentBucketName": "amplify-gocharge-prod-763eb-deployment", "s3Key": "amplify-builds/bookingCheckSchedular-304a3773736870755645-build.zip"}}, "api": {"gocharge": {}}}}}