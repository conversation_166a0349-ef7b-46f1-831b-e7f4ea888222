/* import 'package:flutter/material.dart';
import 'package:go_charge/views/intro_pages/welcome_pages.dart';

class Splashscreen extends StatefulWidget {
  const Splashscreen({super.key});

  @override
  State<Splashscreen> createState() => _SplashscreenState();
}

class _SplashscreenState extends State<Splashscreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration(seconds: 1), () {
      Navigator.pushReplacement(context,
          MaterialPageRoute(builder: (context) => const IntroScreen()));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffE1FFE4),
      body: Container(
        height: double.infinity,
        width: double.infinity,
        child: SizedBox(child: Image.asset("assets/Logo.png")),
      ),
    );
  }
}
 */