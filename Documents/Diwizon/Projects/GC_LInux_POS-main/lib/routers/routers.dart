import 'package:flutter/material.dart';
import 'package:go_charge/models/ChargingTable.dart';
import 'package:go_charge/models/Station.dart';
import 'package:go_charge/models/Vehicle.dart';
import 'package:go_charge/views/complaints/new_complaint.dart';
import 'package:go_charge/views/other/qr_sacnner.dart';
import 'package:go_charge/views/Account/transactions.dart';
import 'package:go_charge/views/Auth/login.dart';
import 'package:go_charge/views/Vehicle/save_vehicle.dart';
import 'package:go_charge/views/charging/charging_page.dart';
import 'package:go_charge/views/complaints/complaints.dart';
import 'package:go_charge/views/dues/dues.dart';
import 'package:go_charge/views/search/search_page.dart';
import 'package:go_charge/views/station/checkout.dart';
import 'package:go_charge/views/terms/terms.dart';
import '../models/Charger.dart';
import '../models/Connector.dart';
import '../models/UserVehicle.dart';
import '../views/Auth/verification.dart';
import '../views/charge_history/charge_history.dart';
import '../views/station/detail_page.dart';

class AppRoutes {
  // LOGIN
  static goToLoginPage(BuildContext context) => Navigator.pushReplacement(
      context, MaterialPageRoute(builder: (context) => const LoginPage()));
  static pushLoginPage(BuildContext context, {String? popTillName}) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => LoginPage(popTillName: popTillName)));

  // OTP
  static goToOtpPage(BuildContext context,
          {String? popTillName}) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => OtpPage(popTillName: popTillName)));

  // GLOBAL VEHICLES LISTING || ADD NEW VEHICLE
  static Future<Vehicle?> goToAddNewVehicle(BuildContext context) async =>
      await Navigator.push<Vehicle?>(context,
          MaterialPageRoute(builder: (context) => const AddNewVehiclePage()));

  // STATION
  static goToStationDetails(BuildContext context, Station station) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              settings: const RouteSettings(name: "details"),
              builder: (context) => DetailPage(station: station)));

  // STATION
  static popAndGoToStationDetails(BuildContext context,
          {required Station station,
          String? chargingPointId,
          String? connectorNo}) =>
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              settings: const RouteSettings(name: "details"),
              builder: (context) => DetailPage(
                  station: station,
                  chargingPointId: chargingPointId,
                  connectorNo: connectorNo)));

  // QR CODE SCANNER
  // static goToQRScanner(BuildContext context) => Navigator.push(
  //     context, MaterialPageRoute(builder: (context) => const QRCodeScanner()));

  // SEARCH PAGE
  static Future goToSearch(BuildContext context) async => await Navigator.push(
      context, MaterialPageRoute(builder: (context) => const SearchPage()));

  // CHARGING DETAILS
  static goToChargingDetails(BuildContext context, String bId,
          {bool popTillFirst = true}) =>
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
            builder: (context) => ChargingPage(bookingDocId: bId)),
        (route) => popTillFirst ? route.isFirst : true,
      );

  // ALLTRANSACTIONS
  static goToAllTransactions(BuildContext context) => Navigator.push(context,
      MaterialPageRoute(builder: (context) => const Alltransactions()));

  // COMPALINTS
  static goToComplaints(BuildContext context) => Navigator.push(
      context,
      MaterialPageRoute(
          settings: const RouteSettings(name: "complaint"),
          builder: (context) => const ComplaintsPage()));

  // NEW COMPLAINT
  static Future goToNewComplaint(BuildContext context) async =>
      await Navigator.push(context,
          MaterialPageRoute(builder: (context) => const NewComplaint()));

  // CHARGING HISTORY
  static goToChargingHistory(BuildContext context) => Navigator.push(context,
      MaterialPageRoute(builder: (context) => const ChargingHistory()));

  // TERMS PAGE
  static goToTermsPage(BuildContext context) => Navigator.push(
      context, MaterialPageRoute(builder: (context) => const TermsPage()));

  // CHECKOUT PAGE
  static goToCheckout(
    BuildContext context, {
    required Station station,
    required UserVehicle? selectedVehicle,
    required Charger? selectedCharge,
    required Connector? selectedConnector,
    required num taxPercent,
    required num estimatedTime,
    required num estimatedUnits,
    required num estimatedAmount,
    required Function(num amountFromWallet) paymentCallback,
    required Function walletCallback,
    required TextEditingController gstNoCtrl,
    required TextEditingController gstNameCtrl,
    required TextEditingController contactCtrl,
  }) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              settings: const RouteSettings(name: "checkout"),
              builder: (context) => CheckoutPage(
                    station: station,
                    selectedCharge: selectedCharge,
                    selectedConnector: selectedConnector,
                    estimatedTime: estimatedTime,
                    estimatedUnits: estimatedUnits,
                    estimatedAmount: estimatedAmount,
                    selectedVehicle: selectedVehicle,
                    paymentNowCallback: paymentCallback,
                    walletCallback: walletCallback,
                    taxPercent: taxPercent,
                    gstNoCtrl: gstNoCtrl,
                    gstNameCtrl: gstNameCtrl,
                    contactCtrl: contactCtrl,
                  )));

  // OVERCHARGER
  static goToViewDues(BuildContext context, List<ChargingTable> chargingList) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => DuesPage(list: chargingList)));

  // ===================== NAVIGAOR ===================== //
  static popCurrennt(BuildContext context) =>
      Navigator.popUntil(context, (route) => route.isCurrent);

  static popAll(BuildContext context) =>
      Navigator.of(context).popUntil((route) => route.isFirst);

  static popTill(BuildContext context, String routeName, [int maxPop = 3]) {
    int trialCount = 0;
    Navigator.of(context).popUntil((route) {
      Future.delayed(const Duration(milliseconds: 10));
      trialCount++;
      return route.settings.name == routeName || trialCount > maxPop;
    });
  }

  static pop(BuildContext context) => Navigator.of(context).pop();
}
