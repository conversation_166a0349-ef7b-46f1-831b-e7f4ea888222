import 'dart:async';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;

class VehiclesCtrl extends GetxController {
  UserVehicle? defauldVehicle;
  List<UserVehicle> myVehicles = [];
  List<Vehicle> globalVehicles = [];
  List<ManufacturerTable> manufacturers = [];
  StreamSubscription<amplify.GraphQLResponse<UserVehicle>>? myVehicleStream;
  bool vehicleSynced = false;

  getAllVehiclesData() {
    getGlobalVehicles();
    getManufacturer();
  }

  getMyVehicles() async {
    final request = amplify.ModelQueries.list(
      UserVehicle.classType,
      where: UserVehicle.USERID.eq(Get.find<AppCtrl>().currentAuthUser?.userId),
    );
    final response = await Amplify.API.query(request: request).response;
    myVehicles = response.data?.items.whereType<UserVehicle>().toList() ?? [];
    myVehicleObserver();
    update();
  }

  myVehicleObserver() async {
    final subscriptionRequest =
        amplify.ModelSubscriptions.onUpdate(UserVehicle.classType);
    // Stream
    final Stream<amplify.GraphQLResponse<UserVehicle>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    myVehicleStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received myvehicle: ${event.data}');
        if (event.data != null) {
          myVehicles[myVehicles.indexWhere(
              (element) => element.id == event.data?.id)] = event.data!;
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  Future setDefaultVehicle(UserVehicle vehicle) async {
    try {
      defauldVehicle = vehicle;
      update();
      safePrint("Setting Default Vehicle...");
      final userData = Get.find<AppCtrl>()
          .currentUserData
          ?.copyWith(default_vehicle_id: vehicle.id);
      if (userData != null) {
        final request = amplify.ModelMutations.update(userData);
        await Amplify.API.mutate(request: request).response;
      }
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
      return null;
    }
  }

  Future<List<Vehicle>?> getGlobalVehicles() async {
    try {
      safePrint("Fethcing Global Vehicles...");
      final request = ModelQueries.list(Vehicle.classType);
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Vehicless Found: ${result.data?.items.length}');
      globalVehicles = result.data?.items.whereType<Vehicle>().toList() ?? [];
      update();
      return globalVehicles;
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
      return null;
    }
  }

  Future<List<ManufacturerTable>?> getManufacturer() async {
    try {
      safePrint("Fethcing Global Vehicles...");
      final request = ModelQueries.list(ManufacturerTable.classType);
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Manufacturers: ${result.data?.items.length}');
      // manufacturers = result;
      manufacturers =
          result.data?.items.whereType<ManufacturerTable>().toList() ?? [];
      update();
      return manufacturers;
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
      return null;
    }
  }

  Future addToMyVehicles(Vehicle newVehicle, String gaadiNo) async {
    try {
      safePrint("Adding to my vehicles...");
      final ownersVehicle = UserVehicle(
          vehical_name: newVehicle.model,
          vehical_number: gaadiNo,
          batteryCapacity: newVehicle.batteryCapacity,
          userId: Get.find<AppCtrl>().currentAuthUser?.userId ?? "");
      final request = ModelMutations.create(ownersVehicle);
      final res = await Amplify.API.query(request: request).response;
      if (res.data != null) {
        myVehicles.add(res.data!);
        update();
      }
      // getMyVehicles();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong adding new vehicle: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong adding new vehicle: $e');
      return null;
    }
  }

  Future deleteFromMyVehicles(UserVehicle userVehicle) async {
    try {
      safePrint("Deleting from my vehicles...");
      final request = ModelMutations.delete(userVehicle);
      final res = await Amplify.API.query(request: request).response;
      if (res.data != null) {
        myVehicles.remove(res.data!);
        update();
      }
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong deleting vehicle: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong deleting vehicle: $e');
      return null;
    }
  }
}
