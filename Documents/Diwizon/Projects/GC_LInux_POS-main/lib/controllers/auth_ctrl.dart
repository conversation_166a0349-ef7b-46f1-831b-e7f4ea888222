import 'dart:async';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/configuration/configs.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/snackbar.dart';
import 'package:hive/hive.dart';
import '../views/Auth/signup_otp_dialog.dart';

const alwaysPass = "12345678";
// bool signedUpNow = false;

class AuthCtrl extends GetxController {
  bool? loggedIn;
  RxBool loding = false.obs;
  RxBool verifying = false.obs;
  final phoneCtrl = TextEditingController(text: "");
  // final phoneCtrl = TextEditingController(text: "+916353817704");
  // final phoneCtrl = TextEditingController(text: "+919340421152");
  final otpCtrl = TextEditingController();
  final passCtrl = TextEditingController();
  late StreamSubscription<AuthHubEvent> authStream;

  @override
  void onInit() {
    super.onInit();
    authStateStream();
    checkAuthSession();
  }

  Future<bool?> isLoggedIn() async {
    try {
      return localChargerId == (await Amplify.Auth.getCurrentUser()).userId &&
          localChargerId != null &&
          loggedIn == true;
    } catch (e) {
      return false;
    }
  }

  Future<bool?> checkLogin(BuildContext context) async {
    final loggedIn = isLoggedIn();
    AppRoutes.pushLoginPage(context);
    return loggedIn;
  }

  checkAuthSession() async {
    final session = await Amplify.Auth.fetchAuthSession();
    loggedIn = session.isSignedIn;
    update();
  }

  authStateStream() async {
    authStream = Amplify.Hub.listen(HubChannel.Auth, (event) {
      debugPrint(event.type.toString());
      loggedIn = event.type == AuthHubEventType.signedIn;
      update();
      onAuthStateChange(event);
    });
  }

  onAuthStateChange(AuthHubEvent event) async {
    switch (event.type) {
      case AuthHubEventType.signedIn:
        safePrint('Signed In...');
        try {
          Get.find<AppCtrl>().setUpInitialData();
        } catch (e) {
          debugPrint(e.toString());
        }
        break;
      case AuthHubEventType.signedOut:
        safePrint('Signed Out...');
        break;
      default:
    }
  }

  Future<AuthUser> getCurrentUser() async {
    final user = await Amplify.Auth.getCurrentUser();
    debugPrint(user.username);
    return user;
  }

/* 
  signIn(BuildContext context,
      {bool directSignIn = false, String? popTillName}) async {
    try {
      debugPrint("SIGNING IN...");
      FocusScope.of(context).unfocus();
      // Amplify.Auth.signOut();
      // return;
      loding.value = true;
      // Amplify.Auth.del
      //  Amplify.Auth.confirmUserAttribute(userAttributeKey: , confirmationCode: confirmationCode)
      // if (!directSignIn) {
      //   loding.value = false;
      //   final res = await Amplify.Auth.sendUserAttributeVerificationCode(
      //       options: SendUserAttributeVerificationCodeOptions(
      //           pluginOptions:
      //               CognitoSendUserAttributeVerificationCodePluginOptions
      //                   .fromJson({})),
      //       userAttributeKey: AuthUserAttributeKey.phoneNumber);
      //   print(res.codeDeliveryDetails.destination);
      //   if (context.mounted) AppRoutes.goToOtpPage(context);
      //   return;
      // }
      // final result = await Amplify.Auth.sendUserAttributeVerificationCode(
      //     userAttributeKey: AuthUserAttributeKey.phoneNumber);
      // return;
      SignInResult result = await Amplify.Auth.signIn(
          username: phoneNo(),
          // username: phoneCtrl.text,
          password: alwaysPass);

      // return;
      if (result.isSignedIn) {
        // X // WILL NEVER SATISFY - MFA IS SET TO REQUIRED HERE
        debugPrint("SIGN IN SUCCESSFUL :)");
        // Get OTP via sendUserAttributeVerificationCode()
        // if (true) {
        if (directSignIn) {
          setLocalUserId();
        } else {
          if (context.mounted) {
            sendOtpAndGoToOtpPage(context, popTillName: popTillName);
          }
        }
      } else if (result.nextStep.signInStep ==
          AuthSignInStep.confirmSignInWithSmsMfaCode) {
        debugPrint("MFA required!");
        // GOTO OTP PAGE
        loding.value = false;
        if (context.mounted) {
          AppRoutes.goToOtpPage(context, popTillName: popTillName);
        }
      } else if (result.nextStep.signInStep == AuthSignInStep.confirmSignUp) {
        debugPrint("SignUp required!");
        // NEED TO SIGNUP NEW USER
        if (context.mounted) signUp(context);
      } else {
        debugPrint("SIGN IN UNSUCCESSFUL!");
      }
      loding.value = false;
    } on UserNotFoundException catch (error) {
      debugPrint("USER NOT FOUND! ${error.message}");
      debugPrint("SignUp required!");
      // NEED TO SIGNUP NEW USER
      if (context.mounted) signUp(context);
    } on InvalidStateException catch (error) {
      debugPrint('Error in signIn InvalidStateException: $error');
      // User is loggedIn actually but on Login Page bcause of localChargerId //
      if (context.mounted) {
        sendOtpAndGoToOtpPage(context, popTillName: popTillName);
      }
      loding.value = false;
    } catch (e) {
      debugPrint('Error in signIn: $e');
      loding.value = false;
    }
  }

  confirmSignIn(BuildContext context, {String? popTillName}) async {
    try {
      FocusScope.of(context).unfocus();
      verifying.value = true;
      final result =
          await Amplify.Auth.confirmSignIn(confirmationValue: otpCtrl.text);
      if (result.isSignedIn && context.mounted) {
        loggedIn = true;
        popTillName == null
            ? AppRoutes.popAll(context)
            : AppRoutes.popTill(context, popTillName);
        // AppRoutes.pop(context);
        setLocalUserId();
      }
      verifying.value = false;
      await Future.delayed(Duration.zero);
      update();
    } on InvalidPasswordException catch (error) {
      showAppShackBar(error.message);
      debugPrint("INVALID PASSWORD! ${error.message}");
    } on AuthException catch (error) {
      // showAppShackBar(error.message);
      debugPrint("INVALID PASSWORD! ${error.message}");
      // print(error.underlyingException);
      if (context.mounted) {
        verifyOtpViaConfirmUserAttribute(context, otpCtrl.text,
            popTillName: popTillName);
      }
    } catch (e) {
      debugPrint('Error in confirmSignIn: $e');
      verifying.value = false;
    }
  }
 */
  sendOtpAndGoToOtpPage(BuildContext context, {String? popTillName}) async {
    try {
      debugPrint("In sendOtpAndGoToOtpPage");
      AppRoutes.goToOtpPage(context, popTillName: popTillName);
      await Amplify.Auth.sendUserAttributeVerificationCode(
          userAttributeKey: AuthUserAttributeKey.phoneNumber);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  verifyOtpViaConfirmUserAttribute(
      BuildContext context, String confirmationCode,
      {String? popTillName}) async {
    try {
      debugPrint("In confirmUserAttribute");
      await Amplify.Auth.confirmUserAttribute(
          userAttributeKey: AuthUserAttributeKey.phoneNumber,
          confirmationCode: confirmationCode);
      // setLocalUserId();
      if (!context.mounted) return;
      popTillName == null
          ? AppRoutes.popAll(context)
          : AppRoutes.popTill(context, popTillName);
      loding.value = false;
    } catch (e) {
      loding.value = false;
      debugPrint(e.toString());
    }
  }

/*   signUp(BuildContext context) async {
    try {
      debugPrint("SIGNING UP...");
      FocusScope.of(context).unfocus();
      loding.value = true;
      final result = await Amplify.Auth.signUp(
        username: phoneNo(),
        // username: phoneCtrl.text,
        password: alwaysPass,
        options: SignUpOptions(userAttributes: <AuthUserAttributeKey, String>{
          AuthUserAttributeKey.phoneNumber: phoneNo(),
          // AuthUserAttributeKey.phoneNumber: phoneCtrl.text
        }),
      );
      // print('>> $result');
      if (result.isSignUpComplete) {
        debugPrint("SIGN UP SUCCESSFUL :)");
        debugPrint("NOW NEED TO SIGNIN");
        // WILL NEVER SATISFY - MFA IS SET TO REQUIRED HERE
        if (context.mounted) signIn(context);
      } else if (result.nextStep.signUpStep == AuthSignUpStep.confirmSignUp) {
        debugPrint("MFA required!");
        // SHOW OTP POPUP TO GET SIGNUP OTP // TODO: THIS NEEDS TO BE CHANGED
        if (!context.mounted) return;
        final signUpOtp = await getSignUpOtpFromUser(context);
        // VERIFYING THERE OTP
        if (!context.mounted) return;
        await confirmSignUp(context, signUpOtp ?? "");
      } else {
        debugPrint("SIGN UP UNSUCCESSFUL!");
      }
      loding.value = false;
    } on InvalidPasswordException catch (error) {
      showAppShackBar(error.message);
      debugPrint("INVALID PASSWORD! ${error.message}");
    } catch (e) {
      debugPrint('Error in signUp: $e');
      loding.value = false;
    }
  }
 */
  Future<String?> getSignUpOtpFromUser(BuildContext context) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const SignUpOtpDialog(),
    );
  }
/* 
  Future<bool> confirmSignUp(BuildContext context, String otp) async {
    try {
      FocusScope.of(context).unfocus();
      loding.value = true;
      debugPrint("CONFIRM SIGNING UP...");
      final result = await Amplify.Auth.confirmSignUp(
          username: phoneNo(),
          // username: phoneCtrl.text,
          confirmationCode: otp,
          options: const ConfirmSignUpOptions(
              pluginOptions: CognitoConfirmSignUpPluginOptions()));
      if (result.isSignUpComplete) {
        // signedUpNow = true;
        showAppShackBar("Successfully registered!");
        if (context.mounted) signIn(context, directSignIn: true);

        return true;
      }
      loding.value = false;
      return false;
    } on CodeMismatchException catch (error) {
      debugPrint('INVALID OTP:  $error');
      showAppShackBar("Invalid Verification code!");
      // SHOW OTP POPUP TO GET SIGNUP OTP // TODO: THIS NEEDS TO BE CHANGED
      if (!context.mounted) return false;
      final signUpOtp = await getSignUpOtpFromUser(context);
      // VERIFYING THERE OTP
      if (!context.mounted) return false;
      await confirmSignUp(context, signUpOtp ?? "");
      return false;
    } catch (e) {
      debugPrint(e.toString());
      loding.value = false;
      return false;
    }
  } */

  String phoneNo() {
    return phoneCtrl.text.contains("+91")
        ? phoneCtrl.text
        : '+91${phoneCtrl.text}';
  } /* 

  getTestPassword(BuildContext context) async {
    try {
      final passCtrl = TextEditingController();
      final res = await showDialog(
          context: context,
          builder: (context) => AlertDialog(
                title: const Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Password Verification"),
                    SizedBox(height: 6),
                    Text(
                      "Please, enter password for verification",
                      style: TextStyle(fontSize: 14, letterSpacing: 1.2),
                    ),
                  ],
                ),
                content: TextField(
                  controller: passCtrl,
                  autofocus: true,
                  keyboardType: const TextInputType.numberWithOptions(),
                ),
                actions: [
                  ElevatedButton(
                      onPressed: () => passCtrl.text.isNotEmpty
                          ? Navigator.pop(context, passCtrl.text)
                          : null,
                      child: const Text("Confirm"))
                ],
              ));
      // print(res);
      if (res is String && context.mounted) {
        await signIn(context, directSignIn: true);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  } */
}

setLocalChargerId(String newId) async {
  try {
    final box = await Hive.openBox('sets');
    box.put('localChargerId', newId);
    localChargerId = newId;
    Get.find<AppCtrl>().setUpInitialData();
  } catch (e) {
    debugPrint(e.toString());
  }
}

removeLocalChargerId() async {
  try {
    final box = await Hive.openBox('sets');
    box.put('localChargerId', null);
    localChargerId = null;
    Get.find<AppCtrl>().setUpInitialData();
  } catch (e) {
    debugPrint(e.toString());
  }
}
