import 'package:get/get.dart';
import 'package:go_charge/utils/payment_handler.dart';
import 'package:phonepe_pg_htkc/models/pg_payment_status_model.dart';
import 'package:phonepe_pg_htkc/phonepe_pg_htkc.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

class PGPaymentController extends GetxController {
  var isLoading = false.obs;
  var isVisible = true.obs;
  var errorMsg = ''.obs;
  var merchantTransactionId = 'OIJDDJ898932'.obs;
  var paymentStatusResponse = List<PGPaymentStatusResponse>.empty().obs;
  Uuid uuid = const Uuid();

  void checkPaymentStatus() async {
    try {
      isLoading(true);
      var data = await PhonePeApiServices.checkTransactionStatus(
          merchantTransactionId.value,
          merchantId: PhonePe.merchantId,
          isUAT: false,
          pgResponseUrl: PhonePe.redirectUrl);
      if (data != null) {
        if (data.code == 'PAYMENT_SUCCESS') {
          print('Payment Success!!');
        } else if (data.code == 'PAYMENT_PENDING') {
          print('Please complete payment process!!!');
        } else if (data.code == 'PAYMENT_ERROR') {
          isVisible(true);
          print('Payment failed! Please try again!');
        }
      } else {
        isVisible(true);
        print('Transaction failed! Please try again!');
      }
    } finally {
      isLoading(false);
    }
  }

  Future<bool> initPayment(
      {required String customerMobile, required double amount}) async {
    merchantTransactionId.value = uuid.v1();
    try {
      isLoading(true);
      var paymentResponse = await PhonePeApiServices.initTransaction(
          isUAT: true,
          callBackURL: PhonePe.callback,
          merchantId: PhonePe.merchantId,
          redirectURL: PhonePe.redirectUrl,
          saltIndex: PhonePe.phonePeKeyIndex.toString(),
          saltKey: PhonePe.phonePeKey,
          merchantTransactionId.value,
          customerMobile,
          amount);
      print(paymentResponse?.message);
      if (paymentResponse != null) {
        await launchUrl(
            Uri.parse(paymentResponse.data.instrumentResponse.redirectInfo.url),
            mode: LaunchMode.externalNonBrowserApplication);
        isVisible(false);
        return true;
      } else {
        print('Something went wrong! Please try again later');
        return true;
      }
    } finally {
      isLoading(false);
    }
  }
}
