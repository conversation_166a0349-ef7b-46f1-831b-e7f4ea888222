import 'dart:async';
import 'package:amplify_api/amplify_api.dart' as amplify;
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/constants/const.dart';
import 'package:go_charge/controllers/booking_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';

import '../configuration/configs.dart';

class AppCtrl extends GetxController {
  StreamSubscription<amplify.GraphQLResponse<Station>>? stationStream;
  StreamSubscription<amplify.GraphQLResponse<Charger>>? chargerStream;
  StreamSubscription<amplify.GraphQLResponse<Connector>>? connecterStream;
  StreamSubscription<amplify.GraphQLResponse<ChargingTable>>? chargingStream;
  StreamSubscription<amplify.GraphQLResponse<ChargingTable>>?
      chargingStreamOnCreate;
  // StreamSubscription<amplify.GraphQLResponse<EndUser>>? endUserStream;
  // String chargerId = "ELCOP";
  List<Station> stations = [];
  List<Connector> connectors = [];
  List<ChargingTable> chargings = [];
  Charger? charger;
  Station? station;
  List<ConnectorType> connectorTypes = [];
  List<ChargerCapacity> chargerCapacities = [];
  bool userSynced = false;
  bool chargerLoaded = false;
  AuthUser? currentAuthUser;
  EndUser? currentUserData;
  bool networkIsUp = false;
  StationListing stationListing = StationListing.all;
  Timer? heartBeatCheckTimer;

  @override
  void onInit() {
    super.onInit();
    setUpInitialData();
  }

  @override
  void onClose() {
    super.onClose();
    heartBeatCheckTimer?.cancel();
  }

  setUpInitialData() async {
    await getCharger();
    Future.delayed(const Duration(seconds: 2))
        .then((value) => fetchConnectorTypes())
        .then((value) => fetchChargerCapacitiesTypes());
  }

  void observeStation() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        Station.classType,
        where: Station.ID.eq(charger?.stationId));
    // Stream
    final Stream<amplify.GraphQLResponse<Station>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    stationStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received: ${event.data}');
        if (event.data != null) {
          station = event.data!;
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  void getStation() async {
    try {
      final request = amplify.ModelQueries.get(Station.classType,
          StationModelIdentifier(id: charger?.stationId ?? ""));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      station = result.data;
      chargerLoaded = true;
      observeStation();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
    }
  }

  void observeCharger() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        Charger.classType,
        where: Charger.CHARGINGPOINTID.eq(localChargerId));
    // Stream
    final Stream<amplify.GraphQLResponse<Charger>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    chargerStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received: ${event.data}');
        if (event.data != null) {
          charger = event.data!;
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  getCharger() async {
    try {
      print("object $localChargerId");
      final request = amplify.ModelQueries.list(Charger.classType,
          limit: unlimitedLimit,
          where: Charger.CHARGINGPOINTID.eq(localChargerId));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      print("object2323");
      safePrint('Charger Found: ${result.data?.items.length}');
      final cList = (result.data?.items.whereType<Charger>().toList() ?? []);
      if (cList.isNotEmpty) charger = cList.first;
      chargerLoaded = true;
      update();
      getStation();
      observeCharger();
      getConnectors();
      getCharging();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
    }
  }

  void observeConnectors() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        Connector.classType,
        where: Connector.CHARGERID.eq(charger?.id));
    // Stream
    final Stream<amplify.GraphQLResponse<Connector>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    connecterStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received: ${event.data}');
        if (event.data != null) {
          connectors[connectors.indexWhere(
              (element) => element.id == event.data?.id)] = event.data!;
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  void getConnectors() async {
    try {
      final request = amplify.ModelQueries.list(Connector.classType,
          limit: unlimitedLimit, where: Connector.CHARGERID.eq(charger?.id));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Connectors Found: ${result.data?.items.length}');
      connectors = (result.data?.items.whereType<Connector>().toList() ?? []);
      connectors
          .sort((a, b) => a.connector_number.compareTo(b.connector_number));
      observeConnectors();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
    }
  }

  void observeCharging() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        ChargingTable.classType,
        where: ChargingTable.CHARGEPOINTID
            .eq(localChargerId)
            .and(ChargingTable.ISPAID.eq(true))
            .and(ChargingTable.STATUS
                .eq(ChargingStatus.inActive)
                .or(ChargingTable.STATUS.eq(ChargingStatus.active))));
    final subscriptionRequestOnCreate = amplify.ModelSubscriptions.onCreate(
        ChargingTable.classType,
        where: ChargingTable.CHARGEPOINTID
            .eq(localChargerId)
            .and(ChargingTable.ISPAID.eq(true)));
    // Stream
    final Stream<amplify.GraphQLResponse<ChargingTable>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    final Stream<amplify.GraphQLResponse<ChargingTable>> operationOnCreate =
        Amplify.API.subscribe(
      subscriptionRequestOnCreate,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    chargingStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received: ${event.data}');
        if (event.data != null) {
          if (chargings.indexWhere((element) => element.id == event.data?.id) ==
              -1) {
            chargings.add(event.data!);
          } else {
            chargings[chargings.indexWhere(
                (element) => element.id == event.data?.id)] = event.data!;
          }
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
    chargingStreamOnCreate = operationOnCreate.listen(
      (event) async {
        safePrint('Subscription event data received,: ${event.data}');
        if (event.data != null) {
          if (chargings.firstWhereOrNull(
                  (element) => element.id == event.data?.id) ==
              null) {
            chargings.add(event.data!);
          }
          update();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  void getCharging() async {
    try {
      final request = amplify.ModelQueries.list(ChargingTable.classType,
          limit: unlimitedLimit,
          where: ChargingTable.CHARGEPOINTID
              .eq(localChargerId)
              .and(ChargingTable.ISPAID.eq(true))
              .and(ChargingTable.STATUS
                  .eq(ChargingStatus.inActive)
                  .or(ChargingTable.STATUS.eq(ChargingStatus.active))));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Chargings Found: ${result.data?.items.length}');
      chargings =
          (result.data?.items.whereType<ChargingTable>().toList() ?? []);
      observeCharging();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying stations: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying stations: $e');
    }
  }

  onStationListingChange(StationListing type) {
    stationListing = type;
    update();
  }
}

Future<bool?> toggleFavourite(String stationId) async {
  try {
    final ctrl = Get.find<AppCtrl>();
    List<String> newFavs = <String>[...(ctrl.currentUserData?.favs ?? [])];
    if (newFavs.contains(stationId)) {
      newFavs.remove(stationId);
    } else {
      newFavs.add(stationId);
    }
    final user = ctrl.currentUserData?.copyWith(favs: newFavs);
    // print(ctrl.currentUserData);
    if (user != null) {
      // print(user);
      final request = amplify.ModelMutations.update(user);
      final res = await Amplify.API.mutate(request: request).response;
      // print(res);

      return res.data != null;
    }
  } on amplify.ApiException catch (e) {
    safePrint('Something went wrong saving model: ${e.message}');
  } catch (e) {
    debugPrint(e.toString());
  }
  return null;
}

Future<List<ConnectorType>?> fetchConnectorTypes() async {
  try {
    safePrint("Fethcing ConnectorTypes...");
    final ctrl = Get.find<AppCtrl>();
    final request = amplify.ModelQueries.list(ConnectorType.classType);
    // BY GRAPHQL API
    final result = await Amplify.API.query(request: request).response;
    safePrint('ConnectorTypes Found: ${result.data?.items.length}');
    ctrl.connectorTypes =
        result.data?.items.whereType<ConnectorType>().toList() ?? [];
    ctrl.update();
    return ctrl.connectorTypes;
  } on amplify.ApiException catch (e) {
    safePrint('Something went wrong querying ConnectorType: ${e.message}');
    return null;
  } catch (e) {
    safePrint('Something went wrong querying ConnectorType: $e');
    return null;
  }
}

Future<List<ChargerCapacity>?> fetchChargerCapacitiesTypes() async {
  try {
    safePrint("Fethcing CapacityTypes...");
    final ctrl = Get.find<AppCtrl>();
    final request = amplify.ModelQueries.list(ChargerCapacity.classType);
    // BY GRAPHQL API
    final result = await Amplify.API.query(request: request).response;
    safePrint('CapacityTypes Found: ${result.data?.items.length}');
    ctrl.chargerCapacities =
        result.data?.items.whereType<ChargerCapacity>().toList() ?? [];
    ctrl.update();
    return ctrl.chargerCapacities;
  } on amplify.ApiException catch (e) {
    safePrint('Something went wrong querying ConnectorType: ${e.message}');
    return null;
  } catch (e) {
    safePrint('Something went wrong querying ConnectorType: $e');
    return null;
  }
}

enum StationListing { all, favourite }
