import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/constants/const.dart';
import 'package:go_charge/shared/methods.dart';
import 'package:go_charge/utils/api_manager.dart';
import 'package:go_charge/views/wallet/wallet.dart';
import '../models/ModelProvider.dart';
import '../shared/snackbar.dart';

class BookingCtrl extends GetxController {
  Future<String?> createNewBooking2({
    required UserVehicle? userVehicle,
    required DateTime? startAt,
    required Connector? selectedConnector,
    required Station station,
    required CPO? cpo,
    required String bookingType,
    required String pgTransRef,
    required String? gstNo,
    required String? gstName,
    required String? contact,
    required Charger? charger,
    required num estimatedDuration,
    required num estimatedPrice,
    required num estimatedUnits,
    required num taxAmount,
    required num taxPercent,
    required num amountFromWallet,
    required bool fromWallet,
  }) async {
    try {
      // GET USER DATA
      final userDataResponse = (contact != null
          ? await Amplify.API
              .mutate(
                  request: ModelQueries.list(EndUser.classType,
                      limit: unlimitedLimit,
                      where: EndUser.CONTACT.eq(phoneNo(contact))))
              .response
          : null);

      final userData = userDataResponse?.data?.items.isNotEmpty ?? false
          ? userDataResponse?.data?.items.first
          : null;
      // Create Transaction
      final transDoc = Transaction(
        currentBalance: userData?.balance,
        bookingId: "",
        transRef: "",
        pgTransRef: pgTransRef,
        status: fromWallet
            ? TransactionStatus.successful
            : TransactionStatus.pending,
        amount: fromWallet
            ? amountFromWallet.toDouble()
            : (estimatedPrice - amountFromWallet).toDouble(),
        dateTime: TemporalDateTime.now(),
        reason: "Booking",
        method: fromWallet ? "Wallet" : "-",
        uId: userData?.id,
        userName: userData?.user_fullname,
        userContact: userData?.contact,
      );
      final request = ModelMutations.create(transDoc);
      final transResponse = await Amplify.API.mutate(request: request).response;
      // print('>>>> ${walletMutationResponse.data?.id}');
      if (transResponse.data != null) {
        bool settlementDone = !fromWallet;
        if (fromWallet) {
          // UPDATE USER DATA
          if (userData != null) {
            if ((userData.balance ?? 0) - amountFromWallet < 0) {
              showAppShackBar("Insufficient balance");
              return null;
            }
            final request = ModelMutations.update(userData!.copyWith(
                balance: (userData?.balance ?? 0) - amountFromWallet));
            await Amplify.API.mutate(request: request).response;
            settlementDone = true;
          } else {
            // Delete Transaction
            final request = ModelMutations.delete(transDoc);
            await Amplify.API.mutate(request: request).response;
          }
        }

        // final appCtrl = Get.find<AppCtrl>();
        final bkId = getRandomId(12).toUpperCase();

        final startTime = startAt != null
            ? TemporalDateTime(startAt)
            : TemporalDateTime.now();
        final endAt =
            startAt?.add(Duration(minutes: estimatedDuration.toInt()));

        if (!settlementDone) return null;
        final chargeBoooking = ChargingTable(
          isPaid: false,
          // isPaid: fromWallet,
          payment_status: "Paid",
          // payment_status: "Pending",
          pgTransRef: pgTransRef,
          taxPercent: taxPercent.toDouble(),
          stationName: station.station_name,
          cpoName: cpo?.name,
          gstin: gstNo,
          gstName: gstName,
          paymentTime: fromWallet ? TemporalDateTime.now() : null,
          amountFromWallet: amountFromWallet.toDouble(),
          tax_amount: taxAmount.toDouble(),
          estimatedDuration: estimatedDuration.toDouble(),
          estimatedUnits: estimatedUnits.toDouble(),
          charging_fee: estimatedPrice.toDouble(),
          chargerId: charger?.id,
          connector_no: selectedConnector?.connector_number,
          createdAt: TemporalDateTime.now(),
          station_id: station.id,
          city: station.city,
          geoState: station.geoState,
          booking_type: bookingType,
          pricePerKw: charger?.pricePerKW ?? 20,
          booking_id: bkId,
          chargePointId: charger?.chargingPointId,
          start_time: startTime,
          user_id: userData?.id,
          vehical_number: userVehicle?.vehical_number,
          vehicle_id: userVehicle?.id,
          end_time:
              endAt != null ? TemporalDateTime(endAt) : TemporalDateTime.now(),
          status: ChargingStatus.inActive,
          transDocId: transResponse.data?.id,
          userName: userData?.user_fullname,
          userContact: userData?.contact,
          isIgst: station.igst,
        );
        final chargingRequest = ModelMutations.create(chargeBoooking);
        final chargingResponse =
            await Amplify.API.mutate(request: chargingRequest).response;
        // Call booking check schedular if from wallet
        if (chargingResponse.data != null && fromWallet) {
          APIManager.bookingCheckSch(chargingResponse.data!);
        }
        // Update charging id in transaction
        await Amplify.API
            .mutate(
                request: ModelMutations.update(transResponse.data!
                    .copyWith(bookingId: chargeBoooking.booking_id)))
            // transDoc.copyWith(bookingId: chargeBoooking.booking_id)))
            .response;
        return chargingResponse.data?.id;
        // return chargeBoooking.id;
        // return createdBooking.id;
      }
      return null;
    } on ApiException catch (e) {
      safePrint('Mutation failed: $e');
      return null;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}

class ChargingStatus {
  static const active = "Active";
  static const inActive = "Inactive";
  static const completed = "Completed";
  static const canceled = "Canceled";
}

class ChargingCommands {
  static const startTransaction = "StartTransaction";
  static const stopTransaction = "StopTransaction";
}

Future<int?> getSetInvoiceNo(String cpoId) async {
  try {
    final request =
        ModelQueries.get(CPO.classType, CPOModelIdentifier(id: cpoId));
    final response = await Amplify.API.mutate(request: request).response;
    bool finDateUpdated = response.data?.currentFinYear != null
        ? checkCurrentFinYearDate(
            response.data!.currentFinYear!.getDateTimeInUtc())
        : true;

    int newInvoiceNo = (response.data?.invoiceNo ?? 0) + 1;
    newInvoiceNo = finDateUpdated ? newInvoiceNo : 1;
    if (response.data == null) return null;
    final updateRequest = ModelMutations.update(response.data!.copyWith(
        invoiceNo: newInvoiceNo,
        currentFinYear: TemporalDateTime(finDateUpdated
            ? (response.data!.currentFinYear == null
                ? getCurrentFinancialYearStartDate()
                : response.data!.currentFinYear!.getDateTimeInUtc())
            : getCurrentFinancialYearStartDate())));
    final updateResponse =
        await Amplify.API.mutate(request: updateRequest).response;
    if (updateResponse.data == null) return null;
    return newInvoiceNo;
  } catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

bool checkCurrentFinYearDate(DateTime currentFinYear) {
  DateTime today = DateTime.now();
  int currentYear = today.year;
  int currentMonth = today.month;

  // Determine the financial year start date
  DateTime finYearStart;
  if (currentMonth < 4) {
    // If the current month is before April (1-indexed: Jan = 1, Feb = 2, Mar = 3)
    finYearStart =
        DateTime(currentYear - 1, 4, 1); // April 1st of the previous year
  } else {
    finYearStart = DateTime(currentYear, 4, 1); // April 1st of the current year
  }

  // Check if currentFinYear is the same as finYearStart
  bool finDateUpdated = currentFinYear.isAtSameMomentAs(finYearStart);

  return finDateUpdated;
}

DateTime getCurrentFinancialYearStartDate() {
  DateTime today = DateTime.now();
  int currentYear = today.year;
  int currentMonth = today.month;

  DateTime finYearStart;
  if (currentMonth < 4) {
    // If the current month is before April (1-indexed: Jan = 1, Feb = 2, Mar = 3)
    finYearStart =
        DateTime(currentYear - 1, 4, 1); // April 1st of the previous year
  } else {
    finYearStart = DateTime(currentYear, 4, 1); // April 1st of the current year
  }

  return finYearStart;
}

String phoneNo(String text) {
  return text.contains("+91") ? text : '+91$text';
}
