/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Wallet type in your schema. */
class Wallet extends amplify_core.Model {
  static const classType = const _WalletModelType();
  final String id;
  final int? _last_amount_added;
  final WalletType? _type;
  final String? _method;
  final amplify_core.TemporalDateTime? _timeStamp;
  final String? _userId;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  WalletModelIdentifier get modelIdentifier {
      return WalletModelIdentifier(
        id: id
      );
  }
  
  int? get last_amount_added {
    return _last_amount_added;
  }
  
  WalletType? get type {
    return _type;
  }
  
  String? get method {
    return _method;
  }
  
  amplify_core.TemporalDateTime? get timeStamp {
    return _timeStamp;
  }
  
  String? get userId {
    return _userId;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Wallet._internal({required this.id, last_amount_added, type, method, timeStamp, userId, createdAt, updatedAt}): _last_amount_added = last_amount_added, _type = type, _method = method, _timeStamp = timeStamp, _userId = userId, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Wallet({String? id, int? last_amount_added, WalletType? type, String? method, amplify_core.TemporalDateTime? timeStamp, String? userId}) {
    return Wallet._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      last_amount_added: last_amount_added,
      type: type,
      method: method,
      timeStamp: timeStamp,
      userId: userId);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Wallet &&
      id == other.id &&
      _last_amount_added == other._last_amount_added &&
      _type == other._type &&
      _method == other._method &&
      _timeStamp == other._timeStamp &&
      _userId == other._userId;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Wallet {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("last_amount_added=" + (_last_amount_added != null ? _last_amount_added!.toString() : "null") + ", ");
    buffer.write("type=" + (_type != null ? amplify_core.enumToString(_type)! : "null") + ", ");
    buffer.write("method=" + "$_method" + ", ");
    buffer.write("timeStamp=" + (_timeStamp != null ? _timeStamp!.format() : "null") + ", ");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Wallet copyWith({int? last_amount_added, WalletType? type, String? method, amplify_core.TemporalDateTime? timeStamp, String? userId}) {
    return Wallet._internal(
      id: id,
      last_amount_added: last_amount_added ?? this.last_amount_added,
      type: type ?? this.type,
      method: method ?? this.method,
      timeStamp: timeStamp ?? this.timeStamp,
      userId: userId ?? this.userId);
  }
  
  Wallet copyWithModelFieldValues({
    ModelFieldValue<int?>? last_amount_added,
    ModelFieldValue<WalletType?>? type,
    ModelFieldValue<String?>? method,
    ModelFieldValue<amplify_core.TemporalDateTime?>? timeStamp,
    ModelFieldValue<String?>? userId
  }) {
    return Wallet._internal(
      id: id,
      last_amount_added: last_amount_added == null ? this.last_amount_added : last_amount_added.value,
      type: type == null ? this.type : type.value,
      method: method == null ? this.method : method.value,
      timeStamp: timeStamp == null ? this.timeStamp : timeStamp.value,
      userId: userId == null ? this.userId : userId.value
    );
  }
  
  Wallet.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _last_amount_added = (json['last_amount_added'] as num?)?.toInt(),
      _type = amplify_core.enumFromString<WalletType>(json['type'], WalletType.values),
      _method = json['method'],
      _timeStamp = json['timeStamp'] != null ? amplify_core.TemporalDateTime.fromString(json['timeStamp']) : null,
      _userId = json['userId'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'last_amount_added': _last_amount_added, 'type': amplify_core.enumToString(_type), 'method': _method, 'timeStamp': _timeStamp?.format(), 'userId': _userId, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'last_amount_added': _last_amount_added,
    'type': _type,
    'method': _method,
    'timeStamp': _timeStamp,
    'userId': _userId,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<WalletModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<WalletModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final LAST_AMOUNT_ADDED = amplify_core.QueryField(fieldName: "last_amount_added");
  static final TYPE = amplify_core.QueryField(fieldName: "type");
  static final METHOD = amplify_core.QueryField(fieldName: "method");
  static final TIMESTAMP = amplify_core.QueryField(fieldName: "timeStamp");
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Wallet";
    modelSchemaDefinition.pluralName = "Wallets";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Wallet.LAST_AMOUNT_ADDED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Wallet.TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.enumeration)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Wallet.METHOD,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Wallet.TIMESTAMP,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Wallet.USERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _WalletModelType extends amplify_core.ModelType<Wallet> {
  const _WalletModelType();
  
  @override
  Wallet fromJson(Map<String, dynamic> jsonData) {
    return Wallet.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Wallet';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Wallet] in your schema.
 */
class WalletModelIdentifier implements amplify_core.ModelIdentifier<Wallet> {
  final String id;

  /** Create an instance of WalletModelIdentifier using [id] the primary key. */
  const WalletModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'WalletModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is WalletModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}