/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the ManufacturerTable type in your schema. */
class ManufacturerTable extends amplify_core.Model {
  static const classType = const _ManufacturerTableModelType();
  final String id;
  final String? _manufacturer_name;
  final String? _manufacturer_type;
  final bool? _status;
  final String? _contact_person;
  final String? _contact_number;
  final String? _email_id;
  final String? _contact_person_dept;
  final String? _address;
  final String? _country;
  final String? _geoState;
  final String? _city;
  final String? _postcode;
  final String? _contact_person_designation;
  final String? _is_preferred;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ManufacturerTableModelIdentifier get modelIdentifier {
      return ManufacturerTableModelIdentifier(
        id: id
      );
  }
  
  String get manufacturer_name {
    try {
      return _manufacturer_name!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get manufacturer_type {
    return _manufacturer_type;
  }
  
  bool? get status {
    return _status;
  }
  
  String? get contact_person {
    return _contact_person;
  }
  
  String? get contact_number {
    return _contact_number;
  }
  
  String? get email_id {
    return _email_id;
  }
  
  String? get contact_person_dept {
    return _contact_person_dept;
  }
  
  String? get address {
    return _address;
  }
  
  String? get country {
    return _country;
  }
  
  String? get geoState {
    return _geoState;
  }
  
  String? get city {
    return _city;
  }
  
  String? get postcode {
    return _postcode;
  }
  
  String? get contact_person_designation {
    return _contact_person_designation;
  }
  
  String? get is_preferred {
    return _is_preferred;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const ManufacturerTable._internal({required this.id, required manufacturer_name, manufacturer_type, status, contact_person, contact_number, email_id, contact_person_dept, address, country, geoState, city, postcode, contact_person_designation, is_preferred, createdAt, updatedAt}): _manufacturer_name = manufacturer_name, _manufacturer_type = manufacturer_type, _status = status, _contact_person = contact_person, _contact_number = contact_number, _email_id = email_id, _contact_person_dept = contact_person_dept, _address = address, _country = country, _geoState = geoState, _city = city, _postcode = postcode, _contact_person_designation = contact_person_designation, _is_preferred = is_preferred, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory ManufacturerTable({String? id, required String manufacturer_name, String? manufacturer_type, bool? status, String? contact_person, String? contact_number, String? email_id, String? contact_person_dept, String? address, String? country, String? geoState, String? city, String? postcode, String? contact_person_designation, String? is_preferred}) {
    return ManufacturerTable._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      manufacturer_name: manufacturer_name,
      manufacturer_type: manufacturer_type,
      status: status,
      contact_person: contact_person,
      contact_number: contact_number,
      email_id: email_id,
      contact_person_dept: contact_person_dept,
      address: address,
      country: country,
      geoState: geoState,
      city: city,
      postcode: postcode,
      contact_person_designation: contact_person_designation,
      is_preferred: is_preferred);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ManufacturerTable &&
      id == other.id &&
      _manufacturer_name == other._manufacturer_name &&
      _manufacturer_type == other._manufacturer_type &&
      _status == other._status &&
      _contact_person == other._contact_person &&
      _contact_number == other._contact_number &&
      _email_id == other._email_id &&
      _contact_person_dept == other._contact_person_dept &&
      _address == other._address &&
      _country == other._country &&
      _geoState == other._geoState &&
      _city == other._city &&
      _postcode == other._postcode &&
      _contact_person_designation == other._contact_person_designation &&
      _is_preferred == other._is_preferred;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("ManufacturerTable {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("manufacturer_name=" + "$_manufacturer_name" + ", ");
    buffer.write("manufacturer_type=" + "$_manufacturer_type" + ", ");
    buffer.write("status=" + (_status != null ? _status!.toString() : "null") + ", ");
    buffer.write("contact_person=" + "$_contact_person" + ", ");
    buffer.write("contact_number=" + "$_contact_number" + ", ");
    buffer.write("email_id=" + "$_email_id" + ", ");
    buffer.write("contact_person_dept=" + "$_contact_person_dept" + ", ");
    buffer.write("address=" + "$_address" + ", ");
    buffer.write("country=" + "$_country" + ", ");
    buffer.write("geoState=" + "$_geoState" + ", ");
    buffer.write("city=" + "$_city" + ", ");
    buffer.write("postcode=" + "$_postcode" + ", ");
    buffer.write("contact_person_designation=" + "$_contact_person_designation" + ", ");
    buffer.write("is_preferred=" + "$_is_preferred" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  ManufacturerTable copyWith({String? manufacturer_name, String? manufacturer_type, bool? status, String? contact_person, String? contact_number, String? email_id, String? contact_person_dept, String? address, String? country, String? geoState, String? city, String? postcode, String? contact_person_designation, String? is_preferred}) {
    return ManufacturerTable._internal(
      id: id,
      manufacturer_name: manufacturer_name ?? this.manufacturer_name,
      manufacturer_type: manufacturer_type ?? this.manufacturer_type,
      status: status ?? this.status,
      contact_person: contact_person ?? this.contact_person,
      contact_number: contact_number ?? this.contact_number,
      email_id: email_id ?? this.email_id,
      contact_person_dept: contact_person_dept ?? this.contact_person_dept,
      address: address ?? this.address,
      country: country ?? this.country,
      geoState: geoState ?? this.geoState,
      city: city ?? this.city,
      postcode: postcode ?? this.postcode,
      contact_person_designation: contact_person_designation ?? this.contact_person_designation,
      is_preferred: is_preferred ?? this.is_preferred);
  }
  
  ManufacturerTable copyWithModelFieldValues({
    ModelFieldValue<String>? manufacturer_name,
    ModelFieldValue<String?>? manufacturer_type,
    ModelFieldValue<bool?>? status,
    ModelFieldValue<String?>? contact_person,
    ModelFieldValue<String?>? contact_number,
    ModelFieldValue<String?>? email_id,
    ModelFieldValue<String?>? contact_person_dept,
    ModelFieldValue<String?>? address,
    ModelFieldValue<String?>? country,
    ModelFieldValue<String?>? geoState,
    ModelFieldValue<String?>? city,
    ModelFieldValue<String?>? postcode,
    ModelFieldValue<String?>? contact_person_designation,
    ModelFieldValue<String?>? is_preferred
  }) {
    return ManufacturerTable._internal(
      id: id,
      manufacturer_name: manufacturer_name == null ? this.manufacturer_name : manufacturer_name.value,
      manufacturer_type: manufacturer_type == null ? this.manufacturer_type : manufacturer_type.value,
      status: status == null ? this.status : status.value,
      contact_person: contact_person == null ? this.contact_person : contact_person.value,
      contact_number: contact_number == null ? this.contact_number : contact_number.value,
      email_id: email_id == null ? this.email_id : email_id.value,
      contact_person_dept: contact_person_dept == null ? this.contact_person_dept : contact_person_dept.value,
      address: address == null ? this.address : address.value,
      country: country == null ? this.country : country.value,
      geoState: geoState == null ? this.geoState : geoState.value,
      city: city == null ? this.city : city.value,
      postcode: postcode == null ? this.postcode : postcode.value,
      contact_person_designation: contact_person_designation == null ? this.contact_person_designation : contact_person_designation.value,
      is_preferred: is_preferred == null ? this.is_preferred : is_preferred.value
    );
  }
  
  ManufacturerTable.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _manufacturer_name = json['manufacturer_name'],
      _manufacturer_type = json['manufacturer_type'],
      _status = json['status'],
      _contact_person = json['contact_person'],
      _contact_number = json['contact_number'],
      _email_id = json['email_id'],
      _contact_person_dept = json['contact_person_dept'],
      _address = json['address'],
      _country = json['country'],
      _geoState = json['geoState'],
      _city = json['city'],
      _postcode = json['postcode'],
      _contact_person_designation = json['contact_person_designation'],
      _is_preferred = json['is_preferred'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'manufacturer_name': _manufacturer_name, 'manufacturer_type': _manufacturer_type, 'status': _status, 'contact_person': _contact_person, 'contact_number': _contact_number, 'email_id': _email_id, 'contact_person_dept': _contact_person_dept, 'address': _address, 'country': _country, 'geoState': _geoState, 'city': _city, 'postcode': _postcode, 'contact_person_designation': _contact_person_designation, 'is_preferred': _is_preferred, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'manufacturer_name': _manufacturer_name,
    'manufacturer_type': _manufacturer_type,
    'status': _status,
    'contact_person': _contact_person,
    'contact_number': _contact_number,
    'email_id': _email_id,
    'contact_person_dept': _contact_person_dept,
    'address': _address,
    'country': _country,
    'geoState': _geoState,
    'city': _city,
    'postcode': _postcode,
    'contact_person_designation': _contact_person_designation,
    'is_preferred': _is_preferred,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ManufacturerTableModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ManufacturerTableModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final MANUFACTURER_NAME = amplify_core.QueryField(fieldName: "manufacturer_name");
  static final MANUFACTURER_TYPE = amplify_core.QueryField(fieldName: "manufacturer_type");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final CONTACT_PERSON = amplify_core.QueryField(fieldName: "contact_person");
  static final CONTACT_NUMBER = amplify_core.QueryField(fieldName: "contact_number");
  static final EMAIL_ID = amplify_core.QueryField(fieldName: "email_id");
  static final CONTACT_PERSON_DEPT = amplify_core.QueryField(fieldName: "contact_person_dept");
  static final ADDRESS = amplify_core.QueryField(fieldName: "address");
  static final COUNTRY = amplify_core.QueryField(fieldName: "country");
  static final GEOSTATE = amplify_core.QueryField(fieldName: "geoState");
  static final CITY = amplify_core.QueryField(fieldName: "city");
  static final POSTCODE = amplify_core.QueryField(fieldName: "postcode");
  static final CONTACT_PERSON_DESIGNATION = amplify_core.QueryField(fieldName: "contact_person_designation");
  static final IS_PREFERRED = amplify_core.QueryField(fieldName: "is_preferred");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "ManufacturerTable";
    modelSchemaDefinition.pluralName = "ManufacturerTables";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.MANUFACTURER_NAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.MANUFACTURER_TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.CONTACT_PERSON,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.CONTACT_NUMBER,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.EMAIL_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.CONTACT_PERSON_DEPT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.ADDRESS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.COUNTRY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.GEOSTATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.CITY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.POSTCODE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.CONTACT_PERSON_DESIGNATION,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ManufacturerTable.IS_PREFERRED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ManufacturerTableModelType extends amplify_core.ModelType<ManufacturerTable> {
  const _ManufacturerTableModelType();
  
  @override
  ManufacturerTable fromJson(Map<String, dynamic> jsonData) {
    return ManufacturerTable.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'ManufacturerTable';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [ManufacturerTable] in your schema.
 */
class ManufacturerTableModelIdentifier implements amplify_core.ModelIdentifier<ManufacturerTable> {
  final String id;

  /** Create an instance of ManufacturerTableModelIdentifier using [id] the primary key. */
  const ManufacturerTableModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ManufacturerTableModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ManufacturerTableModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}