/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the CPO type in your schema. */
class CPO extends amplify_core.Model {
  static const classType = const _CPOModelType();
  final String id;
  final String? _name;
  final String? _taxId;
  final String? _city;
  final String? _geoState;
  final String? _contact;
  final String? _email;
  final amplify_core.TemporalDateTime? _joiningDate;
  final int? _invoiceNo;
  final String? _invoicePrefix;
  final amplify_core.TemporalDateTime? _currentFinYear;
  final double? _quickChargeLimit;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  CPOModelIdentifier get modelIdentifier {
      return CPOModelIdentifier(
        id: id
      );
  }
  
  String get name {
    try {
      return _name!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get taxId {
    return _taxId;
  }
  
  String get city {
    try {
      return _city!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get geoState {
    try {
      return _geoState!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get contact {
    try {
      return _contact!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get email {
    try {
      return _email!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDateTime? get joiningDate {
    return _joiningDate;
  }
  
  int? get invoiceNo {
    return _invoiceNo;
  }
  
  String? get invoicePrefix {
    return _invoicePrefix;
  }
  
  amplify_core.TemporalDateTime? get currentFinYear {
    return _currentFinYear;
  }
  
  double? get quickChargeLimit {
    return _quickChargeLimit;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const CPO._internal({required this.id, required name, taxId, required city, required geoState, required contact, required email, joiningDate, invoiceNo, invoicePrefix, currentFinYear, quickChargeLimit, createdAt, updatedAt}): _name = name, _taxId = taxId, _city = city, _geoState = geoState, _contact = contact, _email = email, _joiningDate = joiningDate, _invoiceNo = invoiceNo, _invoicePrefix = invoicePrefix, _currentFinYear = currentFinYear, _quickChargeLimit = quickChargeLimit, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory CPO({String? id, required String name, String? taxId, required String city, required String geoState, required String contact, required String email, amplify_core.TemporalDateTime? joiningDate, int? invoiceNo, String? invoicePrefix, amplify_core.TemporalDateTime? currentFinYear, double? quickChargeLimit}) {
    return CPO._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      name: name,
      taxId: taxId,
      city: city,
      geoState: geoState,
      contact: contact,
      email: email,
      joiningDate: joiningDate,
      invoiceNo: invoiceNo,
      invoicePrefix: invoicePrefix,
      currentFinYear: currentFinYear,
      quickChargeLimit: quickChargeLimit);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CPO &&
      id == other.id &&
      _name == other._name &&
      _taxId == other._taxId &&
      _city == other._city &&
      _geoState == other._geoState &&
      _contact == other._contact &&
      _email == other._email &&
      _joiningDate == other._joiningDate &&
      _invoiceNo == other._invoiceNo &&
      _invoicePrefix == other._invoicePrefix &&
      _currentFinYear == other._currentFinYear &&
      _quickChargeLimit == other._quickChargeLimit;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("CPO {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("name=" + "$_name" + ", ");
    buffer.write("taxId=" + "$_taxId" + ", ");
    buffer.write("city=" + "$_city" + ", ");
    buffer.write("geoState=" + "$_geoState" + ", ");
    buffer.write("contact=" + "$_contact" + ", ");
    buffer.write("email=" + "$_email" + ", ");
    buffer.write("joiningDate=" + (_joiningDate != null ? _joiningDate!.format() : "null") + ", ");
    buffer.write("invoiceNo=" + (_invoiceNo != null ? _invoiceNo!.toString() : "null") + ", ");
    buffer.write("invoicePrefix=" + "$_invoicePrefix" + ", ");
    buffer.write("currentFinYear=" + (_currentFinYear != null ? _currentFinYear!.format() : "null") + ", ");
    buffer.write("quickChargeLimit=" + (_quickChargeLimit != null ? _quickChargeLimit!.toString() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  CPO copyWith({String? name, String? taxId, String? city, String? geoState, String? contact, String? email, amplify_core.TemporalDateTime? joiningDate, int? invoiceNo, String? invoicePrefix, amplify_core.TemporalDateTime? currentFinYear, double? quickChargeLimit}) {
    return CPO._internal(
      id: id,
      name: name ?? this.name,
      taxId: taxId ?? this.taxId,
      city: city ?? this.city,
      geoState: geoState ?? this.geoState,
      contact: contact ?? this.contact,
      email: email ?? this.email,
      joiningDate: joiningDate ?? this.joiningDate,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      invoicePrefix: invoicePrefix ?? this.invoicePrefix,
      currentFinYear: currentFinYear ?? this.currentFinYear,
      quickChargeLimit: quickChargeLimit ?? this.quickChargeLimit);
  }
  
  CPO copyWithModelFieldValues({
    ModelFieldValue<String>? name,
    ModelFieldValue<String?>? taxId,
    ModelFieldValue<String>? city,
    ModelFieldValue<String>? geoState,
    ModelFieldValue<String>? contact,
    ModelFieldValue<String>? email,
    ModelFieldValue<amplify_core.TemporalDateTime?>? joiningDate,
    ModelFieldValue<int?>? invoiceNo,
    ModelFieldValue<String?>? invoicePrefix,
    ModelFieldValue<amplify_core.TemporalDateTime?>? currentFinYear,
    ModelFieldValue<double?>? quickChargeLimit
  }) {
    return CPO._internal(
      id: id,
      name: name == null ? this.name : name.value,
      taxId: taxId == null ? this.taxId : taxId.value,
      city: city == null ? this.city : city.value,
      geoState: geoState == null ? this.geoState : geoState.value,
      contact: contact == null ? this.contact : contact.value,
      email: email == null ? this.email : email.value,
      joiningDate: joiningDate == null ? this.joiningDate : joiningDate.value,
      invoiceNo: invoiceNo == null ? this.invoiceNo : invoiceNo.value,
      invoicePrefix: invoicePrefix == null ? this.invoicePrefix : invoicePrefix.value,
      currentFinYear: currentFinYear == null ? this.currentFinYear : currentFinYear.value,
      quickChargeLimit: quickChargeLimit == null ? this.quickChargeLimit : quickChargeLimit.value
    );
  }
  
  CPO.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _name = json['name'],
      _taxId = json['taxId'],
      _city = json['city'],
      _geoState = json['geoState'],
      _contact = json['contact'],
      _email = json['email'],
      _joiningDate = json['joiningDate'] != null ? amplify_core.TemporalDateTime.fromString(json['joiningDate']) : null,
      _invoiceNo = (json['invoiceNo'] as num?)?.toInt(),
      _invoicePrefix = json['invoicePrefix'],
      _currentFinYear = json['currentFinYear'] != null ? amplify_core.TemporalDateTime.fromString(json['currentFinYear']) : null,
      _quickChargeLimit = (json['quickChargeLimit'] as num?)?.toDouble(),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'name': _name, 'taxId': _taxId, 'city': _city, 'geoState': _geoState, 'contact': _contact, 'email': _email, 'joiningDate': _joiningDate?.format(), 'invoiceNo': _invoiceNo, 'invoicePrefix': _invoicePrefix, 'currentFinYear': _currentFinYear?.format(), 'quickChargeLimit': _quickChargeLimit, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'name': _name,
    'taxId': _taxId,
    'city': _city,
    'geoState': _geoState,
    'contact': _contact,
    'email': _email,
    'joiningDate': _joiningDate,
    'invoiceNo': _invoiceNo,
    'invoicePrefix': _invoicePrefix,
    'currentFinYear': _currentFinYear,
    'quickChargeLimit': _quickChargeLimit,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<CPOModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<CPOModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final NAME = amplify_core.QueryField(fieldName: "name");
  static final TAXID = amplify_core.QueryField(fieldName: "taxId");
  static final CITY = amplify_core.QueryField(fieldName: "city");
  static final GEOSTATE = amplify_core.QueryField(fieldName: "geoState");
  static final CONTACT = amplify_core.QueryField(fieldName: "contact");
  static final EMAIL = amplify_core.QueryField(fieldName: "email");
  static final JOININGDATE = amplify_core.QueryField(fieldName: "joiningDate");
  static final INVOICENO = amplify_core.QueryField(fieldName: "invoiceNo");
  static final INVOICEPREFIX = amplify_core.QueryField(fieldName: "invoicePrefix");
  static final CURRENTFINYEAR = amplify_core.QueryField(fieldName: "currentFinYear");
  static final QUICKCHARGELIMIT = amplify_core.QueryField(fieldName: "quickChargeLimit");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "CPO";
    modelSchemaDefinition.pluralName = "CPOS";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.NAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.TAXID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.CITY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.GEOSTATE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.CONTACT,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.EMAIL,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.JOININGDATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.INVOICENO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.INVOICEPREFIX,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.CURRENTFINYEAR,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPO.QUICKCHARGELIMIT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _CPOModelType extends amplify_core.ModelType<CPO> {
  const _CPOModelType();
  
  @override
  CPO fromJson(Map<String, dynamic> jsonData) {
    return CPO.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'CPO';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [CPO] in your schema.
 */
class CPOModelIdentifier implements amplify_core.ModelIdentifier<CPO> {
  final String id;

  /** Create an instance of CPOModelIdentifier using [id] the primary key. */
  const CPOModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'CPOModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is CPOModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}