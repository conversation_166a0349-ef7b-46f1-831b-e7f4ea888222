/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Taxes type in your schema. */
class Taxes extends amplify_core.Model {
  static const classType = const _TaxesModelType();
  final String id;
  final String? _gstno;
  final String? _geoState;
  final double? _rate;
  final String? _cinNo;
  final String? _hsn;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  TaxesModelIdentifier get modelIdentifier {
      return TaxesModelIdentifier(
        id: id
      );
  }
  
  String get gstno {
    try {
      return _gstno!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get geoState {
    try {
      return _geoState!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  double get rate {
    try {
      return _rate!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get cinNo {
    return _cinNo;
  }
  
  String? get hsn {
    return _hsn;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Taxes._internal({required this.id, required gstno, required geoState, required rate, cinNo, hsn, createdAt, updatedAt}): _gstno = gstno, _geoState = geoState, _rate = rate, _cinNo = cinNo, _hsn = hsn, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Taxes({String? id, required String gstno, required String geoState, required double rate, String? cinNo, String? hsn}) {
    return Taxes._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      gstno: gstno,
      geoState: geoState,
      rate: rate,
      cinNo: cinNo,
      hsn: hsn);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Taxes &&
      id == other.id &&
      _gstno == other._gstno &&
      _geoState == other._geoState &&
      _rate == other._rate &&
      _cinNo == other._cinNo &&
      _hsn == other._hsn;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Taxes {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("gstno=" + "$_gstno" + ", ");
    buffer.write("geoState=" + "$_geoState" + ", ");
    buffer.write("rate=" + (_rate != null ? _rate!.toString() : "null") + ", ");
    buffer.write("cinNo=" + "$_cinNo" + ", ");
    buffer.write("hsn=" + "$_hsn" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Taxes copyWith({String? gstno, String? geoState, double? rate, String? cinNo, String? hsn}) {
    return Taxes._internal(
      id: id,
      gstno: gstno ?? this.gstno,
      geoState: geoState ?? this.geoState,
      rate: rate ?? this.rate,
      cinNo: cinNo ?? this.cinNo,
      hsn: hsn ?? this.hsn);
  }
  
  Taxes copyWithModelFieldValues({
    ModelFieldValue<String>? gstno,
    ModelFieldValue<String>? geoState,
    ModelFieldValue<double>? rate,
    ModelFieldValue<String?>? cinNo,
    ModelFieldValue<String?>? hsn
  }) {
    return Taxes._internal(
      id: id,
      gstno: gstno == null ? this.gstno : gstno.value,
      geoState: geoState == null ? this.geoState : geoState.value,
      rate: rate == null ? this.rate : rate.value,
      cinNo: cinNo == null ? this.cinNo : cinNo.value,
      hsn: hsn == null ? this.hsn : hsn.value
    );
  }
  
  Taxes.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _gstno = json['gstno'],
      _geoState = json['geoState'],
      _rate = (json['rate'] as num?)?.toDouble(),
      _cinNo = json['cinNo'],
      _hsn = json['hsn'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'gstno': _gstno, 'geoState': _geoState, 'rate': _rate, 'cinNo': _cinNo, 'hsn': _hsn, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'gstno': _gstno,
    'geoState': _geoState,
    'rate': _rate,
    'cinNo': _cinNo,
    'hsn': _hsn,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<TaxesModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<TaxesModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final GSTNO = amplify_core.QueryField(fieldName: "gstno");
  static final GEOSTATE = amplify_core.QueryField(fieldName: "geoState");
  static final RATE = amplify_core.QueryField(fieldName: "rate");
  static final CINNO = amplify_core.QueryField(fieldName: "cinNo");
  static final HSN = amplify_core.QueryField(fieldName: "hsn");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Taxes";
    modelSchemaDefinition.pluralName = "Taxes";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Taxes.GSTNO,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Taxes.GEOSTATE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Taxes.RATE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Taxes.CINNO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Taxes.HSN,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _TaxesModelType extends amplify_core.ModelType<Taxes> {
  const _TaxesModelType();
  
  @override
  Taxes fromJson(Map<String, dynamic> jsonData) {
    return Taxes.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Taxes';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Taxes] in your schema.
 */
class TaxesModelIdentifier implements amplify_core.ModelIdentifier<Taxes> {
  final String id;

  /** Create an instance of TaxesModelIdentifier using [id] the primary key. */
  const TaxesModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'TaxesModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is TaxesModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}