/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the PaymentData type in your schema. */
class PaymentData extends amplify_core.Model {
  static const classType = const _PaymentDataModelType();
  final String id;
  final String? _bId;
  final String? _uId;
  final String? _type;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  PaymentDataModelIdentifier get modelIdentifier {
      return PaymentDataModelIdentifier(
        id: id
      );
  }
  
  String? get bId {
    return _bId;
  }
  
  String? get uId {
    return _uId;
  }
  
  String? get type {
    return _type;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const PaymentData._internal({required this.id, bId, uId, type, createdAt, updatedAt}): _bId = bId, _uId = uId, _type = type, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory PaymentData({String? id, String? bId, String? uId, String? type}) {
    return PaymentData._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      bId: bId,
      uId: uId,
      type: type);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentData &&
      id == other.id &&
      _bId == other._bId &&
      _uId == other._uId &&
      _type == other._type;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("PaymentData {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("bId=" + "$_bId" + ", ");
    buffer.write("uId=" + "$_uId" + ", ");
    buffer.write("type=" + "$_type" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  PaymentData copyWith({String? bId, String? uId, String? type}) {
    return PaymentData._internal(
      id: id,
      bId: bId ?? this.bId,
      uId: uId ?? this.uId,
      type: type ?? this.type);
  }
  
  PaymentData copyWithModelFieldValues({
    ModelFieldValue<String?>? bId,
    ModelFieldValue<String?>? uId,
    ModelFieldValue<String?>? type
  }) {
    return PaymentData._internal(
      id: id,
      bId: bId == null ? this.bId : bId.value,
      uId: uId == null ? this.uId : uId.value,
      type: type == null ? this.type : type.value
    );
  }
  
  PaymentData.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _bId = json['bId'],
      _uId = json['uId'],
      _type = json['type'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'bId': _bId, 'uId': _uId, 'type': _type, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'bId': _bId,
    'uId': _uId,
    'type': _type,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<PaymentDataModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<PaymentDataModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final BID = amplify_core.QueryField(fieldName: "bId");
  static final UID = amplify_core.QueryField(fieldName: "uId");
  static final TYPE = amplify_core.QueryField(fieldName: "type");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "PaymentData";
    modelSchemaDefinition.pluralName = "PaymentData";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentData.BID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentData.UID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentData.TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _PaymentDataModelType extends amplify_core.ModelType<PaymentData> {
  const _PaymentDataModelType();
  
  @override
  PaymentData fromJson(Map<String, dynamic> jsonData) {
    return PaymentData.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'PaymentData';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [PaymentData] in your schema.
 */
class PaymentDataModelIdentifier implements amplify_core.ModelIdentifier<PaymentData> {
  final String id;

  /** Create an instance of PaymentDataModelIdentifier using [id] the primary key. */
  const PaymentDataModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'PaymentDataModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is PaymentDataModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}