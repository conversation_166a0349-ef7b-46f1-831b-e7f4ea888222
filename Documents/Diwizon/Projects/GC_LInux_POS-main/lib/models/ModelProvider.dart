/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'package:amplify_core/amplify_core.dart' as amplify_core;
import 'AdminUsers.dart';
import 'CPO.dart';
import 'ChargeSessionHis.dart';
import 'Charger.dart';
import 'ChargerCapacity.dart';
import 'ChargerManufacturer.dart';
import 'ChargingTable.dart';
import 'Configuration.dart';
import 'Connector.dart';
import 'ConnectorType.dart';
import 'DueRequest.dart';
import 'EndUser.dart';
import 'GoCaLogs.dart';
import 'InLog.dart';
import 'Issues.dart';
import 'LocalList.dart';
import 'LocationType.dart';
import 'ManufacturerTable.dart';
import 'PaymentData.dart';
import 'PaymentRequestTable.dart';
import 'RFIDSchema.dart';
import 'RefundRecord.dart';
import 'Station.dart';
import 'SupportRequest.dart';
import 'Taxes.dart';
import 'Transaction.dart';
import 'UserVehicle.dart';
import 'Vehicle.dart';

export 'AdminUsers.dart';
export 'CPO.dart';
export 'ChargeSessionHis.dart';
export 'Charger.dart';
export 'ChargerCapacity.dart';
export 'ChargerManufacturer.dart';
export 'ChargingTable.dart';
export 'Configuration.dart';
export 'Connector.dart';
export 'ConnectorType.dart';
export 'DueRequest.dart';
export 'EndUser.dart';
export 'GoCaLogs.dart';
export 'InLog.dart';
export 'Issues.dart';
export 'LocalList.dart';
export 'LocationType.dart';
export 'ManufacturerTable.dart';
export 'PaymentData.dart';
export 'PaymentRequestTable.dart';
export 'RFIDSchema.dart';
export 'RefundRecord.dart';
export 'Station.dart';
export 'SupportRequest.dart';
export 'Taxes.dart';
export 'Transaction.dart';
export 'UserVehicle.dart';
export 'Vehicle.dart';
export 'WalletType.dart';

class ModelProvider implements amplify_core.ModelProviderInterface {
  @override
  String version = "d538ea940fd91e89f6fe6d17954d11b9";
  @override
  List<amplify_core.ModelSchema> modelSchemas = [AdminUsers.schema, CPO.schema, ChargeSessionHis.schema, Charger.schema, ChargerCapacity.schema, ChargerManufacturer.schema, ChargingTable.schema, Configuration.schema, Connector.schema, ConnectorType.schema, DueRequest.schema, EndUser.schema, GoCaLogs.schema, InLog.schema, Issues.schema, LocalList.schema, LocationType.schema, ManufacturerTable.schema, PaymentData.schema, PaymentRequestTable.schema, RFIDSchema.schema, RefundRecord.schema, Station.schema, SupportRequest.schema, Taxes.schema, Transaction.schema, UserVehicle.schema, Vehicle.schema];
  @override
  List<amplify_core.ModelSchema> customTypeSchemas = [];
  static final ModelProvider _instance = ModelProvider();

  static ModelProvider get instance => _instance;
  
  amplify_core.ModelType getModelTypeByModelName(String modelName) {
    switch(modelName) {
      case "AdminUsers":
        return AdminUsers.classType;
      case "CPO":
        return CPO.classType;
      case "ChargeSessionHis":
        return ChargeSessionHis.classType;
      case "Charger":
        return Charger.classType;
      case "ChargerCapacity":
        return ChargerCapacity.classType;
      case "ChargerManufacturer":
        return ChargerManufacturer.classType;
      case "ChargingTable":
        return ChargingTable.classType;
      case "Configuration":
        return Configuration.classType;
      case "Connector":
        return Connector.classType;
      case "ConnectorType":
        return ConnectorType.classType;
      case "DueRequest":
        return DueRequest.classType;
      case "EndUser":
        return EndUser.classType;
      case "GoCaLogs":
        return GoCaLogs.classType;
      case "InLog":
        return InLog.classType;
      case "Issues":
        return Issues.classType;
      case "LocalList":
        return LocalList.classType;
      case "LocationType":
        return LocationType.classType;
      case "ManufacturerTable":
        return ManufacturerTable.classType;
      case "PaymentData":
        return PaymentData.classType;
      case "PaymentRequestTable":
        return PaymentRequestTable.classType;
      case "RFIDSchema":
        return RFIDSchema.classType;
      case "RefundRecord":
        return RefundRecord.classType;
      case "Station":
        return Station.classType;
      case "SupportRequest":
        return SupportRequest.classType;
      case "Taxes":
        return Taxes.classType;
      case "Transaction":
        return Transaction.classType;
      case "UserVehicle":
        return UserVehicle.classType;
      case "Vehicle":
        return Vehicle.classType;
      default:
        throw Exception("Failed to find model in model provider for model name: " + modelName);
    }
  }
}


class ModelFieldValue<T> {
  const ModelFieldValue.value(this.value);

  final T value;
}
