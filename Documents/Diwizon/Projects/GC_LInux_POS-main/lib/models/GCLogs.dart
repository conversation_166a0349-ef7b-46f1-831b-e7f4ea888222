/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the GCLogs type in your schema. */
class GCLogs extends amplify_core.Model {
  static const classType = const _GCLogsModelType();
  final String id;
  final String? _cpId;
  final String? _Event;
  final String? _Log;
  final String? _logSequence;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  GCLogsModelIdentifier get modelIdentifier {
      return GCLogsModelIdentifier(
        id: id
      );
  }
  
  String? get cpId {
    return _cpId;
  }
  
  String? get Event {
    return _Event;
  }
  
  String? get Log {
    return _Log;
  }
  
  String? get logSequence {
    return _logSequence;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const GCLogs._internal({required this.id, cpId, Event, Log, logSequence, createdAt, updatedAt}): _cpId = cpId, _Event = Event, _Log = Log, _logSequence = logSequence, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory GCLogs({String? id, String? cpId, String? Event, String? Log, String? logSequence, amplify_core.TemporalDateTime? createdAt, amplify_core.TemporalDateTime? updatedAt}) {
    return GCLogs._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      cpId: cpId,
      Event: Event,
      Log: Log,
      logSequence: logSequence,
      createdAt: createdAt,
      updatedAt: updatedAt);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GCLogs &&
      id == other.id &&
      _cpId == other._cpId &&
      _Event == other._Event &&
      _Log == other._Log &&
      _logSequence == other._logSequence &&
      _createdAt == other._createdAt &&
      _updatedAt == other._updatedAt;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("GCLogs {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("cpId=" + "$_cpId" + ", ");
    buffer.write("Event=" + "$_Event" + ", ");
    buffer.write("Log=" + "$_Log" + ", ");
    buffer.write("logSequence=" + "$_logSequence" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  GCLogs copyWith({String? cpId, String? Event, String? Log, String? logSequence, amplify_core.TemporalDateTime? createdAt, amplify_core.TemporalDateTime? updatedAt}) {
    return GCLogs._internal(
      id: id,
      cpId: cpId ?? this.cpId,
      Event: Event ?? this.Event,
      Log: Log ?? this.Log,
      logSequence: logSequence ?? this.logSequence,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt);
  }
  
  GCLogs copyWithModelFieldValues({
    ModelFieldValue<String?>? cpId,
    ModelFieldValue<String?>? Event,
    ModelFieldValue<String?>? Log,
    ModelFieldValue<String?>? logSequence,
    ModelFieldValue<amplify_core.TemporalDateTime?>? createdAt,
    ModelFieldValue<amplify_core.TemporalDateTime?>? updatedAt
  }) {
    return GCLogs._internal(
      id: id,
      cpId: cpId == null ? this.cpId : cpId.value,
      Event: Event == null ? this.Event : Event.value,
      Log: Log == null ? this.Log : Log.value,
      logSequence: logSequence == null ? this.logSequence : logSequence.value,
      createdAt: createdAt == null ? this.createdAt : createdAt.value,
      updatedAt: updatedAt == null ? this.updatedAt : updatedAt.value
    );
  }
  
  GCLogs.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _cpId = json['cpId'],
      _Event = json['Event'],
      _Log = json['Log'],
      _logSequence = json['logSequence'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'cpId': _cpId, 'Event': _Event, 'Log': _Log, 'logSequence': _logSequence, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'cpId': _cpId,
    'Event': _Event,
    'Log': _Log,
    'logSequence': _logSequence,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<GCLogsModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<GCLogsModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CPID = amplify_core.QueryField(fieldName: "cpId");
  static final EVENT = amplify_core.QueryField(fieldName: "Event");
  static final LOG = amplify_core.QueryField(fieldName: "Log");
  static final LOGSEQUENCE = amplify_core.QueryField(fieldName: "logSequence");
  static final CREATEDAT = amplify_core.QueryField(fieldName: "createdAt");
  static final UPDATEDAT = amplify_core.QueryField(fieldName: "updatedAt");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "GCLogs";
    modelSchemaDefinition.pluralName = "GCLogs";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.indexes = [
      amplify_core.ModelIndex(fields: const ["id"], name: "gCLogsById")
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.CPID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.EVENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.LOG,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.LOGSEQUENCE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.CREATEDAT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: GCLogs.UPDATEDAT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _GCLogsModelType extends amplify_core.ModelType<GCLogs> {
  const _GCLogsModelType();
  
  @override
  GCLogs fromJson(Map<String, dynamic> jsonData) {
    return GCLogs.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'GCLogs';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [GCLogs] in your schema.
 */
class GCLogsModelIdentifier implements amplify_core.ModelIdentifier<GCLogs> {
  final String id;

  /** Create an instance of GCLogsModelIdentifier using [id] the primary key. */
  const GCLogsModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'GCLogsModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is GCLogsModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}