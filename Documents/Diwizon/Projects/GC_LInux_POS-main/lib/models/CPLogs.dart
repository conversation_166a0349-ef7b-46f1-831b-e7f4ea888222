/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the CPLogs type in your schema. */
class CPLogs extends amplify_core.Model {
  static const classType = const _CPLogsModelType();
  final String id;
  final String? _cpId;
  final amplify_core.TemporalDateTime? _TimeStamp;
  final String? _Event;
  final String? _Log;
  final String? _logSequence;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  CPLogsModelIdentifier get modelIdentifier {
      return CPLogsModelIdentifier(
        id: id
      );
  }
  
  String? get cpId {
    return _cpId;
  }
  
  amplify_core.TemporalDateTime? get TimeStamp {
    return _TimeStamp;
  }
  
  String? get Event {
    return _Event;
  }
  
  String? get Log {
    return _Log;
  }
  
  String? get logSequence {
    return _logSequence;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const CPLogs._internal({required this.id, cpId, TimeStamp, Event, Log, logSequence, createdAt, updatedAt}): _cpId = cpId, _TimeStamp = TimeStamp, _Event = Event, _Log = Log, _logSequence = logSequence, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory CPLogs({String? id, String? cpId, amplify_core.TemporalDateTime? TimeStamp, String? Event, String? Log, String? logSequence}) {
    return CPLogs._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      cpId: cpId,
      TimeStamp: TimeStamp,
      Event: Event,
      Log: Log,
      logSequence: logSequence);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is CPLogs &&
      id == other.id &&
      _cpId == other._cpId &&
      _TimeStamp == other._TimeStamp &&
      _Event == other._Event &&
      _Log == other._Log &&
      _logSequence == other._logSequence;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("CPLogs {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("cpId=" + "$_cpId" + ", ");
    buffer.write("TimeStamp=" + (_TimeStamp != null ? _TimeStamp!.format() : "null") + ", ");
    buffer.write("Event=" + "$_Event" + ", ");
    buffer.write("Log=" + "$_Log" + ", ");
    buffer.write("logSequence=" + "$_logSequence" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  CPLogs copyWith({String? cpId, amplify_core.TemporalDateTime? TimeStamp, String? Event, String? Log, String? logSequence}) {
    return CPLogs._internal(
      id: id,
      cpId: cpId ?? this.cpId,
      TimeStamp: TimeStamp ?? this.TimeStamp,
      Event: Event ?? this.Event,
      Log: Log ?? this.Log,
      logSequence: logSequence ?? this.logSequence);
  }
  
  CPLogs copyWithModelFieldValues({
    ModelFieldValue<String?>? cpId,
    ModelFieldValue<amplify_core.TemporalDateTime?>? TimeStamp,
    ModelFieldValue<String?>? Event,
    ModelFieldValue<String?>? Log,
    ModelFieldValue<String?>? logSequence
  }) {
    return CPLogs._internal(
      id: id,
      cpId: cpId == null ? this.cpId : cpId.value,
      TimeStamp: TimeStamp == null ? this.TimeStamp : TimeStamp.value,
      Event: Event == null ? this.Event : Event.value,
      Log: Log == null ? this.Log : Log.value,
      logSequence: logSequence == null ? this.logSequence : logSequence.value
    );
  }
  
  CPLogs.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _cpId = json['cpId'],
      _TimeStamp = json['TimeStamp'] != null ? amplify_core.TemporalDateTime.fromString(json['TimeStamp']) : null,
      _Event = json['Event'],
      _Log = json['Log'],
      _logSequence = json['logSequence'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'cpId': _cpId, 'TimeStamp': _TimeStamp?.format(), 'Event': _Event, 'Log': _Log, 'logSequence': _logSequence, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'cpId': _cpId,
    'TimeStamp': _TimeStamp,
    'Event': _Event,
    'Log': _Log,
    'logSequence': _logSequence,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<CPLogsModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<CPLogsModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CPID = amplify_core.QueryField(fieldName: "cpId");
  static final TIMESTAMP = amplify_core.QueryField(fieldName: "TimeStamp");
  static final EVENT = amplify_core.QueryField(fieldName: "Event");
  static final LOG = amplify_core.QueryField(fieldName: "Log");
  static final LOGSEQUENCE = amplify_core.QueryField(fieldName: "logSequence");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "CPLogs";
    modelSchemaDefinition.pluralName = "CPLogs";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPLogs.CPID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPLogs.TIMESTAMP,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPLogs.EVENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPLogs.LOG,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: CPLogs.LOGSEQUENCE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _CPLogsModelType extends amplify_core.ModelType<CPLogs> {
  const _CPLogsModelType();
  
  @override
  CPLogs fromJson(Map<String, dynamic> jsonData) {
    return CPLogs.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'CPLogs';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [CPLogs] in your schema.
 */
class CPLogsModelIdentifier implements amplify_core.ModelIdentifier<CPLogs> {
  final String id;

  /** Create an instance of CPLogsModelIdentifier using [id] the primary key. */
  const CPLogsModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'CPLogsModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is CPLogsModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}