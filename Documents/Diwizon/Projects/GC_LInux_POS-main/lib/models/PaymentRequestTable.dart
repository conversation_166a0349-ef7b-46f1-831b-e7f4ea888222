/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the PaymentRequestTable type in your schema. */
class PaymentRequestTable extends amplify_core.Model {
  static const classType = const _PaymentRequestTableModelType();
  final String id;
  final String? _gateway;
  final String? _emailId;
  final String? _phoneNo;
  final String? _customerId;
  final String? _OrderId;
  final String? _transactionAmount;
  final String? _callbackurl;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  PaymentRequestTableModelIdentifier get modelIdentifier {
      return PaymentRequestTableModelIdentifier(
        id: id
      );
  }
  
  String? get gateway {
    return _gateway;
  }
  
  String? get emailId {
    return _emailId;
  }
  
  String? get phoneNo {
    return _phoneNo;
  }
  
  String? get customerId {
    return _customerId;
  }
  
  String? get OrderId {
    return _OrderId;
  }
  
  String? get transactionAmount {
    return _transactionAmount;
  }
  
  String? get callbackurl {
    return _callbackurl;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const PaymentRequestTable._internal({required this.id, gateway, emailId, phoneNo, customerId, OrderId, transactionAmount, callbackurl, createdAt, updatedAt}): _gateway = gateway, _emailId = emailId, _phoneNo = phoneNo, _customerId = customerId, _OrderId = OrderId, _transactionAmount = transactionAmount, _callbackurl = callbackurl, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory PaymentRequestTable({String? id, String? gateway, String? emailId, String? phoneNo, String? customerId, String? OrderId, String? transactionAmount, String? callbackurl}) {
    return PaymentRequestTable._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      gateway: gateway,
      emailId: emailId,
      phoneNo: phoneNo,
      customerId: customerId,
      OrderId: OrderId,
      transactionAmount: transactionAmount,
      callbackurl: callbackurl);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is PaymentRequestTable &&
      id == other.id &&
      _gateway == other._gateway &&
      _emailId == other._emailId &&
      _phoneNo == other._phoneNo &&
      _customerId == other._customerId &&
      _OrderId == other._OrderId &&
      _transactionAmount == other._transactionAmount &&
      _callbackurl == other._callbackurl;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("PaymentRequestTable {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("gateway=" + "$_gateway" + ", ");
    buffer.write("emailId=" + "$_emailId" + ", ");
    buffer.write("phoneNo=" + "$_phoneNo" + ", ");
    buffer.write("customerId=" + "$_customerId" + ", ");
    buffer.write("OrderId=" + "$_OrderId" + ", ");
    buffer.write("transactionAmount=" + "$_transactionAmount" + ", ");
    buffer.write("callbackurl=" + "$_callbackurl" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  PaymentRequestTable copyWith({String? gateway, String? emailId, String? phoneNo, String? customerId, String? OrderId, String? transactionAmount, String? callbackurl}) {
    return PaymentRequestTable._internal(
      id: id,
      gateway: gateway ?? this.gateway,
      emailId: emailId ?? this.emailId,
      phoneNo: phoneNo ?? this.phoneNo,
      customerId: customerId ?? this.customerId,
      OrderId: OrderId ?? this.OrderId,
      transactionAmount: transactionAmount ?? this.transactionAmount,
      callbackurl: callbackurl ?? this.callbackurl);
  }
  
  PaymentRequestTable copyWithModelFieldValues({
    ModelFieldValue<String?>? gateway,
    ModelFieldValue<String?>? emailId,
    ModelFieldValue<String?>? phoneNo,
    ModelFieldValue<String?>? customerId,
    ModelFieldValue<String?>? OrderId,
    ModelFieldValue<String?>? transactionAmount,
    ModelFieldValue<String?>? callbackurl
  }) {
    return PaymentRequestTable._internal(
      id: id,
      gateway: gateway == null ? this.gateway : gateway.value,
      emailId: emailId == null ? this.emailId : emailId.value,
      phoneNo: phoneNo == null ? this.phoneNo : phoneNo.value,
      customerId: customerId == null ? this.customerId : customerId.value,
      OrderId: OrderId == null ? this.OrderId : OrderId.value,
      transactionAmount: transactionAmount == null ? this.transactionAmount : transactionAmount.value,
      callbackurl: callbackurl == null ? this.callbackurl : callbackurl.value
    );
  }
  
  PaymentRequestTable.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _gateway = json['gateway'],
      _emailId = json['emailId'],
      _phoneNo = json['phoneNo'],
      _customerId = json['customerId'],
      _OrderId = json['OrderId'],
      _transactionAmount = json['transactionAmount'],
      _callbackurl = json['callbackurl'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'gateway': _gateway, 'emailId': _emailId, 'phoneNo': _phoneNo, 'customerId': _customerId, 'OrderId': _OrderId, 'transactionAmount': _transactionAmount, 'callbackurl': _callbackurl, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'gateway': _gateway,
    'emailId': _emailId,
    'phoneNo': _phoneNo,
    'customerId': _customerId,
    'OrderId': _OrderId,
    'transactionAmount': _transactionAmount,
    'callbackurl': _callbackurl,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<PaymentRequestTableModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<PaymentRequestTableModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final GATEWAY = amplify_core.QueryField(fieldName: "gateway");
  static final EMAILID = amplify_core.QueryField(fieldName: "emailId");
  static final PHONENO = amplify_core.QueryField(fieldName: "phoneNo");
  static final CUSTOMERID = amplify_core.QueryField(fieldName: "customerId");
  static final ORDERID = amplify_core.QueryField(fieldName: "OrderId");
  static final TRANSACTIONAMOUNT = amplify_core.QueryField(fieldName: "transactionAmount");
  static final CALLBACKURL = amplify_core.QueryField(fieldName: "callbackurl");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "PaymentRequestTable";
    modelSchemaDefinition.pluralName = "PaymentRequestTables";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.GATEWAY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.EMAILID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.PHONENO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.CUSTOMERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.ORDERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.TRANSACTIONAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: PaymentRequestTable.CALLBACKURL,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _PaymentRequestTableModelType extends amplify_core.ModelType<PaymentRequestTable> {
  const _PaymentRequestTableModelType();
  
  @override
  PaymentRequestTable fromJson(Map<String, dynamic> jsonData) {
    return PaymentRequestTable.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'PaymentRequestTable';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [PaymentRequestTable] in your schema.
 */
class PaymentRequestTableModelIdentifier implements amplify_core.ModelIdentifier<PaymentRequestTable> {
  final String id;

  /** Create an instance of PaymentRequestTableModelIdentifier using [id] the primary key. */
  const PaymentRequestTableModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'PaymentRequestTableModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is PaymentRequestTableModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}