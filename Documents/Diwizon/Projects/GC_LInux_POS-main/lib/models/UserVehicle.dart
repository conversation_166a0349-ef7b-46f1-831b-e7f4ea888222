/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the UserVehicle type in your schema. */
class UserVehicle extends amplify_core.Model {
  static const classType = const _UserVehicleModelType();
  final String id;
  final String? _vehical_name;
  final String? _vehical_number;
  final String? _connector_type;
  final String? _userId;
  final String? _batteryCapacity;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  UserVehicleModelIdentifier get modelIdentifier {
      return UserVehicleModelIdentifier(
        id: id
      );
  }
  
  String get vehical_name {
    try {
      return _vehical_name!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get vehical_number {
    try {
      return _vehical_number!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get connector_type {
    return _connector_type;
  }
  
  String get userId {
    try {
      return _userId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get batteryCapacity {
    return _batteryCapacity;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const UserVehicle._internal({required this.id, required vehical_name, required vehical_number, connector_type, required userId, batteryCapacity, createdAt, updatedAt}): _vehical_name = vehical_name, _vehical_number = vehical_number, _connector_type = connector_type, _userId = userId, _batteryCapacity = batteryCapacity, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory UserVehicle({String? id, required String vehical_name, required String vehical_number, String? connector_type, required String userId, String? batteryCapacity}) {
    return UserVehicle._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      vehical_name: vehical_name,
      vehical_number: vehical_number,
      connector_type: connector_type,
      userId: userId,
      batteryCapacity: batteryCapacity);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserVehicle &&
      id == other.id &&
      _vehical_name == other._vehical_name &&
      _vehical_number == other._vehical_number &&
      _connector_type == other._connector_type &&
      _userId == other._userId &&
      _batteryCapacity == other._batteryCapacity;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("UserVehicle {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("vehical_name=" + "$_vehical_name" + ", ");
    buffer.write("vehical_number=" + "$_vehical_number" + ", ");
    buffer.write("connector_type=" + "$_connector_type" + ", ");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("batteryCapacity=" + "$_batteryCapacity" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  UserVehicle copyWith({String? vehical_name, String? vehical_number, String? connector_type, String? userId, String? batteryCapacity}) {
    return UserVehicle._internal(
      id: id,
      vehical_name: vehical_name ?? this.vehical_name,
      vehical_number: vehical_number ?? this.vehical_number,
      connector_type: connector_type ?? this.connector_type,
      userId: userId ?? this.userId,
      batteryCapacity: batteryCapacity ?? this.batteryCapacity);
  }
  
  UserVehicle copyWithModelFieldValues({
    ModelFieldValue<String>? vehical_name,
    ModelFieldValue<String>? vehical_number,
    ModelFieldValue<String?>? connector_type,
    ModelFieldValue<String>? userId,
    ModelFieldValue<String?>? batteryCapacity
  }) {
    return UserVehicle._internal(
      id: id,
      vehical_name: vehical_name == null ? this.vehical_name : vehical_name.value,
      vehical_number: vehical_number == null ? this.vehical_number : vehical_number.value,
      connector_type: connector_type == null ? this.connector_type : connector_type.value,
      userId: userId == null ? this.userId : userId.value,
      batteryCapacity: batteryCapacity == null ? this.batteryCapacity : batteryCapacity.value
    );
  }
  
  UserVehicle.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _vehical_name = json['vehical_name'],
      _vehical_number = json['vehical_number'],
      _connector_type = json['connector_type'],
      _userId = json['userId'],
      _batteryCapacity = json['batteryCapacity'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'vehical_name': _vehical_name, 'vehical_number': _vehical_number, 'connector_type': _connector_type, 'userId': _userId, 'batteryCapacity': _batteryCapacity, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'vehical_name': _vehical_name,
    'vehical_number': _vehical_number,
    'connector_type': _connector_type,
    'userId': _userId,
    'batteryCapacity': _batteryCapacity,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<UserVehicleModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<UserVehicleModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final VEHICAL_NAME = amplify_core.QueryField(fieldName: "vehical_name");
  static final VEHICAL_NUMBER = amplify_core.QueryField(fieldName: "vehical_number");
  static final CONNECTOR_TYPE = amplify_core.QueryField(fieldName: "connector_type");
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static final BATTERYCAPACITY = amplify_core.QueryField(fieldName: "batteryCapacity");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "UserVehicle";
    modelSchemaDefinition.pluralName = "UserVehicles";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserVehicle.VEHICAL_NAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserVehicle.VEHICAL_NUMBER,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserVehicle.CONNECTOR_TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserVehicle.USERID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserVehicle.BATTERYCAPACITY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _UserVehicleModelType extends amplify_core.ModelType<UserVehicle> {
  const _UserVehicleModelType();
  
  @override
  UserVehicle fromJson(Map<String, dynamic> jsonData) {
    return UserVehicle.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'UserVehicle';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [UserVehicle] in your schema.
 */
class UserVehicleModelIdentifier implements amplify_core.ModelIdentifier<UserVehicle> {
  final String id;

  /** Create an instance of UserVehicleModelIdentifier using [id] the primary key. */
  const UserVehicleModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'UserVehicleModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is UserVehicleModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}