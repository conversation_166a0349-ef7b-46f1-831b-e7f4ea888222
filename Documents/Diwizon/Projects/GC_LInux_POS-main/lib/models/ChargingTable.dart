/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the ChargingTable type in your schema. */
class ChargingTable extends amplify_core.Model {
  static const classType = const _ChargingTableModelType();
  final String id;
  final String? _booking_id;
  final amplify_core.TemporalDateTime? _start_time;
  final amplify_core.TemporalDateTime? _end_time;
  final String? _status;
  final int? _connector_no;
  final int? _CurrentMeterWatt;
  final String? _city;
  final double? _charging_fee;
  final String? _payment_status;
  final amplify_core.TemporalDateTime? _createdAt;
  final double? _tax_amount;
  final String? _vehical_number;
  final String? _chargePointId;
  final String? _user_id;
  final String? _station_id;
  final String? _vehicle_id;
  final String? _charging_percent;
  final int? _MeterStartWatt;
  final int? _MeterEndWatt;
  final String? _booking_type;
  final String? _compareValue;
  final double? _pricePerKw;
  final String? _geoState;
  final bool? _isPaid;
  final String? _chargerId;
  final int? _transactionId;
  final double? _estimatedDuration;
  final double? _estimatedUnits;
  final int? _startedAtPercent;
  final int? _stopedAtPercent;
  final double? _unitsBurned;
  final double? _costOfConsump;
  final double? _refundedAmount;
  final amplify_core.TemporalDateTime? _startedAtTime;
  final amplify_core.TemporalDateTime? _stopedAtTime;
  final double? _amountFromWallet;
  final String? _transDocId;
  final String? _payment_Id;
  final amplify_core.TemporalDateTime? _paymentTime;
  final String? _lastCommand;
  final String? _gstin;
  final String? _gstName;
  final String? _userName;
  final String? _userContact;
  final bool? _overchargeDueCleared;
  final int? _invoiceNo;
  final double? _dueAmount;
  final String? _stationName;
  final String? _cpoName;
  final String? _cpoId;
  final double? _taxPercent;
  final bool? _isIgst;
  final double? _igstAmount;
  final double? _sgstAmount;
  final double? _cgstAmount;
  final double? _baseAmount;
  final String? _invoiceId;
  final String? _rfid;
  final String? _pgTransRef;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ChargingTableModelIdentifier get modelIdentifier {
      return ChargingTableModelIdentifier(
        id: id
      );
  }
  
  String? get booking_id {
    return _booking_id;
  }
  
  amplify_core.TemporalDateTime? get start_time {
    return _start_time;
  }
  
  amplify_core.TemporalDateTime? get end_time {
    return _end_time;
  }
  
  String? get status {
    return _status;
  }
  
  int? get connector_no {
    return _connector_no;
  }
  
  int? get CurrentMeterWatt {
    return _CurrentMeterWatt;
  }
  
  String? get city {
    return _city;
  }
  
  double? get charging_fee {
    return _charging_fee;
  }
  
  String? get payment_status {
    return _payment_status;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  double? get tax_amount {
    return _tax_amount;
  }
  
  String? get vehical_number {
    return _vehical_number;
  }
  
  String? get chargePointId {
    return _chargePointId;
  }
  
  String? get user_id {
    return _user_id;
  }
  
  String? get station_id {
    return _station_id;
  }
  
  String? get vehicle_id {
    return _vehicle_id;
  }
  
  String? get charging_percent {
    return _charging_percent;
  }
  
  int? get MeterStartWatt {
    return _MeterStartWatt;
  }
  
  int? get MeterEndWatt {
    return _MeterEndWatt;
  }
  
  String? get booking_type {
    return _booking_type;
  }
  
  String? get compareValue {
    return _compareValue;
  }
  
  double? get pricePerKw {
    return _pricePerKw;
  }
  
  String? get geoState {
    return _geoState;
  }
  
  bool? get isPaid {
    return _isPaid;
  }
  
  String? get chargerId {
    return _chargerId;
  }
  
  int? get transactionId {
    return _transactionId;
  }
  
  double? get estimatedDuration {
    return _estimatedDuration;
  }
  
  double? get estimatedUnits {
    return _estimatedUnits;
  }
  
  int? get startedAtPercent {
    return _startedAtPercent;
  }
  
  int? get stopedAtPercent {
    return _stopedAtPercent;
  }
  
  double? get unitsBurned {
    return _unitsBurned;
  }
  
  double? get costOfConsump {
    return _costOfConsump;
  }
  
  double? get refundedAmount {
    return _refundedAmount;
  }
  
  amplify_core.TemporalDateTime? get startedAtTime {
    return _startedAtTime;
  }
  
  amplify_core.TemporalDateTime? get stopedAtTime {
    return _stopedAtTime;
  }
  
  double? get amountFromWallet {
    return _amountFromWallet;
  }
  
  String? get transDocId {
    return _transDocId;
  }
  
  String? get payment_Id {
    return _payment_Id;
  }
  
  amplify_core.TemporalDateTime? get paymentTime {
    return _paymentTime;
  }
  
  String? get lastCommand {
    return _lastCommand;
  }
  
  String? get gstin {
    return _gstin;
  }
  
  String? get gstName {
    return _gstName;
  }
  
  String? get userName {
    return _userName;
  }
  
  String? get userContact {
    return _userContact;
  }
  
  bool? get overchargeDueCleared {
    return _overchargeDueCleared;
  }
  
  int? get invoiceNo {
    return _invoiceNo;
  }
  
  double? get dueAmount {
    return _dueAmount;
  }
  
  String? get stationName {
    return _stationName;
  }
  
  String? get cpoName {
    return _cpoName;
  }
  
  String? get cpoId {
    return _cpoId;
  }
  
  double? get taxPercent {
    return _taxPercent;
  }
  
  bool? get isIgst {
    return _isIgst;
  }
  
  double? get igstAmount {
    return _igstAmount;
  }
  
  double? get sgstAmount {
    return _sgstAmount;
  }
  
  double? get cgstAmount {
    return _cgstAmount;
  }
  
  double? get baseAmount {
    return _baseAmount;
  }
  
  String? get invoiceId {
    return _invoiceId;
  }
  
  String? get rfid {
    return _rfid;
  }
  
  String? get pgTransRef {
    return _pgTransRef;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const ChargingTable._internal({required this.id, booking_id, start_time, end_time, status, connector_no, CurrentMeterWatt, city, charging_fee, payment_status, createdAt, tax_amount, vehical_number, chargePointId, user_id, station_id, vehicle_id, charging_percent, MeterStartWatt, MeterEndWatt, booking_type, compareValue, pricePerKw, geoState, isPaid, chargerId, transactionId, estimatedDuration, estimatedUnits, startedAtPercent, stopedAtPercent, unitsBurned, costOfConsump, refundedAmount, startedAtTime, stopedAtTime, amountFromWallet, transDocId, payment_Id, paymentTime, lastCommand, gstin, gstName, userName, userContact, overchargeDueCleared, invoiceNo, dueAmount, stationName, cpoName, cpoId, taxPercent, isIgst, igstAmount, sgstAmount, cgstAmount, baseAmount, invoiceId, rfid, pgTransRef, updatedAt}): _booking_id = booking_id, _start_time = start_time, _end_time = end_time, _status = status, _connector_no = connector_no, _CurrentMeterWatt = CurrentMeterWatt, _city = city, _charging_fee = charging_fee, _payment_status = payment_status, _createdAt = createdAt, _tax_amount = tax_amount, _vehical_number = vehical_number, _chargePointId = chargePointId, _user_id = user_id, _station_id = station_id, _vehicle_id = vehicle_id, _charging_percent = charging_percent, _MeterStartWatt = MeterStartWatt, _MeterEndWatt = MeterEndWatt, _booking_type = booking_type, _compareValue = compareValue, _pricePerKw = pricePerKw, _geoState = geoState, _isPaid = isPaid, _chargerId = chargerId, _transactionId = transactionId, _estimatedDuration = estimatedDuration, _estimatedUnits = estimatedUnits, _startedAtPercent = startedAtPercent, _stopedAtPercent = stopedAtPercent, _unitsBurned = unitsBurned, _costOfConsump = costOfConsump, _refundedAmount = refundedAmount, _startedAtTime = startedAtTime, _stopedAtTime = stopedAtTime, _amountFromWallet = amountFromWallet, _transDocId = transDocId, _payment_Id = payment_Id, _paymentTime = paymentTime, _lastCommand = lastCommand, _gstin = gstin, _gstName = gstName, _userName = userName, _userContact = userContact, _overchargeDueCleared = overchargeDueCleared, _invoiceNo = invoiceNo, _dueAmount = dueAmount, _stationName = stationName, _cpoName = cpoName, _cpoId = cpoId, _taxPercent = taxPercent, _isIgst = isIgst, _igstAmount = igstAmount, _sgstAmount = sgstAmount, _cgstAmount = cgstAmount, _baseAmount = baseAmount, _invoiceId = invoiceId, _rfid = rfid, _pgTransRef = pgTransRef, _updatedAt = updatedAt;
  
  factory ChargingTable({String? id, String? booking_id, amplify_core.TemporalDateTime? start_time, amplify_core.TemporalDateTime? end_time, String? status, int? connector_no, int? CurrentMeterWatt, String? city, double? charging_fee, String? payment_status, amplify_core.TemporalDateTime? createdAt, double? tax_amount, String? vehical_number, String? chargePointId, String? user_id, String? station_id, String? vehicle_id, String? charging_percent, int? MeterStartWatt, int? MeterEndWatt, String? booking_type, String? compareValue, double? pricePerKw, String? geoState, bool? isPaid, String? chargerId, int? transactionId, double? estimatedDuration, double? estimatedUnits, int? startedAtPercent, int? stopedAtPercent, double? unitsBurned, double? costOfConsump, double? refundedAmount, amplify_core.TemporalDateTime? startedAtTime, amplify_core.TemporalDateTime? stopedAtTime, double? amountFromWallet, String? transDocId, String? payment_Id, amplify_core.TemporalDateTime? paymentTime, String? lastCommand, String? gstin, String? gstName, String? userName, String? userContact, bool? overchargeDueCleared, int? invoiceNo, double? dueAmount, String? stationName, String? cpoName, String? cpoId, double? taxPercent, bool? isIgst, double? igstAmount, double? sgstAmount, double? cgstAmount, double? baseAmount, String? invoiceId, String? rfid, String? pgTransRef}) {
    return ChargingTable._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      booking_id: booking_id,
      start_time: start_time,
      end_time: end_time,
      status: status,
      connector_no: connector_no,
      CurrentMeterWatt: CurrentMeterWatt,
      city: city,
      charging_fee: charging_fee,
      payment_status: payment_status,
      createdAt: createdAt,
      tax_amount: tax_amount,
      vehical_number: vehical_number,
      chargePointId: chargePointId,
      user_id: user_id,
      station_id: station_id,
      vehicle_id: vehicle_id,
      charging_percent: charging_percent,
      MeterStartWatt: MeterStartWatt,
      MeterEndWatt: MeterEndWatt,
      booking_type: booking_type,
      compareValue: compareValue,
      pricePerKw: pricePerKw,
      geoState: geoState,
      isPaid: isPaid,
      chargerId: chargerId,
      transactionId: transactionId,
      estimatedDuration: estimatedDuration,
      estimatedUnits: estimatedUnits,
      startedAtPercent: startedAtPercent,
      stopedAtPercent: stopedAtPercent,
      unitsBurned: unitsBurned,
      costOfConsump: costOfConsump,
      refundedAmount: refundedAmount,
      startedAtTime: startedAtTime,
      stopedAtTime: stopedAtTime,
      amountFromWallet: amountFromWallet,
      transDocId: transDocId,
      payment_Id: payment_Id,
      paymentTime: paymentTime,
      lastCommand: lastCommand,
      gstin: gstin,
      gstName: gstName,
      userName: userName,
      userContact: userContact,
      overchargeDueCleared: overchargeDueCleared,
      invoiceNo: invoiceNo,
      dueAmount: dueAmount,
      stationName: stationName,
      cpoName: cpoName,
      cpoId: cpoId,
      taxPercent: taxPercent,
      isIgst: isIgst,
      igstAmount: igstAmount,
      sgstAmount: sgstAmount,
      cgstAmount: cgstAmount,
      baseAmount: baseAmount,
      invoiceId: invoiceId,
      rfid: rfid,
      pgTransRef: pgTransRef);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ChargingTable &&
      id == other.id &&
      _booking_id == other._booking_id &&
      _start_time == other._start_time &&
      _end_time == other._end_time &&
      _status == other._status &&
      _connector_no == other._connector_no &&
      _CurrentMeterWatt == other._CurrentMeterWatt &&
      _city == other._city &&
      _charging_fee == other._charging_fee &&
      _payment_status == other._payment_status &&
      _createdAt == other._createdAt &&
      _tax_amount == other._tax_amount &&
      _vehical_number == other._vehical_number &&
      _chargePointId == other._chargePointId &&
      _user_id == other._user_id &&
      _station_id == other._station_id &&
      _vehicle_id == other._vehicle_id &&
      _charging_percent == other._charging_percent &&
      _MeterStartWatt == other._MeterStartWatt &&
      _MeterEndWatt == other._MeterEndWatt &&
      _booking_type == other._booking_type &&
      _compareValue == other._compareValue &&
      _pricePerKw == other._pricePerKw &&
      _geoState == other._geoState &&
      _isPaid == other._isPaid &&
      _chargerId == other._chargerId &&
      _transactionId == other._transactionId &&
      _estimatedDuration == other._estimatedDuration &&
      _estimatedUnits == other._estimatedUnits &&
      _startedAtPercent == other._startedAtPercent &&
      _stopedAtPercent == other._stopedAtPercent &&
      _unitsBurned == other._unitsBurned &&
      _costOfConsump == other._costOfConsump &&
      _refundedAmount == other._refundedAmount &&
      _startedAtTime == other._startedAtTime &&
      _stopedAtTime == other._stopedAtTime &&
      _amountFromWallet == other._amountFromWallet &&
      _transDocId == other._transDocId &&
      _payment_Id == other._payment_Id &&
      _paymentTime == other._paymentTime &&
      _lastCommand == other._lastCommand &&
      _gstin == other._gstin &&
      _gstName == other._gstName &&
      _userName == other._userName &&
      _userContact == other._userContact &&
      _overchargeDueCleared == other._overchargeDueCleared &&
      _invoiceNo == other._invoiceNo &&
      _dueAmount == other._dueAmount &&
      _stationName == other._stationName &&
      _cpoName == other._cpoName &&
      _cpoId == other._cpoId &&
      _taxPercent == other._taxPercent &&
      _isIgst == other._isIgst &&
      _igstAmount == other._igstAmount &&
      _sgstAmount == other._sgstAmount &&
      _cgstAmount == other._cgstAmount &&
      _baseAmount == other._baseAmount &&
      _invoiceId == other._invoiceId &&
      _rfid == other._rfid &&
      _pgTransRef == other._pgTransRef;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("ChargingTable {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("booking_id=" + "$_booking_id" + ", ");
    buffer.write("start_time=" + (_start_time != null ? _start_time!.format() : "null") + ", ");
    buffer.write("end_time=" + (_end_time != null ? _end_time!.format() : "null") + ", ");
    buffer.write("status=" + "$_status" + ", ");
    buffer.write("connector_no=" + (_connector_no != null ? _connector_no!.toString() : "null") + ", ");
    buffer.write("CurrentMeterWatt=" + (_CurrentMeterWatt != null ? _CurrentMeterWatt!.toString() : "null") + ", ");
    buffer.write("city=" + "$_city" + ", ");
    buffer.write("charging_fee=" + (_charging_fee != null ? _charging_fee!.toString() : "null") + ", ");
    buffer.write("payment_status=" + "$_payment_status" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("tax_amount=" + (_tax_amount != null ? _tax_amount!.toString() : "null") + ", ");
    buffer.write("vehical_number=" + "$_vehical_number" + ", ");
    buffer.write("chargePointId=" + "$_chargePointId" + ", ");
    buffer.write("user_id=" + "$_user_id" + ", ");
    buffer.write("station_id=" + "$_station_id" + ", ");
    buffer.write("vehicle_id=" + "$_vehicle_id" + ", ");
    buffer.write("charging_percent=" + "$_charging_percent" + ", ");
    buffer.write("MeterStartWatt=" + (_MeterStartWatt != null ? _MeterStartWatt!.toString() : "null") + ", ");
    buffer.write("MeterEndWatt=" + (_MeterEndWatt != null ? _MeterEndWatt!.toString() : "null") + ", ");
    buffer.write("booking_type=" + "$_booking_type" + ", ");
    buffer.write("compareValue=" + "$_compareValue" + ", ");
    buffer.write("pricePerKw=" + (_pricePerKw != null ? _pricePerKw!.toString() : "null") + ", ");
    buffer.write("geoState=" + "$_geoState" + ", ");
    buffer.write("isPaid=" + (_isPaid != null ? _isPaid!.toString() : "null") + ", ");
    buffer.write("chargerId=" + "$_chargerId" + ", ");
    buffer.write("transactionId=" + (_transactionId != null ? _transactionId!.toString() : "null") + ", ");
    buffer.write("estimatedDuration=" + (_estimatedDuration != null ? _estimatedDuration!.toString() : "null") + ", ");
    buffer.write("estimatedUnits=" + (_estimatedUnits != null ? _estimatedUnits!.toString() : "null") + ", ");
    buffer.write("startedAtPercent=" + (_startedAtPercent != null ? _startedAtPercent!.toString() : "null") + ", ");
    buffer.write("stopedAtPercent=" + (_stopedAtPercent != null ? _stopedAtPercent!.toString() : "null") + ", ");
    buffer.write("unitsBurned=" + (_unitsBurned != null ? _unitsBurned!.toString() : "null") + ", ");
    buffer.write("costOfConsump=" + (_costOfConsump != null ? _costOfConsump!.toString() : "null") + ", ");
    buffer.write("refundedAmount=" + (_refundedAmount != null ? _refundedAmount!.toString() : "null") + ", ");
    buffer.write("startedAtTime=" + (_startedAtTime != null ? _startedAtTime!.format() : "null") + ", ");
    buffer.write("stopedAtTime=" + (_stopedAtTime != null ? _stopedAtTime!.format() : "null") + ", ");
    buffer.write("amountFromWallet=" + (_amountFromWallet != null ? _amountFromWallet!.toString() : "null") + ", ");
    buffer.write("transDocId=" + "$_transDocId" + ", ");
    buffer.write("payment_Id=" + "$_payment_Id" + ", ");
    buffer.write("paymentTime=" + (_paymentTime != null ? _paymentTime!.format() : "null") + ", ");
    buffer.write("lastCommand=" + "$_lastCommand" + ", ");
    buffer.write("gstin=" + "$_gstin" + ", ");
    buffer.write("gstName=" + "$_gstName" + ", ");
    buffer.write("userName=" + "$_userName" + ", ");
    buffer.write("userContact=" + "$_userContact" + ", ");
    buffer.write("overchargeDueCleared=" + (_overchargeDueCleared != null ? _overchargeDueCleared!.toString() : "null") + ", ");
    buffer.write("invoiceNo=" + (_invoiceNo != null ? _invoiceNo!.toString() : "null") + ", ");
    buffer.write("dueAmount=" + (_dueAmount != null ? _dueAmount!.toString() : "null") + ", ");
    buffer.write("stationName=" + "$_stationName" + ", ");
    buffer.write("cpoName=" + "$_cpoName" + ", ");
    buffer.write("cpoId=" + "$_cpoId" + ", ");
    buffer.write("taxPercent=" + (_taxPercent != null ? _taxPercent!.toString() : "null") + ", ");
    buffer.write("isIgst=" + (_isIgst != null ? _isIgst!.toString() : "null") + ", ");
    buffer.write("igstAmount=" + (_igstAmount != null ? _igstAmount!.toString() : "null") + ", ");
    buffer.write("sgstAmount=" + (_sgstAmount != null ? _sgstAmount!.toString() : "null") + ", ");
    buffer.write("cgstAmount=" + (_cgstAmount != null ? _cgstAmount!.toString() : "null") + ", ");
    buffer.write("baseAmount=" + (_baseAmount != null ? _baseAmount!.toString() : "null") + ", ");
    buffer.write("invoiceId=" + "$_invoiceId" + ", ");
    buffer.write("rfid=" + "$_rfid" + ", ");
    buffer.write("pgTransRef=" + "$_pgTransRef" + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  ChargingTable copyWith({String? booking_id, amplify_core.TemporalDateTime? start_time, amplify_core.TemporalDateTime? end_time, String? status, int? connector_no, int? CurrentMeterWatt, String? city, double? charging_fee, String? payment_status, amplify_core.TemporalDateTime? createdAt, double? tax_amount, String? vehical_number, String? chargePointId, String? user_id, String? station_id, String? vehicle_id, String? charging_percent, int? MeterStartWatt, int? MeterEndWatt, String? booking_type, String? compareValue, double? pricePerKw, String? geoState, bool? isPaid, String? chargerId, int? transactionId, double? estimatedDuration, double? estimatedUnits, int? startedAtPercent, int? stopedAtPercent, double? unitsBurned, double? costOfConsump, double? refundedAmount, amplify_core.TemporalDateTime? startedAtTime, amplify_core.TemporalDateTime? stopedAtTime, double? amountFromWallet, String? transDocId, String? payment_Id, amplify_core.TemporalDateTime? paymentTime, String? lastCommand, String? gstin, String? gstName, String? userName, String? userContact, bool? overchargeDueCleared, int? invoiceNo, double? dueAmount, String? stationName, String? cpoName, String? cpoId, double? taxPercent, bool? isIgst, double? igstAmount, double? sgstAmount, double? cgstAmount, double? baseAmount, String? invoiceId, String? rfid, String? pgTransRef}) {
    return ChargingTable._internal(
      id: id,
      booking_id: booking_id ?? this.booking_id,
      start_time: start_time ?? this.start_time,
      end_time: end_time ?? this.end_time,
      status: status ?? this.status,
      connector_no: connector_no ?? this.connector_no,
      CurrentMeterWatt: CurrentMeterWatt ?? this.CurrentMeterWatt,
      city: city ?? this.city,
      charging_fee: charging_fee ?? this.charging_fee,
      payment_status: payment_status ?? this.payment_status,
      createdAt: createdAt ?? this.createdAt,
      tax_amount: tax_amount ?? this.tax_amount,
      vehical_number: vehical_number ?? this.vehical_number,
      chargePointId: chargePointId ?? this.chargePointId,
      user_id: user_id ?? this.user_id,
      station_id: station_id ?? this.station_id,
      vehicle_id: vehicle_id ?? this.vehicle_id,
      charging_percent: charging_percent ?? this.charging_percent,
      MeterStartWatt: MeterStartWatt ?? this.MeterStartWatt,
      MeterEndWatt: MeterEndWatt ?? this.MeterEndWatt,
      booking_type: booking_type ?? this.booking_type,
      compareValue: compareValue ?? this.compareValue,
      pricePerKw: pricePerKw ?? this.pricePerKw,
      geoState: geoState ?? this.geoState,
      isPaid: isPaid ?? this.isPaid,
      chargerId: chargerId ?? this.chargerId,
      transactionId: transactionId ?? this.transactionId,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      estimatedUnits: estimatedUnits ?? this.estimatedUnits,
      startedAtPercent: startedAtPercent ?? this.startedAtPercent,
      stopedAtPercent: stopedAtPercent ?? this.stopedAtPercent,
      unitsBurned: unitsBurned ?? this.unitsBurned,
      costOfConsump: costOfConsump ?? this.costOfConsump,
      refundedAmount: refundedAmount ?? this.refundedAmount,
      startedAtTime: startedAtTime ?? this.startedAtTime,
      stopedAtTime: stopedAtTime ?? this.stopedAtTime,
      amountFromWallet: amountFromWallet ?? this.amountFromWallet,
      transDocId: transDocId ?? this.transDocId,
      payment_Id: payment_Id ?? this.payment_Id,
      paymentTime: paymentTime ?? this.paymentTime,
      lastCommand: lastCommand ?? this.lastCommand,
      gstin: gstin ?? this.gstin,
      gstName: gstName ?? this.gstName,
      userName: userName ?? this.userName,
      userContact: userContact ?? this.userContact,
      overchargeDueCleared: overchargeDueCleared ?? this.overchargeDueCleared,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      dueAmount: dueAmount ?? this.dueAmount,
      stationName: stationName ?? this.stationName,
      cpoName: cpoName ?? this.cpoName,
      cpoId: cpoId ?? this.cpoId,
      taxPercent: taxPercent ?? this.taxPercent,
      isIgst: isIgst ?? this.isIgst,
      igstAmount: igstAmount ?? this.igstAmount,
      sgstAmount: sgstAmount ?? this.sgstAmount,
      cgstAmount: cgstAmount ?? this.cgstAmount,
      baseAmount: baseAmount ?? this.baseAmount,
      invoiceId: invoiceId ?? this.invoiceId,
      rfid: rfid ?? this.rfid,
      pgTransRef: pgTransRef ?? this.pgTransRef);
  }
  
  ChargingTable copyWithModelFieldValues({
    ModelFieldValue<String?>? booking_id,
    ModelFieldValue<amplify_core.TemporalDateTime?>? start_time,
    ModelFieldValue<amplify_core.TemporalDateTime?>? end_time,
    ModelFieldValue<String?>? status,
    ModelFieldValue<int?>? connector_no,
    ModelFieldValue<int?>? CurrentMeterWatt,
    ModelFieldValue<String?>? city,
    ModelFieldValue<double?>? charging_fee,
    ModelFieldValue<String?>? payment_status,
    ModelFieldValue<amplify_core.TemporalDateTime?>? createdAt,
    ModelFieldValue<double?>? tax_amount,
    ModelFieldValue<String?>? vehical_number,
    ModelFieldValue<String?>? chargePointId,
    ModelFieldValue<String?>? user_id,
    ModelFieldValue<String?>? station_id,
    ModelFieldValue<String?>? vehicle_id,
    ModelFieldValue<String?>? charging_percent,
    ModelFieldValue<int?>? MeterStartWatt,
    ModelFieldValue<int?>? MeterEndWatt,
    ModelFieldValue<String?>? booking_type,
    ModelFieldValue<String?>? compareValue,
    ModelFieldValue<double?>? pricePerKw,
    ModelFieldValue<String?>? geoState,
    ModelFieldValue<bool?>? isPaid,
    ModelFieldValue<String?>? chargerId,
    ModelFieldValue<int?>? transactionId,
    ModelFieldValue<double?>? estimatedDuration,
    ModelFieldValue<double?>? estimatedUnits,
    ModelFieldValue<int?>? startedAtPercent,
    ModelFieldValue<int?>? stopedAtPercent,
    ModelFieldValue<double?>? unitsBurned,
    ModelFieldValue<double?>? costOfConsump,
    ModelFieldValue<double?>? refundedAmount,
    ModelFieldValue<amplify_core.TemporalDateTime?>? startedAtTime,
    ModelFieldValue<amplify_core.TemporalDateTime?>? stopedAtTime,
    ModelFieldValue<double?>? amountFromWallet,
    ModelFieldValue<String?>? transDocId,
    ModelFieldValue<String?>? payment_Id,
    ModelFieldValue<amplify_core.TemporalDateTime?>? paymentTime,
    ModelFieldValue<String?>? lastCommand,
    ModelFieldValue<String?>? gstin,
    ModelFieldValue<String?>? gstName,
    ModelFieldValue<String?>? userName,
    ModelFieldValue<String?>? userContact,
    ModelFieldValue<bool?>? overchargeDueCleared,
    ModelFieldValue<int?>? invoiceNo,
    ModelFieldValue<double?>? dueAmount,
    ModelFieldValue<String?>? stationName,
    ModelFieldValue<String?>? cpoName,
    ModelFieldValue<String?>? cpoId,
    ModelFieldValue<double?>? taxPercent,
    ModelFieldValue<bool?>? isIgst,
    ModelFieldValue<double?>? igstAmount,
    ModelFieldValue<double?>? sgstAmount,
    ModelFieldValue<double?>? cgstAmount,
    ModelFieldValue<double?>? baseAmount,
    ModelFieldValue<String?>? invoiceId,
    ModelFieldValue<String?>? rfid,
    ModelFieldValue<String?>? pgTransRef
  }) {
    return ChargingTable._internal(
      id: id,
      booking_id: booking_id == null ? this.booking_id : booking_id.value,
      start_time: start_time == null ? this.start_time : start_time.value,
      end_time: end_time == null ? this.end_time : end_time.value,
      status: status == null ? this.status : status.value,
      connector_no: connector_no == null ? this.connector_no : connector_no.value,
      CurrentMeterWatt: CurrentMeterWatt == null ? this.CurrentMeterWatt : CurrentMeterWatt.value,
      city: city == null ? this.city : city.value,
      charging_fee: charging_fee == null ? this.charging_fee : charging_fee.value,
      payment_status: payment_status == null ? this.payment_status : payment_status.value,
      createdAt: createdAt == null ? this.createdAt : createdAt.value,
      tax_amount: tax_amount == null ? this.tax_amount : tax_amount.value,
      vehical_number: vehical_number == null ? this.vehical_number : vehical_number.value,
      chargePointId: chargePointId == null ? this.chargePointId : chargePointId.value,
      user_id: user_id == null ? this.user_id : user_id.value,
      station_id: station_id == null ? this.station_id : station_id.value,
      vehicle_id: vehicle_id == null ? this.vehicle_id : vehicle_id.value,
      charging_percent: charging_percent == null ? this.charging_percent : charging_percent.value,
      MeterStartWatt: MeterStartWatt == null ? this.MeterStartWatt : MeterStartWatt.value,
      MeterEndWatt: MeterEndWatt == null ? this.MeterEndWatt : MeterEndWatt.value,
      booking_type: booking_type == null ? this.booking_type : booking_type.value,
      compareValue: compareValue == null ? this.compareValue : compareValue.value,
      pricePerKw: pricePerKw == null ? this.pricePerKw : pricePerKw.value,
      geoState: geoState == null ? this.geoState : geoState.value,
      isPaid: isPaid == null ? this.isPaid : isPaid.value,
      chargerId: chargerId == null ? this.chargerId : chargerId.value,
      transactionId: transactionId == null ? this.transactionId : transactionId.value,
      estimatedDuration: estimatedDuration == null ? this.estimatedDuration : estimatedDuration.value,
      estimatedUnits: estimatedUnits == null ? this.estimatedUnits : estimatedUnits.value,
      startedAtPercent: startedAtPercent == null ? this.startedAtPercent : startedAtPercent.value,
      stopedAtPercent: stopedAtPercent == null ? this.stopedAtPercent : stopedAtPercent.value,
      unitsBurned: unitsBurned == null ? this.unitsBurned : unitsBurned.value,
      costOfConsump: costOfConsump == null ? this.costOfConsump : costOfConsump.value,
      refundedAmount: refundedAmount == null ? this.refundedAmount : refundedAmount.value,
      startedAtTime: startedAtTime == null ? this.startedAtTime : startedAtTime.value,
      stopedAtTime: stopedAtTime == null ? this.stopedAtTime : stopedAtTime.value,
      amountFromWallet: amountFromWallet == null ? this.amountFromWallet : amountFromWallet.value,
      transDocId: transDocId == null ? this.transDocId : transDocId.value,
      payment_Id: payment_Id == null ? this.payment_Id : payment_Id.value,
      paymentTime: paymentTime == null ? this.paymentTime : paymentTime.value,
      lastCommand: lastCommand == null ? this.lastCommand : lastCommand.value,
      gstin: gstin == null ? this.gstin : gstin.value,
      gstName: gstName == null ? this.gstName : gstName.value,
      userName: userName == null ? this.userName : userName.value,
      userContact: userContact == null ? this.userContact : userContact.value,
      overchargeDueCleared: overchargeDueCleared == null ? this.overchargeDueCleared : overchargeDueCleared.value,
      invoiceNo: invoiceNo == null ? this.invoiceNo : invoiceNo.value,
      dueAmount: dueAmount == null ? this.dueAmount : dueAmount.value,
      stationName: stationName == null ? this.stationName : stationName.value,
      cpoName: cpoName == null ? this.cpoName : cpoName.value,
      cpoId: cpoId == null ? this.cpoId : cpoId.value,
      taxPercent: taxPercent == null ? this.taxPercent : taxPercent.value,
      isIgst: isIgst == null ? this.isIgst : isIgst.value,
      igstAmount: igstAmount == null ? this.igstAmount : igstAmount.value,
      sgstAmount: sgstAmount == null ? this.sgstAmount : sgstAmount.value,
      cgstAmount: cgstAmount == null ? this.cgstAmount : cgstAmount.value,
      baseAmount: baseAmount == null ? this.baseAmount : baseAmount.value,
      invoiceId: invoiceId == null ? this.invoiceId : invoiceId.value,
      rfid: rfid == null ? this.rfid : rfid.value,
      pgTransRef: pgTransRef == null ? this.pgTransRef : pgTransRef.value
    );
  }
  
  ChargingTable.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _booking_id = json['booking_id'],
      _start_time = json['start_time'] != null ? amplify_core.TemporalDateTime.fromString(json['start_time']) : null,
      _end_time = json['end_time'] != null ? amplify_core.TemporalDateTime.fromString(json['end_time']) : null,
      _status = json['status'],
      _connector_no = (json['connector_no'] as num?)?.toInt(),
      _CurrentMeterWatt = (json['CurrentMeterWatt'] as num?)?.toInt(),
      _city = json['city'],
      _charging_fee = (json['charging_fee'] as num?)?.toDouble(),
      _payment_status = json['payment_status'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _tax_amount = (json['tax_amount'] as num?)?.toDouble(),
      _vehical_number = json['vehical_number'],
      _chargePointId = json['chargePointId'],
      _user_id = json['user_id'],
      _station_id = json['station_id'],
      _vehicle_id = json['vehicle_id'],
      _charging_percent = json['charging_percent'],
      _MeterStartWatt = (json['MeterStartWatt'] as num?)?.toInt(),
      _MeterEndWatt = (json['MeterEndWatt'] as num?)?.toInt(),
      _booking_type = json['booking_type'],
      _compareValue = json['compareValue'],
      _pricePerKw = (json['pricePerKw'] as num?)?.toDouble(),
      _geoState = json['geoState'],
      _isPaid = json['isPaid'],
      _chargerId = json['chargerId'],
      _transactionId = (json['transactionId'] as num?)?.toInt(),
      _estimatedDuration = (json['estimatedDuration'] as num?)?.toDouble(),
      _estimatedUnits = (json['estimatedUnits'] as num?)?.toDouble(),
      _startedAtPercent = (json['startedAtPercent'] as num?)?.toInt(),
      _stopedAtPercent = (json['stopedAtPercent'] as num?)?.toInt(),
      _unitsBurned = (json['unitsBurned'] as num?)?.toDouble(),
      _costOfConsump = (json['costOfConsump'] as num?)?.toDouble(),
      _refundedAmount = (json['refundedAmount'] as num?)?.toDouble(),
      _startedAtTime = json['startedAtTime'] != null ? amplify_core.TemporalDateTime.fromString(json['startedAtTime']) : null,
      _stopedAtTime = json['stopedAtTime'] != null ? amplify_core.TemporalDateTime.fromString(json['stopedAtTime']) : null,
      _amountFromWallet = (json['amountFromWallet'] as num?)?.toDouble(),
      _transDocId = json['transDocId'],
      _payment_Id = json['payment_Id'],
      _paymentTime = json['paymentTime'] != null ? amplify_core.TemporalDateTime.fromString(json['paymentTime']) : null,
      _lastCommand = json['lastCommand'],
      _gstin = json['gstin'],
      _gstName = json['gstName'],
      _userName = json['userName'],
      _userContact = json['userContact'],
      _overchargeDueCleared = json['overchargeDueCleared'],
      _invoiceNo = (json['invoiceNo'] as num?)?.toInt(),
      _dueAmount = (json['dueAmount'] as num?)?.toDouble(),
      _stationName = json['stationName'],
      _cpoName = json['cpoName'],
      _cpoId = json['cpoId'],
      _taxPercent = (json['taxPercent'] as num?)?.toDouble(),
      _isIgst = json['isIgst'],
      _igstAmount = (json['igstAmount'] as num?)?.toDouble(),
      _sgstAmount = (json['sgstAmount'] as num?)?.toDouble(),
      _cgstAmount = (json['cgstAmount'] as num?)?.toDouble(),
      _baseAmount = (json['baseAmount'] as num?)?.toDouble(),
      _invoiceId = json['invoiceId'],
      _rfid = json['rfid'],
      _pgTransRef = json['pgTransRef'],
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'booking_id': _booking_id, 'start_time': _start_time?.format(), 'end_time': _end_time?.format(), 'status': _status, 'connector_no': _connector_no, 'CurrentMeterWatt': _CurrentMeterWatt, 'city': _city, 'charging_fee': _charging_fee, 'payment_status': _payment_status, 'createdAt': _createdAt?.format(), 'tax_amount': _tax_amount, 'vehical_number': _vehical_number, 'chargePointId': _chargePointId, 'user_id': _user_id, 'station_id': _station_id, 'vehicle_id': _vehicle_id, 'charging_percent': _charging_percent, 'MeterStartWatt': _MeterStartWatt, 'MeterEndWatt': _MeterEndWatt, 'booking_type': _booking_type, 'compareValue': _compareValue, 'pricePerKw': _pricePerKw, 'geoState': _geoState, 'isPaid': _isPaid, 'chargerId': _chargerId, 'transactionId': _transactionId, 'estimatedDuration': _estimatedDuration, 'estimatedUnits': _estimatedUnits, 'startedAtPercent': _startedAtPercent, 'stopedAtPercent': _stopedAtPercent, 'unitsBurned': _unitsBurned, 'costOfConsump': _costOfConsump, 'refundedAmount': _refundedAmount, 'startedAtTime': _startedAtTime?.format(), 'stopedAtTime': _stopedAtTime?.format(), 'amountFromWallet': _amountFromWallet, 'transDocId': _transDocId, 'payment_Id': _payment_Id, 'paymentTime': _paymentTime?.format(), 'lastCommand': _lastCommand, 'gstin': _gstin, 'gstName': _gstName, 'userName': _userName, 'userContact': _userContact, 'overchargeDueCleared': _overchargeDueCleared, 'invoiceNo': _invoiceNo, 'dueAmount': _dueAmount, 'stationName': _stationName, 'cpoName': _cpoName, 'cpoId': _cpoId, 'taxPercent': _taxPercent, 'isIgst': _isIgst, 'igstAmount': _igstAmount, 'sgstAmount': _sgstAmount, 'cgstAmount': _cgstAmount, 'baseAmount': _baseAmount, 'invoiceId': _invoiceId, 'rfid': _rfid, 'pgTransRef': _pgTransRef, 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'booking_id': _booking_id,
    'start_time': _start_time,
    'end_time': _end_time,
    'status': _status,
    'connector_no': _connector_no,
    'CurrentMeterWatt': _CurrentMeterWatt,
    'city': _city,
    'charging_fee': _charging_fee,
    'payment_status': _payment_status,
    'createdAt': _createdAt,
    'tax_amount': _tax_amount,
    'vehical_number': _vehical_number,
    'chargePointId': _chargePointId,
    'user_id': _user_id,
    'station_id': _station_id,
    'vehicle_id': _vehicle_id,
    'charging_percent': _charging_percent,
    'MeterStartWatt': _MeterStartWatt,
    'MeterEndWatt': _MeterEndWatt,
    'booking_type': _booking_type,
    'compareValue': _compareValue,
    'pricePerKw': _pricePerKw,
    'geoState': _geoState,
    'isPaid': _isPaid,
    'chargerId': _chargerId,
    'transactionId': _transactionId,
    'estimatedDuration': _estimatedDuration,
    'estimatedUnits': _estimatedUnits,
    'startedAtPercent': _startedAtPercent,
    'stopedAtPercent': _stopedAtPercent,
    'unitsBurned': _unitsBurned,
    'costOfConsump': _costOfConsump,
    'refundedAmount': _refundedAmount,
    'startedAtTime': _startedAtTime,
    'stopedAtTime': _stopedAtTime,
    'amountFromWallet': _amountFromWallet,
    'transDocId': _transDocId,
    'payment_Id': _payment_Id,
    'paymentTime': _paymentTime,
    'lastCommand': _lastCommand,
    'gstin': _gstin,
    'gstName': _gstName,
    'userName': _userName,
    'userContact': _userContact,
    'overchargeDueCleared': _overchargeDueCleared,
    'invoiceNo': _invoiceNo,
    'dueAmount': _dueAmount,
    'stationName': _stationName,
    'cpoName': _cpoName,
    'cpoId': _cpoId,
    'taxPercent': _taxPercent,
    'isIgst': _isIgst,
    'igstAmount': _igstAmount,
    'sgstAmount': _sgstAmount,
    'cgstAmount': _cgstAmount,
    'baseAmount': _baseAmount,
    'invoiceId': _invoiceId,
    'rfid': _rfid,
    'pgTransRef': _pgTransRef,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ChargingTableModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ChargingTableModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final BOOKING_ID = amplify_core.QueryField(fieldName: "booking_id");
  static final START_TIME = amplify_core.QueryField(fieldName: "start_time");
  static final END_TIME = amplify_core.QueryField(fieldName: "end_time");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final CONNECTOR_NO = amplify_core.QueryField(fieldName: "connector_no");
  static final CURRENTMETERWATT = amplify_core.QueryField(fieldName: "CurrentMeterWatt");
  static final CITY = amplify_core.QueryField(fieldName: "city");
  static final CHARGING_FEE = amplify_core.QueryField(fieldName: "charging_fee");
  static final PAYMENT_STATUS = amplify_core.QueryField(fieldName: "payment_status");
  static final CREATEDAT = amplify_core.QueryField(fieldName: "createdAt");
  static final TAX_AMOUNT = amplify_core.QueryField(fieldName: "tax_amount");
  static final VEHICAL_NUMBER = amplify_core.QueryField(fieldName: "vehical_number");
  static final CHARGEPOINTID = amplify_core.QueryField(fieldName: "chargePointId");
  static final USER_ID = amplify_core.QueryField(fieldName: "user_id");
  static final STATION_ID = amplify_core.QueryField(fieldName: "station_id");
  static final VEHICLE_ID = amplify_core.QueryField(fieldName: "vehicle_id");
  static final CHARGING_PERCENT = amplify_core.QueryField(fieldName: "charging_percent");
  static final METERSTARTWATT = amplify_core.QueryField(fieldName: "MeterStartWatt");
  static final METERENDWATT = amplify_core.QueryField(fieldName: "MeterEndWatt");
  static final BOOKING_TYPE = amplify_core.QueryField(fieldName: "booking_type");
  static final COMPAREVALUE = amplify_core.QueryField(fieldName: "compareValue");
  static final PRICEPERKW = amplify_core.QueryField(fieldName: "pricePerKw");
  static final GEOSTATE = amplify_core.QueryField(fieldName: "geoState");
  static final ISPAID = amplify_core.QueryField(fieldName: "isPaid");
  static final CHARGERID = amplify_core.QueryField(fieldName: "chargerId");
  static final TRANSACTIONID = amplify_core.QueryField(fieldName: "transactionId");
  static final ESTIMATEDDURATION = amplify_core.QueryField(fieldName: "estimatedDuration");
  static final ESTIMATEDUNITS = amplify_core.QueryField(fieldName: "estimatedUnits");
  static final STARTEDATPERCENT = amplify_core.QueryField(fieldName: "startedAtPercent");
  static final STOPEDATPERCENT = amplify_core.QueryField(fieldName: "stopedAtPercent");
  static final UNITSBURNED = amplify_core.QueryField(fieldName: "unitsBurned");
  static final COSTOFCONSUMP = amplify_core.QueryField(fieldName: "costOfConsump");
  static final REFUNDEDAMOUNT = amplify_core.QueryField(fieldName: "refundedAmount");
  static final STARTEDATTIME = amplify_core.QueryField(fieldName: "startedAtTime");
  static final STOPEDATTIME = amplify_core.QueryField(fieldName: "stopedAtTime");
  static final AMOUNTFROMWALLET = amplify_core.QueryField(fieldName: "amountFromWallet");
  static final TRANSDOCID = amplify_core.QueryField(fieldName: "transDocId");
  static final PAYMENT_ID = amplify_core.QueryField(fieldName: "payment_Id");
  static final PAYMENTTIME = amplify_core.QueryField(fieldName: "paymentTime");
  static final LASTCOMMAND = amplify_core.QueryField(fieldName: "lastCommand");
  static final GSTIN = amplify_core.QueryField(fieldName: "gstin");
  static final GSTNAME = amplify_core.QueryField(fieldName: "gstName");
  static final USERNAME = amplify_core.QueryField(fieldName: "userName");
  static final USERCONTACT = amplify_core.QueryField(fieldName: "userContact");
  static final OVERCHARGEDUECLEARED = amplify_core.QueryField(fieldName: "overchargeDueCleared");
  static final INVOICENO = amplify_core.QueryField(fieldName: "invoiceNo");
  static final DUEAMOUNT = amplify_core.QueryField(fieldName: "dueAmount");
  static final STATIONNAME = amplify_core.QueryField(fieldName: "stationName");
  static final CPONAME = amplify_core.QueryField(fieldName: "cpoName");
  static final CPOID = amplify_core.QueryField(fieldName: "cpoId");
  static final TAXPERCENT = amplify_core.QueryField(fieldName: "taxPercent");
  static final ISIGST = amplify_core.QueryField(fieldName: "isIgst");
  static final IGSTAMOUNT = amplify_core.QueryField(fieldName: "igstAmount");
  static final SGSTAMOUNT = amplify_core.QueryField(fieldName: "sgstAmount");
  static final CGSTAMOUNT = amplify_core.QueryField(fieldName: "cgstAmount");
  static final BASEAMOUNT = amplify_core.QueryField(fieldName: "baseAmount");
  static final INVOICEID = amplify_core.QueryField(fieldName: "invoiceId");
  static final RFID = amplify_core.QueryField(fieldName: "rfid");
  static final PGTRANSREF = amplify_core.QueryField(fieldName: "pgTransRef");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "ChargingTable";
    modelSchemaDefinition.pluralName = "ChargingTables";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.BOOKING_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.START_TIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.END_TIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CONNECTOR_NO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CURRENTMETERWATT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CITY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CHARGING_FEE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.PAYMENT_STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CREATEDAT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.TAX_AMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.VEHICAL_NUMBER,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CHARGEPOINTID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.USER_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STATION_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.VEHICLE_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CHARGING_PERCENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.METERSTARTWATT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.METERENDWATT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.BOOKING_TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.COMPAREVALUE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.PRICEPERKW,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.GEOSTATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.ISPAID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CHARGERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.TRANSACTIONID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.ESTIMATEDDURATION,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.ESTIMATEDUNITS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STARTEDATPERCENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STOPEDATPERCENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.UNITSBURNED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.COSTOFCONSUMP,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.REFUNDEDAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STARTEDATTIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STOPEDATTIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.AMOUNTFROMWALLET,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.TRANSDOCID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.PAYMENT_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.PAYMENTTIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.LASTCOMMAND,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.GSTIN,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.GSTNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.USERNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.USERCONTACT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.OVERCHARGEDUECLEARED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.INVOICENO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.DUEAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.STATIONNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CPONAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CPOID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.TAXPERCENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.ISIGST,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.IGSTAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.SGSTAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.CGSTAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.BASEAMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.INVOICEID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.RFID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargingTable.PGTRANSREF,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ChargingTableModelType extends amplify_core.ModelType<ChargingTable> {
  const _ChargingTableModelType();
  
  @override
  ChargingTable fromJson(Map<String, dynamic> jsonData) {
    return ChargingTable.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'ChargingTable';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [ChargingTable] in your schema.
 */
class ChargingTableModelIdentifier implements amplify_core.ModelIdentifier<ChargingTable> {
  final String id;

  /** Create an instance of ChargingTableModelIdentifier using [id] the primary key. */
  const ChargingTableModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ChargingTableModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ChargingTableModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}