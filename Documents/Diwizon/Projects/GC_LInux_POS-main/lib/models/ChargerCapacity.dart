/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the ChargerCapacity type in your schema. */
class ChargerCapacity extends amplify_core.Model {
  static const classType = const _ChargerCapacityModelType();
  final String id;
  final double? _charger_capacity;
  final bool? _perKW;
  final bool? _isDC;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ChargerCapacityModelIdentifier get modelIdentifier {
      return ChargerCapacityModelIdentifier(
        id: id
      );
  }
  
  double get charger_capacity {
    try {
      return _charger_capacity!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool get perKW {
    try {
      return _perKW!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool get isDC {
    try {
      return _isDC!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const ChargerCapacity._internal({required this.id, required charger_capacity, required perKW, required isDC, createdAt, updatedAt}): _charger_capacity = charger_capacity, _perKW = perKW, _isDC = isDC, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory ChargerCapacity({String? id, required double charger_capacity, required bool perKW, required bool isDC}) {
    return ChargerCapacity._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      charger_capacity: charger_capacity,
      perKW: perKW,
      isDC: isDC);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ChargerCapacity &&
      id == other.id &&
      _charger_capacity == other._charger_capacity &&
      _perKW == other._perKW &&
      _isDC == other._isDC;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("ChargerCapacity {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("charger_capacity=" + (_charger_capacity != null ? _charger_capacity!.toString() : "null") + ", ");
    buffer.write("perKW=" + (_perKW != null ? _perKW!.toString() : "null") + ", ");
    buffer.write("isDC=" + (_isDC != null ? _isDC!.toString() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  ChargerCapacity copyWith({double? charger_capacity, bool? perKW, bool? isDC}) {
    return ChargerCapacity._internal(
      id: id,
      charger_capacity: charger_capacity ?? this.charger_capacity,
      perKW: perKW ?? this.perKW,
      isDC: isDC ?? this.isDC);
  }
  
  ChargerCapacity copyWithModelFieldValues({
    ModelFieldValue<double>? charger_capacity,
    ModelFieldValue<bool>? perKW,
    ModelFieldValue<bool>? isDC
  }) {
    return ChargerCapacity._internal(
      id: id,
      charger_capacity: charger_capacity == null ? this.charger_capacity : charger_capacity.value,
      perKW: perKW == null ? this.perKW : perKW.value,
      isDC: isDC == null ? this.isDC : isDC.value
    );
  }
  
  ChargerCapacity.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _charger_capacity = (json['charger_capacity'] as num?)?.toDouble(),
      _perKW = json['perKW'],
      _isDC = json['isDC'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'charger_capacity': _charger_capacity, 'perKW': _perKW, 'isDC': _isDC, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'charger_capacity': _charger_capacity,
    'perKW': _perKW,
    'isDC': _isDC,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ChargerCapacityModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ChargerCapacityModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CHARGER_CAPACITY = amplify_core.QueryField(fieldName: "charger_capacity");
  static final PERKW = amplify_core.QueryField(fieldName: "perKW");
  static final ISDC = amplify_core.QueryField(fieldName: "isDC");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "ChargerCapacity";
    modelSchemaDefinition.pluralName = "ChargerCapacities";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargerCapacity.CHARGER_CAPACITY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargerCapacity.PERKW,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargerCapacity.ISDC,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ChargerCapacityModelType extends amplify_core.ModelType<ChargerCapacity> {
  const _ChargerCapacityModelType();
  
  @override
  ChargerCapacity fromJson(Map<String, dynamic> jsonData) {
    return ChargerCapacity.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'ChargerCapacity';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [ChargerCapacity] in your schema.
 */
class ChargerCapacityModelIdentifier implements amplify_core.ModelIdentifier<ChargerCapacity> {
  final String id;

  /** Create an instance of ChargerCapacityModelIdentifier using [id] the primary key. */
  const ChargerCapacityModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ChargerCapacityModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ChargerCapacityModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}