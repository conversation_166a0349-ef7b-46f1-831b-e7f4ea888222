/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;
import 'package:collection/collection.dart';


/** This is an auto generated class representing the LocalList type in your schema. */
class LocalList extends amplify_core.Model {
  static const classType = const _LocalListModelType();
  final String id;
  final String? _chargerId;
  final String? _chargingPointId;
  final String? _listVersion;
  final amplify_core.TemporalDateTime? _updatedTimeStamp;
  final List<String>? _list;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  LocalListModelIdentifier get modelIdentifier {
      return LocalListModelIdentifier(
        id: id
      );
  }
  
  String? get chargerId {
    return _chargerId;
  }
  
  String? get chargingPointId {
    return _chargingPointId;
  }
  
  String? get listVersion {
    return _listVersion;
  }
  
  amplify_core.TemporalDateTime? get updatedTimeStamp {
    return _updatedTimeStamp;
  }
  
  List<String>? get list {
    return _list;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const LocalList._internal({required this.id, chargerId, chargingPointId, listVersion, updatedTimeStamp, list, createdAt, updatedAt}): _chargerId = chargerId, _chargingPointId = chargingPointId, _listVersion = listVersion, _updatedTimeStamp = updatedTimeStamp, _list = list, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory LocalList({String? id, String? chargerId, String? chargingPointId, String? listVersion, amplify_core.TemporalDateTime? updatedTimeStamp, List<String>? list}) {
    return LocalList._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      chargerId: chargerId,
      chargingPointId: chargingPointId,
      listVersion: listVersion,
      updatedTimeStamp: updatedTimeStamp,
      list: list != null ? List<String>.unmodifiable(list) : list);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is LocalList &&
      id == other.id &&
      _chargerId == other._chargerId &&
      _chargingPointId == other._chargingPointId &&
      _listVersion == other._listVersion &&
      _updatedTimeStamp == other._updatedTimeStamp &&
      DeepCollectionEquality().equals(_list, other._list);
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("LocalList {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("chargerId=" + "$_chargerId" + ", ");
    buffer.write("chargingPointId=" + "$_chargingPointId" + ", ");
    buffer.write("listVersion=" + "$_listVersion" + ", ");
    buffer.write("updatedTimeStamp=" + (_updatedTimeStamp != null ? _updatedTimeStamp!.format() : "null") + ", ");
    buffer.write("list=" + (_list != null ? _list!.toString() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  LocalList copyWith({String? chargerId, String? chargingPointId, String? listVersion, amplify_core.TemporalDateTime? updatedTimeStamp, List<String>? list}) {
    return LocalList._internal(
      id: id,
      chargerId: chargerId ?? this.chargerId,
      chargingPointId: chargingPointId ?? this.chargingPointId,
      listVersion: listVersion ?? this.listVersion,
      updatedTimeStamp: updatedTimeStamp ?? this.updatedTimeStamp,
      list: list ?? this.list);
  }
  
  LocalList copyWithModelFieldValues({
    ModelFieldValue<String?>? chargerId,
    ModelFieldValue<String?>? chargingPointId,
    ModelFieldValue<String?>? listVersion,
    ModelFieldValue<amplify_core.TemporalDateTime?>? updatedTimeStamp,
    ModelFieldValue<List<String>?>? list
  }) {
    return LocalList._internal(
      id: id,
      chargerId: chargerId == null ? this.chargerId : chargerId.value,
      chargingPointId: chargingPointId == null ? this.chargingPointId : chargingPointId.value,
      listVersion: listVersion == null ? this.listVersion : listVersion.value,
      updatedTimeStamp: updatedTimeStamp == null ? this.updatedTimeStamp : updatedTimeStamp.value,
      list: list == null ? this.list : list.value
    );
  }
  
  LocalList.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _chargerId = json['chargerId'],
      _chargingPointId = json['chargingPointId'],
      _listVersion = json['listVersion'],
      _updatedTimeStamp = json['updatedTimeStamp'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedTimeStamp']) : null,
      _list = json['list']?.cast<String>(),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'chargerId': _chargerId, 'chargingPointId': _chargingPointId, 'listVersion': _listVersion, 'updatedTimeStamp': _updatedTimeStamp?.format(), 'list': _list, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'chargerId': _chargerId,
    'chargingPointId': _chargingPointId,
    'listVersion': _listVersion,
    'updatedTimeStamp': _updatedTimeStamp,
    'list': _list,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<LocalListModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<LocalListModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CHARGERID = amplify_core.QueryField(fieldName: "chargerId");
  static final CHARGINGPOINTID = amplify_core.QueryField(fieldName: "chargingPointId");
  static final LISTVERSION = amplify_core.QueryField(fieldName: "listVersion");
  static final UPDATEDTIMESTAMP = amplify_core.QueryField(fieldName: "updatedTimeStamp");
  static final LIST = amplify_core.QueryField(fieldName: "list");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "LocalList";
    modelSchemaDefinition.pluralName = "LocalLists";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: LocalList.CHARGERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: LocalList.CHARGINGPOINTID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: LocalList.LISTVERSION,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: LocalList.UPDATEDTIMESTAMP,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: LocalList.LIST,
      isRequired: false,
      isArray: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.collection, ofModelName: amplify_core.ModelFieldTypeEnum.string.name)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _LocalListModelType extends amplify_core.ModelType<LocalList> {
  const _LocalListModelType();
  
  @override
  LocalList fromJson(Map<String, dynamic> jsonData) {
    return LocalList.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'LocalList';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [LocalList] in your schema.
 */
class LocalListModelIdentifier implements amplify_core.ModelIdentifier<LocalList> {
  final String id;

  /** Create an instance of LocalListModelIdentifier using [id] the primary key. */
  const LocalListModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'LocalListModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is LocalListModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}