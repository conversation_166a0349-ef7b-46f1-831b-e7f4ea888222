/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the InLog type in your schema. */
class InLog extends amplify_core.Model {
  static const classType = const _InLogModelType();
  final String id;
  final String? _data;
  final amplify_core.TemporalDate? _date;
  final String? _charger_id;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  InLogModelIdentifier get modelIdentifier {
      return InLogModelIdentifier(
        id: id
      );
  }
  
  String? get data {
    return _data;
  }
  
  amplify_core.TemporalDate? get date {
    return _date;
  }
  
  String? get charger_id {
    return _charger_id;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const InLog._internal({required this.id, data, date, charger_id, createdAt, updatedAt}): _data = data, _date = date, _charger_id = charger_id, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory InLog({String? id, String? data, amplify_core.TemporalDate? date, String? charger_id}) {
    return InLog._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      data: data,
      date: date,
      charger_id: charger_id);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is InLog &&
      id == other.id &&
      _data == other._data &&
      _date == other._date &&
      _charger_id == other._charger_id;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("InLog {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("data=" + "$_data" + ", ");
    buffer.write("date=" + (_date != null ? _date!.format() : "null") + ", ");
    buffer.write("charger_id=" + "$_charger_id" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  InLog copyWith({String? data, amplify_core.TemporalDate? date, String? charger_id}) {
    return InLog._internal(
      id: id,
      data: data ?? this.data,
      date: date ?? this.date,
      charger_id: charger_id ?? this.charger_id);
  }
  
  InLog copyWithModelFieldValues({
    ModelFieldValue<String?>? data,
    ModelFieldValue<amplify_core.TemporalDate?>? date,
    ModelFieldValue<String?>? charger_id
  }) {
    return InLog._internal(
      id: id,
      data: data == null ? this.data : data.value,
      date: date == null ? this.date : date.value,
      charger_id: charger_id == null ? this.charger_id : charger_id.value
    );
  }
  
  InLog.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _data = json['data'],
      _date = json['date'] != null ? amplify_core.TemporalDate.fromString(json['date']) : null,
      _charger_id = json['charger_id'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'data': _data, 'date': _date?.format(), 'charger_id': _charger_id, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'data': _data,
    'date': _date,
    'charger_id': _charger_id,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<InLogModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<InLogModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final DATA = amplify_core.QueryField(fieldName: "data");
  static final DATE = amplify_core.QueryField(fieldName: "date");
  static final CHARGER_ID = amplify_core.QueryField(fieldName: "charger_id");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "InLog";
    modelSchemaDefinition.pluralName = "InLogs";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: InLog.DATA,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: InLog.DATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.date)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: InLog.CHARGER_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _InLogModelType extends amplify_core.ModelType<InLog> {
  const _InLogModelType();
  
  @override
  InLog fromJson(Map<String, dynamic> jsonData) {
    return InLog.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'InLog';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [InLog] in your schema.
 */
class InLogModelIdentifier implements amplify_core.ModelIdentifier<InLog> {
  final String id;

  /** Create an instance of InLogModelIdentifier using [id] the primary key. */
  const InLogModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'InLogModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is InLogModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}