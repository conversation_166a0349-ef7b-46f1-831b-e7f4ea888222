/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Station type in your schema. */
class Station extends amplify_core.Model {
  static const classType = const _StationModelType();
  final String id;
  final String? _station_name;
  final String? _latitude;
  final String? _longitude;
  final String? _landmark;
  final String? _pincode;
  final String? _contact_no;
  final String? _email;
  final String? _geoState;
  final String? _city;
  final bool? _active;
  final String? _cpoId;
  final String? _address;
  final bool? _igst;
  final String? _status;
  final amplify_core.TemporalDateTime? _last_heart_beat;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  StationModelIdentifier get modelIdentifier {
      return StationModelIdentifier(
        id: id
      );
  }
  
  String get station_name {
    try {
      return _station_name!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get latitude {
    try {
      return _latitude!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get longitude {
    return _longitude;
  }
  
  String? get landmark {
    return _landmark;
  }
  
  String? get pincode {
    return _pincode;
  }
  
  String? get contact_no {
    return _contact_no;
  }
  
  String? get email {
    return _email;
  }
  
  String get geoState {
    try {
      return _geoState!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get city {
    try {
      return _city!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool? get active {
    return _active;
  }
  
  String? get cpoId {
    return _cpoId;
  }
  
  String? get address {
    return _address;
  }
  
  bool? get igst {
    return _igst;
  }
  
  String? get status {
    return _status;
  }
  
  amplify_core.TemporalDateTime? get last_heart_beat {
    return _last_heart_beat;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Station._internal({required this.id, required station_name, required latitude, longitude, landmark, pincode, contact_no, email, required geoState, required city, active, cpoId, address, igst, status, last_heart_beat, createdAt, updatedAt}): _station_name = station_name, _latitude = latitude, _longitude = longitude, _landmark = landmark, _pincode = pincode, _contact_no = contact_no, _email = email, _geoState = geoState, _city = city, _active = active, _cpoId = cpoId, _address = address, _igst = igst, _status = status, _last_heart_beat = last_heart_beat, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Station({String? id, required String station_name, required String latitude, String? longitude, String? landmark, String? pincode, String? contact_no, String? email, required String geoState, required String city, bool? active, String? cpoId, String? address, bool? igst, String? status, amplify_core.TemporalDateTime? last_heart_beat}) {
    return Station._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      station_name: station_name,
      latitude: latitude,
      longitude: longitude,
      landmark: landmark,
      pincode: pincode,
      contact_no: contact_no,
      email: email,
      geoState: geoState,
      city: city,
      active: active,
      cpoId: cpoId,
      address: address,
      igst: igst,
      status: status,
      last_heart_beat: last_heart_beat);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Station &&
      id == other.id &&
      _station_name == other._station_name &&
      _latitude == other._latitude &&
      _longitude == other._longitude &&
      _landmark == other._landmark &&
      _pincode == other._pincode &&
      _contact_no == other._contact_no &&
      _email == other._email &&
      _geoState == other._geoState &&
      _city == other._city &&
      _active == other._active &&
      _cpoId == other._cpoId &&
      _address == other._address &&
      _igst == other._igst &&
      _status == other._status &&
      _last_heart_beat == other._last_heart_beat;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Station {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("station_name=" + "$_station_name" + ", ");
    buffer.write("latitude=" + "$_latitude" + ", ");
    buffer.write("longitude=" + "$_longitude" + ", ");
    buffer.write("landmark=" + "$_landmark" + ", ");
    buffer.write("pincode=" + "$_pincode" + ", ");
    buffer.write("contact_no=" + "$_contact_no" + ", ");
    buffer.write("email=" + "$_email" + ", ");
    buffer.write("geoState=" + "$_geoState" + ", ");
    buffer.write("city=" + "$_city" + ", ");
    buffer.write("active=" + (_active != null ? _active!.toString() : "null") + ", ");
    buffer.write("cpoId=" + "$_cpoId" + ", ");
    buffer.write("address=" + "$_address" + ", ");
    buffer.write("igst=" + (_igst != null ? _igst!.toString() : "null") + ", ");
    buffer.write("status=" + "$_status" + ", ");
    buffer.write("last_heart_beat=" + (_last_heart_beat != null ? _last_heart_beat!.format() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Station copyWith({String? station_name, String? latitude, String? longitude, String? landmark, String? pincode, String? contact_no, String? email, String? geoState, String? city, bool? active, String? cpoId, String? address, bool? igst, String? status, amplify_core.TemporalDateTime? last_heart_beat}) {
    return Station._internal(
      id: id,
      station_name: station_name ?? this.station_name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      landmark: landmark ?? this.landmark,
      pincode: pincode ?? this.pincode,
      contact_no: contact_no ?? this.contact_no,
      email: email ?? this.email,
      geoState: geoState ?? this.geoState,
      city: city ?? this.city,
      active: active ?? this.active,
      cpoId: cpoId ?? this.cpoId,
      address: address ?? this.address,
      igst: igst ?? this.igst,
      status: status ?? this.status,
      last_heart_beat: last_heart_beat ?? this.last_heart_beat);
  }
  
  Station copyWithModelFieldValues({
    ModelFieldValue<String>? station_name,
    ModelFieldValue<String>? latitude,
    ModelFieldValue<String?>? longitude,
    ModelFieldValue<String?>? landmark,
    ModelFieldValue<String?>? pincode,
    ModelFieldValue<String?>? contact_no,
    ModelFieldValue<String?>? email,
    ModelFieldValue<String>? geoState,
    ModelFieldValue<String>? city,
    ModelFieldValue<bool?>? active,
    ModelFieldValue<String?>? cpoId,
    ModelFieldValue<String?>? address,
    ModelFieldValue<bool?>? igst,
    ModelFieldValue<String?>? status,
    ModelFieldValue<amplify_core.TemporalDateTime?>? last_heart_beat
  }) {
    return Station._internal(
      id: id,
      station_name: station_name == null ? this.station_name : station_name.value,
      latitude: latitude == null ? this.latitude : latitude.value,
      longitude: longitude == null ? this.longitude : longitude.value,
      landmark: landmark == null ? this.landmark : landmark.value,
      pincode: pincode == null ? this.pincode : pincode.value,
      contact_no: contact_no == null ? this.contact_no : contact_no.value,
      email: email == null ? this.email : email.value,
      geoState: geoState == null ? this.geoState : geoState.value,
      city: city == null ? this.city : city.value,
      active: active == null ? this.active : active.value,
      cpoId: cpoId == null ? this.cpoId : cpoId.value,
      address: address == null ? this.address : address.value,
      igst: igst == null ? this.igst : igst.value,
      status: status == null ? this.status : status.value,
      last_heart_beat: last_heart_beat == null ? this.last_heart_beat : last_heart_beat.value
    );
  }
  
  Station.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _station_name = json['station_name'],
      _latitude = json['latitude'],
      _longitude = json['longitude'],
      _landmark = json['landmark'],
      _pincode = json['pincode'],
      _contact_no = json['contact_no'],
      _email = json['email'],
      _geoState = json['geoState'],
      _city = json['city'],
      _active = json['active'],
      _cpoId = json['cpoId'],
      _address = json['address'],
      _igst = json['igst'],
      _status = json['status'],
      _last_heart_beat = json['last_heart_beat'] != null ? amplify_core.TemporalDateTime.fromString(json['last_heart_beat']) : null,
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'station_name': _station_name, 'latitude': _latitude, 'longitude': _longitude, 'landmark': _landmark, 'pincode': _pincode, 'contact_no': _contact_no, 'email': _email, 'geoState': _geoState, 'city': _city, 'active': _active, 'cpoId': _cpoId, 'address': _address, 'igst': _igst, 'status': _status, 'last_heart_beat': _last_heart_beat?.format(), 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'station_name': _station_name,
    'latitude': _latitude,
    'longitude': _longitude,
    'landmark': _landmark,
    'pincode': _pincode,
    'contact_no': _contact_no,
    'email': _email,
    'geoState': _geoState,
    'city': _city,
    'active': _active,
    'cpoId': _cpoId,
    'address': _address,
    'igst': _igst,
    'status': _status,
    'last_heart_beat': _last_heart_beat,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<StationModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<StationModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final STATION_NAME = amplify_core.QueryField(fieldName: "station_name");
  static final LATITUDE = amplify_core.QueryField(fieldName: "latitude");
  static final LONGITUDE = amplify_core.QueryField(fieldName: "longitude");
  static final LANDMARK = amplify_core.QueryField(fieldName: "landmark");
  static final PINCODE = amplify_core.QueryField(fieldName: "pincode");
  static final CONTACT_NO = amplify_core.QueryField(fieldName: "contact_no");
  static final EMAIL = amplify_core.QueryField(fieldName: "email");
  static final GEOSTATE = amplify_core.QueryField(fieldName: "geoState");
  static final CITY = amplify_core.QueryField(fieldName: "city");
  static final ACTIVE = amplify_core.QueryField(fieldName: "active");
  static final CPOID = amplify_core.QueryField(fieldName: "cpoId");
  static final ADDRESS = amplify_core.QueryField(fieldName: "address");
  static final IGST = amplify_core.QueryField(fieldName: "igst");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final LAST_HEART_BEAT = amplify_core.QueryField(fieldName: "last_heart_beat");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Station";
    modelSchemaDefinition.pluralName = "Stations";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.STATION_NAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.LATITUDE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.LONGITUDE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.LANDMARK,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.PINCODE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.CONTACT_NO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.EMAIL,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.GEOSTATE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.CITY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.ACTIVE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.CPOID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.ADDRESS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.IGST,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Station.LAST_HEART_BEAT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _StationModelType extends amplify_core.ModelType<Station> {
  const _StationModelType();
  
  @override
  Station fromJson(Map<String, dynamic> jsonData) {
    return Station.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Station';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Station] in your schema.
 */
class StationModelIdentifier implements amplify_core.ModelIdentifier<Station> {
  final String id;

  /** Create an instance of StationModelIdentifier using [id] the primary key. */
  const StationModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'StationModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is StationModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}