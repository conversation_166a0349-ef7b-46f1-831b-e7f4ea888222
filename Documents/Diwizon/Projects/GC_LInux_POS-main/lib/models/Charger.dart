/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Charger type in your schema. */
class Charger extends amplify_core.Model {
  static const classType = const _ChargerModelType();
  final String id;
  final String? _chargingPointId;
  final amplify_core.TemporalDateTime? _last_heart_beat;
  final amplify_core.TemporalDate? _service_date;
  final String? _status;
  final double? _pricePerKW;
  final String? _manufactId;
  final String? _capacityId;
  final String? _stationId;
  final String? _stationName;
  final String? _simNo;
  final String? _simCompany;
  final String? _serialNo;
  final bool? _faulted;
  final String? _invoicePrefix;
  final int? _invoiceNo;
  final amplify_core.TemporalDateTime? _currentFinYear;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ChargerModelIdentifier get modelIdentifier {
      return ChargerModelIdentifier(
        id: id
      );
  }
  
  String get chargingPointId {
    try {
      return _chargingPointId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDateTime? get last_heart_beat {
    return _last_heart_beat;
  }
  
  amplify_core.TemporalDate? get service_date {
    return _service_date;
  }
  
  String? get status {
    return _status;
  }
  
  double get pricePerKW {
    try {
      return _pricePerKW!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get manufactId {
    return _manufactId;
  }
  
  String get capacityId {
    try {
      return _capacityId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get stationId {
    try {
      return _stationId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get stationName {
    return _stationName;
  }
  
  String? get simNo {
    return _simNo;
  }
  
  String? get simCompany {
    return _simCompany;
  }
  
  String? get serialNo {
    return _serialNo;
  }
  
  bool? get faulted {
    return _faulted;
  }
  
  String? get invoicePrefix {
    return _invoicePrefix;
  }
  
  int? get invoiceNo {
    return _invoiceNo;
  }
  
  amplify_core.TemporalDateTime? get currentFinYear {
    return _currentFinYear;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Charger._internal({required this.id, required chargingPointId, last_heart_beat, service_date, status, required pricePerKW, manufactId, required capacityId, required stationId, stationName, simNo, simCompany, serialNo, faulted, invoicePrefix, invoiceNo, currentFinYear, createdAt, updatedAt}): _chargingPointId = chargingPointId, _last_heart_beat = last_heart_beat, _service_date = service_date, _status = status, _pricePerKW = pricePerKW, _manufactId = manufactId, _capacityId = capacityId, _stationId = stationId, _stationName = stationName, _simNo = simNo, _simCompany = simCompany, _serialNo = serialNo, _faulted = faulted, _invoicePrefix = invoicePrefix, _invoiceNo = invoiceNo, _currentFinYear = currentFinYear, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Charger({String? id, required String chargingPointId, amplify_core.TemporalDateTime? last_heart_beat, amplify_core.TemporalDate? service_date, String? status, required double pricePerKW, String? manufactId, required String capacityId, required String stationId, String? stationName, String? simNo, String? simCompany, String? serialNo, bool? faulted, String? invoicePrefix, int? invoiceNo, amplify_core.TemporalDateTime? currentFinYear}) {
    return Charger._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      chargingPointId: chargingPointId,
      last_heart_beat: last_heart_beat,
      service_date: service_date,
      status: status,
      pricePerKW: pricePerKW,
      manufactId: manufactId,
      capacityId: capacityId,
      stationId: stationId,
      stationName: stationName,
      simNo: simNo,
      simCompany: simCompany,
      serialNo: serialNo,
      faulted: faulted,
      invoicePrefix: invoicePrefix,
      invoiceNo: invoiceNo,
      currentFinYear: currentFinYear);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Charger &&
      id == other.id &&
      _chargingPointId == other._chargingPointId &&
      _last_heart_beat == other._last_heart_beat &&
      _service_date == other._service_date &&
      _status == other._status &&
      _pricePerKW == other._pricePerKW &&
      _manufactId == other._manufactId &&
      _capacityId == other._capacityId &&
      _stationId == other._stationId &&
      _stationName == other._stationName &&
      _simNo == other._simNo &&
      _simCompany == other._simCompany &&
      _serialNo == other._serialNo &&
      _faulted == other._faulted &&
      _invoicePrefix == other._invoicePrefix &&
      _invoiceNo == other._invoiceNo &&
      _currentFinYear == other._currentFinYear;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Charger {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("chargingPointId=" + "$_chargingPointId" + ", ");
    buffer.write("last_heart_beat=" + (_last_heart_beat != null ? _last_heart_beat!.format() : "null") + ", ");
    buffer.write("service_date=" + (_service_date != null ? _service_date!.format() : "null") + ", ");
    buffer.write("status=" + "$_status" + ", ");
    buffer.write("pricePerKW=" + (_pricePerKW != null ? _pricePerKW!.toString() : "null") + ", ");
    buffer.write("manufactId=" + "$_manufactId" + ", ");
    buffer.write("capacityId=" + "$_capacityId" + ", ");
    buffer.write("stationId=" + "$_stationId" + ", ");
    buffer.write("stationName=" + "$_stationName" + ", ");
    buffer.write("simNo=" + "$_simNo" + ", ");
    buffer.write("simCompany=" + "$_simCompany" + ", ");
    buffer.write("serialNo=" + "$_serialNo" + ", ");
    buffer.write("faulted=" + (_faulted != null ? _faulted!.toString() : "null") + ", ");
    buffer.write("invoicePrefix=" + "$_invoicePrefix" + ", ");
    buffer.write("invoiceNo=" + (_invoiceNo != null ? _invoiceNo!.toString() : "null") + ", ");
    buffer.write("currentFinYear=" + (_currentFinYear != null ? _currentFinYear!.format() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Charger copyWith({String? chargingPointId, amplify_core.TemporalDateTime? last_heart_beat, amplify_core.TemporalDate? service_date, String? status, double? pricePerKW, String? manufactId, String? capacityId, String? stationId, String? stationName, String? simNo, String? simCompany, String? serialNo, bool? faulted, String? invoicePrefix, int? invoiceNo, amplify_core.TemporalDateTime? currentFinYear}) {
    return Charger._internal(
      id: id,
      chargingPointId: chargingPointId ?? this.chargingPointId,
      last_heart_beat: last_heart_beat ?? this.last_heart_beat,
      service_date: service_date ?? this.service_date,
      status: status ?? this.status,
      pricePerKW: pricePerKW ?? this.pricePerKW,
      manufactId: manufactId ?? this.manufactId,
      capacityId: capacityId ?? this.capacityId,
      stationId: stationId ?? this.stationId,
      stationName: stationName ?? this.stationName,
      simNo: simNo ?? this.simNo,
      simCompany: simCompany ?? this.simCompany,
      serialNo: serialNo ?? this.serialNo,
      faulted: faulted ?? this.faulted,
      invoicePrefix: invoicePrefix ?? this.invoicePrefix,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      currentFinYear: currentFinYear ?? this.currentFinYear);
  }
  
  Charger copyWithModelFieldValues({
    ModelFieldValue<String>? chargingPointId,
    ModelFieldValue<amplify_core.TemporalDateTime?>? last_heart_beat,
    ModelFieldValue<amplify_core.TemporalDate?>? service_date,
    ModelFieldValue<String?>? status,
    ModelFieldValue<double>? pricePerKW,
    ModelFieldValue<String?>? manufactId,
    ModelFieldValue<String>? capacityId,
    ModelFieldValue<String>? stationId,
    ModelFieldValue<String?>? stationName,
    ModelFieldValue<String?>? simNo,
    ModelFieldValue<String?>? simCompany,
    ModelFieldValue<String?>? serialNo,
    ModelFieldValue<bool?>? faulted,
    ModelFieldValue<String?>? invoicePrefix,
    ModelFieldValue<int?>? invoiceNo,
    ModelFieldValue<amplify_core.TemporalDateTime?>? currentFinYear
  }) {
    return Charger._internal(
      id: id,
      chargingPointId: chargingPointId == null ? this.chargingPointId : chargingPointId.value,
      last_heart_beat: last_heart_beat == null ? this.last_heart_beat : last_heart_beat.value,
      service_date: service_date == null ? this.service_date : service_date.value,
      status: status == null ? this.status : status.value,
      pricePerKW: pricePerKW == null ? this.pricePerKW : pricePerKW.value,
      manufactId: manufactId == null ? this.manufactId : manufactId.value,
      capacityId: capacityId == null ? this.capacityId : capacityId.value,
      stationId: stationId == null ? this.stationId : stationId.value,
      stationName: stationName == null ? this.stationName : stationName.value,
      simNo: simNo == null ? this.simNo : simNo.value,
      simCompany: simCompany == null ? this.simCompany : simCompany.value,
      serialNo: serialNo == null ? this.serialNo : serialNo.value,
      faulted: faulted == null ? this.faulted : faulted.value,
      invoicePrefix: invoicePrefix == null ? this.invoicePrefix : invoicePrefix.value,
      invoiceNo: invoiceNo == null ? this.invoiceNo : invoiceNo.value,
      currentFinYear: currentFinYear == null ? this.currentFinYear : currentFinYear.value
    );
  }
  
  Charger.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _chargingPointId = json['chargingPointId'],
      _last_heart_beat = json['last_heart_beat'] != null ? amplify_core.TemporalDateTime.fromString(json['last_heart_beat']) : null,
      _service_date = json['service_date'] != null ? amplify_core.TemporalDate.fromString(json['service_date']) : null,
      _status = json['status'],
      _pricePerKW = (json['pricePerKW'] as num?)?.toDouble(),
      _manufactId = json['manufactId'],
      _capacityId = json['capacityId'],
      _stationId = json['stationId'],
      _stationName = json['stationName'],
      _simNo = json['simNo'],
      _simCompany = json['simCompany'],
      _serialNo = json['serialNo'],
      _faulted = json['faulted'],
      _invoicePrefix = json['invoicePrefix'],
      _invoiceNo = (json['invoiceNo'] as num?)?.toInt(),
      _currentFinYear = json['currentFinYear'] != null ? amplify_core.TemporalDateTime.fromString(json['currentFinYear']) : null,
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'chargingPointId': _chargingPointId, 'last_heart_beat': _last_heart_beat?.format(), 'service_date': _service_date?.format(), 'status': _status, 'pricePerKW': _pricePerKW, 'manufactId': _manufactId, 'capacityId': _capacityId, 'stationId': _stationId, 'stationName': _stationName, 'simNo': _simNo, 'simCompany': _simCompany, 'serialNo': _serialNo, 'faulted': _faulted, 'invoicePrefix': _invoicePrefix, 'invoiceNo': _invoiceNo, 'currentFinYear': _currentFinYear?.format(), 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'chargingPointId': _chargingPointId,
    'last_heart_beat': _last_heart_beat,
    'service_date': _service_date,
    'status': _status,
    'pricePerKW': _pricePerKW,
    'manufactId': _manufactId,
    'capacityId': _capacityId,
    'stationId': _stationId,
    'stationName': _stationName,
    'simNo': _simNo,
    'simCompany': _simCompany,
    'serialNo': _serialNo,
    'faulted': _faulted,
    'invoicePrefix': _invoicePrefix,
    'invoiceNo': _invoiceNo,
    'currentFinYear': _currentFinYear,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ChargerModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ChargerModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CHARGINGPOINTID = amplify_core.QueryField(fieldName: "chargingPointId");
  static final LAST_HEART_BEAT = amplify_core.QueryField(fieldName: "last_heart_beat");
  static final SERVICE_DATE = amplify_core.QueryField(fieldName: "service_date");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final PRICEPERKW = amplify_core.QueryField(fieldName: "pricePerKW");
  static final MANUFACTID = amplify_core.QueryField(fieldName: "manufactId");
  static final CAPACITYID = amplify_core.QueryField(fieldName: "capacityId");
  static final STATIONID = amplify_core.QueryField(fieldName: "stationId");
  static final STATIONNAME = amplify_core.QueryField(fieldName: "stationName");
  static final SIMNO = amplify_core.QueryField(fieldName: "simNo");
  static final SIMCOMPANY = amplify_core.QueryField(fieldName: "simCompany");
  static final SERIALNO = amplify_core.QueryField(fieldName: "serialNo");
  static final FAULTED = amplify_core.QueryField(fieldName: "faulted");
  static final INVOICEPREFIX = amplify_core.QueryField(fieldName: "invoicePrefix");
  static final INVOICENO = amplify_core.QueryField(fieldName: "invoiceNo");
  static final CURRENTFINYEAR = amplify_core.QueryField(fieldName: "currentFinYear");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Charger";
    modelSchemaDefinition.pluralName = "Chargers";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.CHARGINGPOINTID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.LAST_HEART_BEAT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.SERVICE_DATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.date)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.PRICEPERKW,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.MANUFACTID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.CAPACITYID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.STATIONID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.STATIONNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.SIMNO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.SIMCOMPANY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.SERIALNO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.FAULTED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.INVOICEPREFIX,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.INVOICENO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Charger.CURRENTFINYEAR,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ChargerModelType extends amplify_core.ModelType<Charger> {
  const _ChargerModelType();
  
  @override
  Charger fromJson(Map<String, dynamic> jsonData) {
    return Charger.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Charger';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Charger] in your schema.
 */
class ChargerModelIdentifier implements amplify_core.ModelIdentifier<Charger> {
  final String id;

  /** Create an instance of ChargerModelIdentifier using [id] the primary key. */
  const ChargerModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ChargerModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ChargerModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}