/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;
import 'package:collection/collection.dart';


/** This is an auto generated class representing the EndUser type in your schema. */
class EndUser extends amplify_core.Model {
  static const classType = const _EndUserModelType();
  final String id;
  final String? _user_fullname;
  final amplify_core.TemporalDate? _dob;
  final amplify_core.TemporalDate? _joining_date;
  final String? _email;
  final String? _contact;
  final double? _balance;
  final String? _default_vehicle_id;
  final List<String>? _favs;
  final String? _uId;
  final String? _deviceId;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  EndUserModelIdentifier get modelIdentifier {
      return EndUserModelIdentifier(
        id: id
      );
  }
  
  String get user_fullname {
    try {
      return _user_fullname!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDate? get dob {
    return _dob;
  }
  
  amplify_core.TemporalDate? get joining_date {
    return _joining_date;
  }
  
  String? get email {
    return _email;
  }
  
  String get contact {
    try {
      return _contact!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  double? get balance {
    return _balance;
  }
  
  String? get default_vehicle_id {
    return _default_vehicle_id;
  }
  
  List<String>? get favs {
    return _favs;
  }
  
  String get uId {
    try {
      return _uId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get deviceId {
    return _deviceId;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const EndUser._internal({required this.id, required user_fullname, dob, joining_date, email, required contact, balance, default_vehicle_id, favs, required uId, deviceId, createdAt, updatedAt}): _user_fullname = user_fullname, _dob = dob, _joining_date = joining_date, _email = email, _contact = contact, _balance = balance, _default_vehicle_id = default_vehicle_id, _favs = favs, _uId = uId, _deviceId = deviceId, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory EndUser({String? id, required String user_fullname, amplify_core.TemporalDate? dob, amplify_core.TemporalDate? joining_date, String? email, required String contact, double? balance, String? default_vehicle_id, List<String>? favs, required String uId, String? deviceId}) {
    return EndUser._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      user_fullname: user_fullname,
      dob: dob,
      joining_date: joining_date,
      email: email,
      contact: contact,
      balance: balance,
      default_vehicle_id: default_vehicle_id,
      favs: favs != null ? List<String>.unmodifiable(favs) : favs,
      uId: uId,
      deviceId: deviceId);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is EndUser &&
      id == other.id &&
      _user_fullname == other._user_fullname &&
      _dob == other._dob &&
      _joining_date == other._joining_date &&
      _email == other._email &&
      _contact == other._contact &&
      _balance == other._balance &&
      _default_vehicle_id == other._default_vehicle_id &&
      DeepCollectionEquality().equals(_favs, other._favs) &&
      _uId == other._uId &&
      _deviceId == other._deviceId;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("EndUser {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("user_fullname=" + "$_user_fullname" + ", ");
    buffer.write("dob=" + (_dob != null ? _dob!.format() : "null") + ", ");
    buffer.write("joining_date=" + (_joining_date != null ? _joining_date!.format() : "null") + ", ");
    buffer.write("email=" + "$_email" + ", ");
    buffer.write("contact=" + "$_contact" + ", ");
    buffer.write("balance=" + (_balance != null ? _balance!.toString() : "null") + ", ");
    buffer.write("default_vehicle_id=" + "$_default_vehicle_id" + ", ");
    buffer.write("favs=" + (_favs != null ? _favs!.toString() : "null") + ", ");
    buffer.write("uId=" + "$_uId" + ", ");
    buffer.write("deviceId=" + "$_deviceId" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  EndUser copyWith({String? user_fullname, amplify_core.TemporalDate? dob, amplify_core.TemporalDate? joining_date, String? email, String? contact, double? balance, String? default_vehicle_id, List<String>? favs, String? uId, String? deviceId}) {
    return EndUser._internal(
      id: id,
      user_fullname: user_fullname ?? this.user_fullname,
      dob: dob ?? this.dob,
      joining_date: joining_date ?? this.joining_date,
      email: email ?? this.email,
      contact: contact ?? this.contact,
      balance: balance ?? this.balance,
      default_vehicle_id: default_vehicle_id ?? this.default_vehicle_id,
      favs: favs ?? this.favs,
      uId: uId ?? this.uId,
      deviceId: deviceId ?? this.deviceId);
  }
  
  EndUser copyWithModelFieldValues({
    ModelFieldValue<String>? user_fullname,
    ModelFieldValue<amplify_core.TemporalDate?>? dob,
    ModelFieldValue<amplify_core.TemporalDate?>? joining_date,
    ModelFieldValue<String?>? email,
    ModelFieldValue<String>? contact,
    ModelFieldValue<double?>? balance,
    ModelFieldValue<String?>? default_vehicle_id,
    ModelFieldValue<List<String>?>? favs,
    ModelFieldValue<String>? uId,
    ModelFieldValue<String?>? deviceId
  }) {
    return EndUser._internal(
      id: id,
      user_fullname: user_fullname == null ? this.user_fullname : user_fullname.value,
      dob: dob == null ? this.dob : dob.value,
      joining_date: joining_date == null ? this.joining_date : joining_date.value,
      email: email == null ? this.email : email.value,
      contact: contact == null ? this.contact : contact.value,
      balance: balance == null ? this.balance : balance.value,
      default_vehicle_id: default_vehicle_id == null ? this.default_vehicle_id : default_vehicle_id.value,
      favs: favs == null ? this.favs : favs.value,
      uId: uId == null ? this.uId : uId.value,
      deviceId: deviceId == null ? this.deviceId : deviceId.value
    );
  }
  
  EndUser.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _user_fullname = json['user_fullname'],
      _dob = json['dob'] != null ? amplify_core.TemporalDate.fromString(json['dob']) : null,
      _joining_date = json['joining_date'] != null ? amplify_core.TemporalDate.fromString(json['joining_date']) : null,
      _email = json['email'],
      _contact = json['contact'],
      _balance = (json['balance'] as num?)?.toDouble(),
      _default_vehicle_id = json['default_vehicle_id'],
      _favs = json['favs']?.cast<String>(),
      _uId = json['uId'],
      _deviceId = json['deviceId'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'user_fullname': _user_fullname, 'dob': _dob?.format(), 'joining_date': _joining_date?.format(), 'email': _email, 'contact': _contact, 'balance': _balance, 'default_vehicle_id': _default_vehicle_id, 'favs': _favs, 'uId': _uId, 'deviceId': _deviceId, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'user_fullname': _user_fullname,
    'dob': _dob,
    'joining_date': _joining_date,
    'email': _email,
    'contact': _contact,
    'balance': _balance,
    'default_vehicle_id': _default_vehicle_id,
    'favs': _favs,
    'uId': _uId,
    'deviceId': _deviceId,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<EndUserModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<EndUserModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final USER_FULLNAME = amplify_core.QueryField(fieldName: "user_fullname");
  static final DOB = amplify_core.QueryField(fieldName: "dob");
  static final JOINING_DATE = amplify_core.QueryField(fieldName: "joining_date");
  static final EMAIL = amplify_core.QueryField(fieldName: "email");
  static final CONTACT = amplify_core.QueryField(fieldName: "contact");
  static final BALANCE = amplify_core.QueryField(fieldName: "balance");
  static final DEFAULT_VEHICLE_ID = amplify_core.QueryField(fieldName: "default_vehicle_id");
  static final FAVS = amplify_core.QueryField(fieldName: "favs");
  static final UID = amplify_core.QueryField(fieldName: "uId");
  static final DEVICEID = amplify_core.QueryField(fieldName: "deviceId");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "EndUser";
    modelSchemaDefinition.pluralName = "EndUsers";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.USER_FULLNAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.DOB,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.date)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.JOINING_DATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.date)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.EMAIL,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.CONTACT,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.BALANCE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.DEFAULT_VEHICLE_ID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.FAVS,
      isRequired: false,
      isArray: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.collection, ofModelName: amplify_core.ModelFieldTypeEnum.string.name)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.UID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: EndUser.DEVICEID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _EndUserModelType extends amplify_core.ModelType<EndUser> {
  const _EndUserModelType();
  
  @override
  EndUser fromJson(Map<String, dynamic> jsonData) {
    return EndUser.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'EndUser';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [EndUser] in your schema.
 */
class EndUserModelIdentifier implements amplify_core.ModelIdentifier<EndUser> {
  final String id;

  /** Create an instance of EndUserModelIdentifier using [id] the primary key. */
  const EndUserModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'EndUserModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is EndUserModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}