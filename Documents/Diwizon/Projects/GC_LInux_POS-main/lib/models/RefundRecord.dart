/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the RefundRecord type in your schema. */
class RefundRecord extends amplify_core.Model {
  static const classType = const _RefundRecordModelType();
  final String id;
  final bool? _refunded;
  final String? _merchantId;
  final String? _merchantTransactionId;
  final String? _transactionId;
  final String? _amount;
  final String? _state;
  final String? _responseCode;
  final String? _paymentInstrument;
  final String? _code;
  final String? _message;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  RefundRecordModelIdentifier get modelIdentifier {
      return RefundRecordModelIdentifier(
        id: id
      );
  }
  
  bool? get refunded {
    return _refunded;
  }
  
  String? get merchantId {
    return _merchantId;
  }
  
  String? get merchantTransactionId {
    return _merchantTransactionId;
  }
  
  String? get transactionId {
    return _transactionId;
  }
  
  String? get amount {
    return _amount;
  }
  
  String? get state {
    return _state;
  }
  
  String? get responseCode {
    return _responseCode;
  }
  
  String? get paymentInstrument {
    return _paymentInstrument;
  }
  
  String? get code {
    return _code;
  }
  
  String? get message {
    return _message;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const RefundRecord._internal({required this.id, refunded, merchantId, merchantTransactionId, transactionId, amount, state, responseCode, paymentInstrument, code, message, createdAt, updatedAt}): _refunded = refunded, _merchantId = merchantId, _merchantTransactionId = merchantTransactionId, _transactionId = transactionId, _amount = amount, _state = state, _responseCode = responseCode, _paymentInstrument = paymentInstrument, _code = code, _message = message, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory RefundRecord({String? id, bool? refunded, String? merchantId, String? merchantTransactionId, String? transactionId, String? amount, String? state, String? responseCode, String? paymentInstrument, String? code, String? message}) {
    return RefundRecord._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      refunded: refunded,
      merchantId: merchantId,
      merchantTransactionId: merchantTransactionId,
      transactionId: transactionId,
      amount: amount,
      state: state,
      responseCode: responseCode,
      paymentInstrument: paymentInstrument,
      code: code,
      message: message);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RefundRecord &&
      id == other.id &&
      _refunded == other._refunded &&
      _merchantId == other._merchantId &&
      _merchantTransactionId == other._merchantTransactionId &&
      _transactionId == other._transactionId &&
      _amount == other._amount &&
      _state == other._state &&
      _responseCode == other._responseCode &&
      _paymentInstrument == other._paymentInstrument &&
      _code == other._code &&
      _message == other._message;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("RefundRecord {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("refunded=" + (_refunded != null ? _refunded!.toString() : "null") + ", ");
    buffer.write("merchantId=" + "$_merchantId" + ", ");
    buffer.write("merchantTransactionId=" + "$_merchantTransactionId" + ", ");
    buffer.write("transactionId=" + "$_transactionId" + ", ");
    buffer.write("amount=" + "$_amount" + ", ");
    buffer.write("state=" + "$_state" + ", ");
    buffer.write("responseCode=" + "$_responseCode" + ", ");
    buffer.write("paymentInstrument=" + "$_paymentInstrument" + ", ");
    buffer.write("code=" + "$_code" + ", ");
    buffer.write("message=" + "$_message" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  RefundRecord copyWith({bool? refunded, String? merchantId, String? merchantTransactionId, String? transactionId, String? amount, String? state, String? responseCode, String? paymentInstrument, String? code, String? message}) {
    return RefundRecord._internal(
      id: id,
      refunded: refunded ?? this.refunded,
      merchantId: merchantId ?? this.merchantId,
      merchantTransactionId: merchantTransactionId ?? this.merchantTransactionId,
      transactionId: transactionId ?? this.transactionId,
      amount: amount ?? this.amount,
      state: state ?? this.state,
      responseCode: responseCode ?? this.responseCode,
      paymentInstrument: paymentInstrument ?? this.paymentInstrument,
      code: code ?? this.code,
      message: message ?? this.message);
  }
  
  RefundRecord copyWithModelFieldValues({
    ModelFieldValue<bool?>? refunded,
    ModelFieldValue<String?>? merchantId,
    ModelFieldValue<String?>? merchantTransactionId,
    ModelFieldValue<String?>? transactionId,
    ModelFieldValue<String?>? amount,
    ModelFieldValue<String?>? state,
    ModelFieldValue<String?>? responseCode,
    ModelFieldValue<String?>? paymentInstrument,
    ModelFieldValue<String?>? code,
    ModelFieldValue<String?>? message
  }) {
    return RefundRecord._internal(
      id: id,
      refunded: refunded == null ? this.refunded : refunded.value,
      merchantId: merchantId == null ? this.merchantId : merchantId.value,
      merchantTransactionId: merchantTransactionId == null ? this.merchantTransactionId : merchantTransactionId.value,
      transactionId: transactionId == null ? this.transactionId : transactionId.value,
      amount: amount == null ? this.amount : amount.value,
      state: state == null ? this.state : state.value,
      responseCode: responseCode == null ? this.responseCode : responseCode.value,
      paymentInstrument: paymentInstrument == null ? this.paymentInstrument : paymentInstrument.value,
      code: code == null ? this.code : code.value,
      message: message == null ? this.message : message.value
    );
  }
  
  RefundRecord.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _refunded = json['refunded'],
      _merchantId = json['merchantId'],
      _merchantTransactionId = json['merchantTransactionId'],
      _transactionId = json['transactionId'],
      _amount = json['amount'],
      _state = json['state'],
      _responseCode = json['responseCode'],
      _paymentInstrument = json['paymentInstrument'],
      _code = json['code'],
      _message = json['message'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'refunded': _refunded, 'merchantId': _merchantId, 'merchantTransactionId': _merchantTransactionId, 'transactionId': _transactionId, 'amount': _amount, 'state': _state, 'responseCode': _responseCode, 'paymentInstrument': _paymentInstrument, 'code': _code, 'message': _message, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'refunded': _refunded,
    'merchantId': _merchantId,
    'merchantTransactionId': _merchantTransactionId,
    'transactionId': _transactionId,
    'amount': _amount,
    'state': _state,
    'responseCode': _responseCode,
    'paymentInstrument': _paymentInstrument,
    'code': _code,
    'message': _message,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<RefundRecordModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<RefundRecordModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final REFUNDED = amplify_core.QueryField(fieldName: "refunded");
  static final MERCHANTID = amplify_core.QueryField(fieldName: "merchantId");
  static final MERCHANTTRANSACTIONID = amplify_core.QueryField(fieldName: "merchantTransactionId");
  static final TRANSACTIONID = amplify_core.QueryField(fieldName: "transactionId");
  static final AMOUNT = amplify_core.QueryField(fieldName: "amount");
  static final STATE = amplify_core.QueryField(fieldName: "state");
  static final RESPONSECODE = amplify_core.QueryField(fieldName: "responseCode");
  static final PAYMENTINSTRUMENT = amplify_core.QueryField(fieldName: "paymentInstrument");
  static final CODE = amplify_core.QueryField(fieldName: "code");
  static final MESSAGE = amplify_core.QueryField(fieldName: "message");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "RefundRecord";
    modelSchemaDefinition.pluralName = "RefundRecords";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.REFUNDED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.MERCHANTID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.MERCHANTTRANSACTIONID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.TRANSACTIONID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.AMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.STATE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.RESPONSECODE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.PAYMENTINSTRUMENT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.CODE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RefundRecord.MESSAGE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _RefundRecordModelType extends amplify_core.ModelType<RefundRecord> {
  const _RefundRecordModelType();
  
  @override
  RefundRecord fromJson(Map<String, dynamic> jsonData) {
    return RefundRecord.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'RefundRecord';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [RefundRecord] in your schema.
 */
class RefundRecordModelIdentifier implements amplify_core.ModelIdentifier<RefundRecord> {
  final String id;

  /** Create an instance of RefundRecordModelIdentifier using [id] the primary key. */
  const RefundRecordModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'RefundRecordModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is RefundRecordModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}