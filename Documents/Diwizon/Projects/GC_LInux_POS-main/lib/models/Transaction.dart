/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Transaction type in your schema. */
class Transaction extends amplify_core.Model {
  static const classType = const _TransactionModelType();
  final String id;
  final double? _amount;
  final String? _method;
  final String? _reason;
  final String? _bookingId;
  final String? _uId;
  final amplify_core.TemporalDateTime? _dateTime;
  final String? _transRef;
  final String? _pgTransRef;
  final String? _status;
  final double? _walletAmountUsed;
  final double? _currentBalance;
  final String? _userName;
  final String? _userContact;
  final String? _note;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  TransactionModelIdentifier get modelIdentifier {
      return TransactionModelIdentifier(
        id: id
      );
  }
  
  double? get amount {
    return _amount;
  }
  
  String? get method {
    return _method;
  }
  
  String? get reason {
    return _reason;
  }
  
  String? get bookingId {
    return _bookingId;
  }
  
  String? get uId {
    return _uId;
  }
  
  amplify_core.TemporalDateTime? get dateTime {
    return _dateTime;
  }
  
  String? get transRef {
    return _transRef;
  }
  
  String? get pgTransRef {
    return _pgTransRef;
  }
  
  String? get status {
    return _status;
  }
  
  double? get walletAmountUsed {
    return _walletAmountUsed;
  }
  
  double? get currentBalance {
    return _currentBalance;
  }
  
  String? get userName {
    return _userName;
  }
  
  String? get userContact {
    return _userContact;
  }
  
  String? get note {
    return _note;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Transaction._internal({required this.id, amount, method, reason, bookingId, uId, dateTime, transRef, pgTransRef, status, walletAmountUsed, currentBalance, userName, userContact, note, createdAt, updatedAt}): _amount = amount, _method = method, _reason = reason, _bookingId = bookingId, _uId = uId, _dateTime = dateTime, _transRef = transRef, _pgTransRef = pgTransRef, _status = status, _walletAmountUsed = walletAmountUsed, _currentBalance = currentBalance, _userName = userName, _userContact = userContact, _note = note, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Transaction({String? id, double? amount, String? method, String? reason, String? bookingId, String? uId, amplify_core.TemporalDateTime? dateTime, String? transRef, String? pgTransRef, String? status, double? walletAmountUsed, double? currentBalance, String? userName, String? userContact, String? note}) {
    return Transaction._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      amount: amount,
      method: method,
      reason: reason,
      bookingId: bookingId,
      uId: uId,
      dateTime: dateTime,
      transRef: transRef,
      pgTransRef: pgTransRef,
      status: status,
      walletAmountUsed: walletAmountUsed,
      currentBalance: currentBalance,
      userName: userName,
      userContact: userContact,
      note: note);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Transaction &&
      id == other.id &&
      _amount == other._amount &&
      _method == other._method &&
      _reason == other._reason &&
      _bookingId == other._bookingId &&
      _uId == other._uId &&
      _dateTime == other._dateTime &&
      _transRef == other._transRef &&
      _pgTransRef == other._pgTransRef &&
      _status == other._status &&
      _walletAmountUsed == other._walletAmountUsed &&
      _currentBalance == other._currentBalance &&
      _userName == other._userName &&
      _userContact == other._userContact &&
      _note == other._note;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Transaction {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("amount=" + (_amount != null ? _amount!.toString() : "null") + ", ");
    buffer.write("method=" + "$_method" + ", ");
    buffer.write("reason=" + "$_reason" + ", ");
    buffer.write("bookingId=" + "$_bookingId" + ", ");
    buffer.write("uId=" + "$_uId" + ", ");
    buffer.write("dateTime=" + (_dateTime != null ? _dateTime!.format() : "null") + ", ");
    buffer.write("transRef=" + "$_transRef" + ", ");
    buffer.write("pgTransRef=" + "$_pgTransRef" + ", ");
    buffer.write("status=" + "$_status" + ", ");
    buffer.write("walletAmountUsed=" + (_walletAmountUsed != null ? _walletAmountUsed!.toString() : "null") + ", ");
    buffer.write("currentBalance=" + (_currentBalance != null ? _currentBalance!.toString() : "null") + ", ");
    buffer.write("userName=" + "$_userName" + ", ");
    buffer.write("userContact=" + "$_userContact" + ", ");
    buffer.write("note=" + "$_note" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Transaction copyWith({double? amount, String? method, String? reason, String? bookingId, String? uId, amplify_core.TemporalDateTime? dateTime, String? transRef, String? pgTransRef, String? status, double? walletAmountUsed, double? currentBalance, String? userName, String? userContact, String? note}) {
    return Transaction._internal(
      id: id,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      reason: reason ?? this.reason,
      bookingId: bookingId ?? this.bookingId,
      uId: uId ?? this.uId,
      dateTime: dateTime ?? this.dateTime,
      transRef: transRef ?? this.transRef,
      pgTransRef: pgTransRef ?? this.pgTransRef,
      status: status ?? this.status,
      walletAmountUsed: walletAmountUsed ?? this.walletAmountUsed,
      currentBalance: currentBalance ?? this.currentBalance,
      userName: userName ?? this.userName,
      userContact: userContact ?? this.userContact,
      note: note ?? this.note);
  }
  
  Transaction copyWithModelFieldValues({
    ModelFieldValue<double?>? amount,
    ModelFieldValue<String?>? method,
    ModelFieldValue<String?>? reason,
    ModelFieldValue<String?>? bookingId,
    ModelFieldValue<String?>? uId,
    ModelFieldValue<amplify_core.TemporalDateTime?>? dateTime,
    ModelFieldValue<String?>? transRef,
    ModelFieldValue<String?>? pgTransRef,
    ModelFieldValue<String?>? status,
    ModelFieldValue<double?>? walletAmountUsed,
    ModelFieldValue<double?>? currentBalance,
    ModelFieldValue<String?>? userName,
    ModelFieldValue<String?>? userContact,
    ModelFieldValue<String?>? note
  }) {
    return Transaction._internal(
      id: id,
      amount: amount == null ? this.amount : amount.value,
      method: method == null ? this.method : method.value,
      reason: reason == null ? this.reason : reason.value,
      bookingId: bookingId == null ? this.bookingId : bookingId.value,
      uId: uId == null ? this.uId : uId.value,
      dateTime: dateTime == null ? this.dateTime : dateTime.value,
      transRef: transRef == null ? this.transRef : transRef.value,
      pgTransRef: pgTransRef == null ? this.pgTransRef : pgTransRef.value,
      status: status == null ? this.status : status.value,
      walletAmountUsed: walletAmountUsed == null ? this.walletAmountUsed : walletAmountUsed.value,
      currentBalance: currentBalance == null ? this.currentBalance : currentBalance.value,
      userName: userName == null ? this.userName : userName.value,
      userContact: userContact == null ? this.userContact : userContact.value,
      note: note == null ? this.note : note.value
    );
  }
  
  Transaction.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _amount = (json['amount'] as num?)?.toDouble(),
      _method = json['method'],
      _reason = json['reason'],
      _bookingId = json['bookingId'],
      _uId = json['uId'],
      _dateTime = json['dateTime'] != null ? amplify_core.TemporalDateTime.fromString(json['dateTime']) : null,
      _transRef = json['transRef'],
      _pgTransRef = json['pgTransRef'],
      _status = json['status'],
      _walletAmountUsed = (json['walletAmountUsed'] as num?)?.toDouble(),
      _currentBalance = (json['currentBalance'] as num?)?.toDouble(),
      _userName = json['userName'],
      _userContact = json['userContact'],
      _note = json['note'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'amount': _amount, 'method': _method, 'reason': _reason, 'bookingId': _bookingId, 'uId': _uId, 'dateTime': _dateTime?.format(), 'transRef': _transRef, 'pgTransRef': _pgTransRef, 'status': _status, 'walletAmountUsed': _walletAmountUsed, 'currentBalance': _currentBalance, 'userName': _userName, 'userContact': _userContact, 'note': _note, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'amount': _amount,
    'method': _method,
    'reason': _reason,
    'bookingId': _bookingId,
    'uId': _uId,
    'dateTime': _dateTime,
    'transRef': _transRef,
    'pgTransRef': _pgTransRef,
    'status': _status,
    'walletAmountUsed': _walletAmountUsed,
    'currentBalance': _currentBalance,
    'userName': _userName,
    'userContact': _userContact,
    'note': _note,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<TransactionModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<TransactionModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final AMOUNT = amplify_core.QueryField(fieldName: "amount");
  static final METHOD = amplify_core.QueryField(fieldName: "method");
  static final REASON = amplify_core.QueryField(fieldName: "reason");
  static final BOOKINGID = amplify_core.QueryField(fieldName: "bookingId");
  static final UID = amplify_core.QueryField(fieldName: "uId");
  static final DATETIME = amplify_core.QueryField(fieldName: "dateTime");
  static final TRANSREF = amplify_core.QueryField(fieldName: "transRef");
  static final PGTRANSREF = amplify_core.QueryField(fieldName: "pgTransRef");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final WALLETAMOUNTUSED = amplify_core.QueryField(fieldName: "walletAmountUsed");
  static final CURRENTBALANCE = amplify_core.QueryField(fieldName: "currentBalance");
  static final USERNAME = amplify_core.QueryField(fieldName: "userName");
  static final USERCONTACT = amplify_core.QueryField(fieldName: "userContact");
  static final NOTE = amplify_core.QueryField(fieldName: "note");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Transaction";
    modelSchemaDefinition.pluralName = "Transactions";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.AMOUNT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.METHOD,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.REASON,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.BOOKINGID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.UID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.DATETIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.TRANSREF,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.PGTRANSREF,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.WALLETAMOUNTUSED,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.CURRENTBALANCE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.double)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.USERNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.USERCONTACT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Transaction.NOTE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _TransactionModelType extends amplify_core.ModelType<Transaction> {
  const _TransactionModelType();
  
  @override
  Transaction fromJson(Map<String, dynamic> jsonData) {
    return Transaction.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Transaction';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Transaction] in your schema.
 */
class TransactionModelIdentifier implements amplify_core.ModelIdentifier<Transaction> {
  final String id;

  /** Create an instance of TransactionModelIdentifier using [id] the primary key. */
  const TransactionModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'TransactionModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is TransactionModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}