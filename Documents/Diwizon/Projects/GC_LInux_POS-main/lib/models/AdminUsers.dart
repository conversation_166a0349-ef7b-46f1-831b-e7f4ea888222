/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;
import 'package:collection/collection.dart';


/** This is an auto generated class representing the AdminUsers type in your schema. */
class AdminUsers extends amplify_core.Model {
  static const classType = const _AdminUsersModelType();
  final String id;
  final bool? _isCPO;
  final String? _uId;
  final String? _phone;
  final String? _name;
  final List<String>? _permits;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  AdminUsersModelIdentifier get modelIdentifier {
      return AdminUsersModelIdentifier(
        id: id
      );
  }
  
  bool? get isCPO {
    return _isCPO;
  }
  
  String? get uId {
    return _uId;
  }
  
  String? get phone {
    return _phone;
  }
  
  String? get name {
    return _name;
  }
  
  List<String>? get permits {
    return _permits;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const AdminUsers._internal({required this.id, isCPO, uId, phone, name, permits, createdAt, updatedAt}): _isCPO = isCPO, _uId = uId, _phone = phone, _name = name, _permits = permits, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory AdminUsers({String? id, bool? isCPO, String? uId, String? phone, String? name, List<String>? permits}) {
    return AdminUsers._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      isCPO: isCPO,
      uId: uId,
      phone: phone,
      name: name,
      permits: permits != null ? List<String>.unmodifiable(permits) : permits);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminUsers &&
      id == other.id &&
      _isCPO == other._isCPO &&
      _uId == other._uId &&
      _phone == other._phone &&
      _name == other._name &&
      DeepCollectionEquality().equals(_permits, other._permits);
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("AdminUsers {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("isCPO=" + (_isCPO != null ? _isCPO!.toString() : "null") + ", ");
    buffer.write("uId=" + "$_uId" + ", ");
    buffer.write("phone=" + "$_phone" + ", ");
    buffer.write("name=" + "$_name" + ", ");
    buffer.write("permits=" + (_permits != null ? _permits!.toString() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  AdminUsers copyWith({bool? isCPO, String? uId, String? phone, String? name, List<String>? permits}) {
    return AdminUsers._internal(
      id: id,
      isCPO: isCPO ?? this.isCPO,
      uId: uId ?? this.uId,
      phone: phone ?? this.phone,
      name: name ?? this.name,
      permits: permits ?? this.permits);
  }
  
  AdminUsers copyWithModelFieldValues({
    ModelFieldValue<bool?>? isCPO,
    ModelFieldValue<String?>? uId,
    ModelFieldValue<String?>? phone,
    ModelFieldValue<String?>? name,
    ModelFieldValue<List<String>?>? permits
  }) {
    return AdminUsers._internal(
      id: id,
      isCPO: isCPO == null ? this.isCPO : isCPO.value,
      uId: uId == null ? this.uId : uId.value,
      phone: phone == null ? this.phone : phone.value,
      name: name == null ? this.name : name.value,
      permits: permits == null ? this.permits : permits.value
    );
  }
  
  AdminUsers.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _isCPO = json['isCPO'],
      _uId = json['uId'],
      _phone = json['phone'],
      _name = json['name'],
      _permits = json['permits']?.cast<String>(),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'isCPO': _isCPO, 'uId': _uId, 'phone': _phone, 'name': _name, 'permits': _permits, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'isCPO': _isCPO,
    'uId': _uId,
    'phone': _phone,
    'name': _name,
    'permits': _permits,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<AdminUsersModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<AdminUsersModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final ISCPO = amplify_core.QueryField(fieldName: "isCPO");
  static final UID = amplify_core.QueryField(fieldName: "uId");
  static final PHONE = amplify_core.QueryField(fieldName: "phone");
  static final NAME = amplify_core.QueryField(fieldName: "name");
  static final PERMITS = amplify_core.QueryField(fieldName: "permits");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "AdminUsers";
    modelSchemaDefinition.pluralName = "AdminUsers";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: AdminUsers.ISCPO,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: AdminUsers.UID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: AdminUsers.PHONE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: AdminUsers.NAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: AdminUsers.PERMITS,
      isRequired: false,
      isArray: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.collection, ofModelName: amplify_core.ModelFieldTypeEnum.string.name)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _AdminUsersModelType extends amplify_core.ModelType<AdminUsers> {
  const _AdminUsersModelType();
  
  @override
  AdminUsers fromJson(Map<String, dynamic> jsonData) {
    return AdminUsers.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'AdminUsers';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [AdminUsers] in your schema.
 */
class AdminUsersModelIdentifier implements amplify_core.ModelIdentifier<AdminUsers> {
  final String id;

  /** Create an instance of AdminUsersModelIdentifier using [id] the primary key. */
  const AdminUsersModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'AdminUsersModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is AdminUsersModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}