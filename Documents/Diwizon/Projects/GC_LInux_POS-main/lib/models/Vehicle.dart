/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Vehicle type in your schema. */
class Vehicle extends amplify_core.Model {
  static const classType = const _VehicleModelType();
  final String id;
  final String? _model;
  final String? _type;
  final String? _manufactId;
  final String? _batteryCapacity;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  VehicleModelIdentifier get modelIdentifier {
      return VehicleModelIdentifier(
        id: id
      );
  }
  
  String get model {
    try {
      return _model!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get type {
    return _type;
  }
  
  String get manufactId {
    try {
      return _manufactId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get batteryCapacity {
    try {
      return _batteryCapacity!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Vehicle._internal({required this.id, required model, type, required manufactId, required batteryCapacity, createdAt, updatedAt}): _model = model, _type = type, _manufactId = manufactId, _batteryCapacity = batteryCapacity, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Vehicle({String? id, required String model, String? type, required String manufactId, required String batteryCapacity}) {
    return Vehicle._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      model: model,
      type: type,
      manufactId: manufactId,
      batteryCapacity: batteryCapacity);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Vehicle &&
      id == other.id &&
      _model == other._model &&
      _type == other._type &&
      _manufactId == other._manufactId &&
      _batteryCapacity == other._batteryCapacity;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Vehicle {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("model=" + "$_model" + ", ");
    buffer.write("type=" + "$_type" + ", ");
    buffer.write("manufactId=" + "$_manufactId" + ", ");
    buffer.write("batteryCapacity=" + "$_batteryCapacity" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Vehicle copyWith({String? model, String? type, String? manufactId, String? batteryCapacity}) {
    return Vehicle._internal(
      id: id,
      model: model ?? this.model,
      type: type ?? this.type,
      manufactId: manufactId ?? this.manufactId,
      batteryCapacity: batteryCapacity ?? this.batteryCapacity);
  }
  
  Vehicle copyWithModelFieldValues({
    ModelFieldValue<String>? model,
    ModelFieldValue<String?>? type,
    ModelFieldValue<String>? manufactId,
    ModelFieldValue<String>? batteryCapacity
  }) {
    return Vehicle._internal(
      id: id,
      model: model == null ? this.model : model.value,
      type: type == null ? this.type : type.value,
      manufactId: manufactId == null ? this.manufactId : manufactId.value,
      batteryCapacity: batteryCapacity == null ? this.batteryCapacity : batteryCapacity.value
    );
  }
  
  Vehicle.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _model = json['model'],
      _type = json['type'],
      _manufactId = json['manufactId'],
      _batteryCapacity = json['batteryCapacity'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'model': _model, 'type': _type, 'manufactId': _manufactId, 'batteryCapacity': _batteryCapacity, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'model': _model,
    'type': _type,
    'manufactId': _manufactId,
    'batteryCapacity': _batteryCapacity,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<VehicleModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<VehicleModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final MODEL = amplify_core.QueryField(fieldName: "model");
  static final TYPE = amplify_core.QueryField(fieldName: "type");
  static final MANUFACTID = amplify_core.QueryField(fieldName: "manufactId");
  static final BATTERYCAPACITY = amplify_core.QueryField(fieldName: "batteryCapacity");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Vehicle";
    modelSchemaDefinition.pluralName = "Vehicles";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Vehicle.MODEL,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Vehicle.TYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Vehicle.MANUFACTID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Vehicle.BATTERYCAPACITY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _VehicleModelType extends amplify_core.ModelType<Vehicle> {
  const _VehicleModelType();
  
  @override
  Vehicle fromJson(Map<String, dynamic> jsonData) {
    return Vehicle.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Vehicle';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Vehicle] in your schema.
 */
class VehicleModelIdentifier implements amplify_core.ModelIdentifier<Vehicle> {
  final String id;

  /** Create an instance of VehicleModelIdentifier using [id] the primary key. */
  const VehicleModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'VehicleModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is VehicleModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}