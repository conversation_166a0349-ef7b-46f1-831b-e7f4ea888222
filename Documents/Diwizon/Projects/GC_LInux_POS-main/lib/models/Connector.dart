/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Connector type in your schema. */
class Connector extends amplify_core.Model {
  static const classType = const _ConnectorModelType();
  final String id;
  final int? _connector_number;
  final bool? _isActive;
  final String? _connectorStatus;
  final String? _typeId;
  final String? _chargerId;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ConnectorModelIdentifier get modelIdentifier {
      return ConnectorModelIdentifier(
        id: id
      );
  }
  
  int get connector_number {
    try {
      return _connector_number!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool? get isActive {
    return _isActive;
  }
  
  String? get connectorStatus {
    return _connectorStatus;
  }
  
  String get typeId {
    try {
      return _typeId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get chargerId {
    try {
      return _chargerId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Connector._internal({required this.id, required connector_number, isActive, connectorStatus, required typeId, required chargerId, createdAt, updatedAt}): _connector_number = connector_number, _isActive = isActive, _connectorStatus = connectorStatus, _typeId = typeId, _chargerId = chargerId, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Connector({String? id, required int connector_number, bool? isActive, String? connectorStatus, required String typeId, required String chargerId}) {
    return Connector._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      connector_number: connector_number,
      isActive: isActive,
      connectorStatus: connectorStatus,
      typeId: typeId,
      chargerId: chargerId);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Connector &&
      id == other.id &&
      _connector_number == other._connector_number &&
      _isActive == other._isActive &&
      _connectorStatus == other._connectorStatus &&
      _typeId == other._typeId &&
      _chargerId == other._chargerId;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Connector {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("connector_number=" + (_connector_number != null ? _connector_number!.toString() : "null") + ", ");
    buffer.write("isActive=" + (_isActive != null ? _isActive!.toString() : "null") + ", ");
    buffer.write("connectorStatus=" + "$_connectorStatus" + ", ");
    buffer.write("typeId=" + "$_typeId" + ", ");
    buffer.write("chargerId=" + "$_chargerId" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Connector copyWith({int? connector_number, bool? isActive, String? connectorStatus, String? typeId, String? chargerId}) {
    return Connector._internal(
      id: id,
      connector_number: connector_number ?? this.connector_number,
      isActive: isActive ?? this.isActive,
      connectorStatus: connectorStatus ?? this.connectorStatus,
      typeId: typeId ?? this.typeId,
      chargerId: chargerId ?? this.chargerId);
  }
  
  Connector copyWithModelFieldValues({
    ModelFieldValue<int>? connector_number,
    ModelFieldValue<bool?>? isActive,
    ModelFieldValue<String?>? connectorStatus,
    ModelFieldValue<String>? typeId,
    ModelFieldValue<String>? chargerId
  }) {
    return Connector._internal(
      id: id,
      connector_number: connector_number == null ? this.connector_number : connector_number.value,
      isActive: isActive == null ? this.isActive : isActive.value,
      connectorStatus: connectorStatus == null ? this.connectorStatus : connectorStatus.value,
      typeId: typeId == null ? this.typeId : typeId.value,
      chargerId: chargerId == null ? this.chargerId : chargerId.value
    );
  }
  
  Connector.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _connector_number = (json['connector_number'] as num?)?.toInt(),
      _isActive = json['isActive'],
      _connectorStatus = json['connectorStatus'],
      _typeId = json['typeId'],
      _chargerId = json['chargerId'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'connector_number': _connector_number, 'isActive': _isActive, 'connectorStatus': _connectorStatus, 'typeId': _typeId, 'chargerId': _chargerId, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'connector_number': _connector_number,
    'isActive': _isActive,
    'connectorStatus': _connectorStatus,
    'typeId': _typeId,
    'chargerId': _chargerId,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ConnectorModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ConnectorModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CONNECTOR_NUMBER = amplify_core.QueryField(fieldName: "connector_number");
  static final ISACTIVE = amplify_core.QueryField(fieldName: "isActive");
  static final CONNECTORSTATUS = amplify_core.QueryField(fieldName: "connectorStatus");
  static final TYPEID = amplify_core.QueryField(fieldName: "typeId");
  static final CHARGERID = amplify_core.QueryField(fieldName: "chargerId");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Connector";
    modelSchemaDefinition.pluralName = "Connectors";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Connector.CONNECTOR_NUMBER,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.int)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Connector.ISACTIVE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Connector.CONNECTORSTATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Connector.TYPEID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Connector.CHARGERID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ConnectorModelType extends amplify_core.ModelType<Connector> {
  const _ConnectorModelType();
  
  @override
  Connector fromJson(Map<String, dynamic> jsonData) {
    return Connector.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Connector';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Connector] in your schema.
 */
class ConnectorModelIdentifier implements amplify_core.ModelIdentifier<Connector> {
  final String id;

  /** Create an instance of ConnectorModelIdentifier using [id] the primary key. */
  const ConnectorModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ConnectorModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ConnectorModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}