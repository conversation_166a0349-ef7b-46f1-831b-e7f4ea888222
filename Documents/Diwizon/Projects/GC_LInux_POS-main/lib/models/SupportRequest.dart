/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the SupportRequest type in your schema. */
class SupportRequest extends amplify_core.Model {
  static const classType = const _SupportRequestModelType();
  final String id;
  final String? _category;
  final String? _subCategory;
  final String? _chargingStation;
  final String? _remarks;
  final String? _adminRemarks;
  final String? _status;
  final String? _code;
  final bool? _canCall;
  final String? _uId;
  final String? _userName;
  final String? _userContact;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  SupportRequestModelIdentifier get modelIdentifier {
      return SupportRequestModelIdentifier(
        id: id
      );
  }
  
  String? get category {
    return _category;
  }
  
  String? get subCategory {
    return _subCategory;
  }
  
  String? get chargingStation {
    return _chargingStation;
  }
  
  String? get remarks {
    return _remarks;
  }
  
  String? get adminRemarks {
    return _adminRemarks;
  }
  
  String? get status {
    return _status;
  }
  
  String? get code {
    return _code;
  }
  
  bool? get canCall {
    return _canCall;
  }
  
  String? get uId {
    return _uId;
  }
  
  String? get userName {
    return _userName;
  }
  
  String? get userContact {
    return _userContact;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const SupportRequest._internal({required this.id, category, subCategory, chargingStation, remarks, adminRemarks, status, code, canCall, uId, userName, userContact, createdAt, updatedAt}): _category = category, _subCategory = subCategory, _chargingStation = chargingStation, _remarks = remarks, _adminRemarks = adminRemarks, _status = status, _code = code, _canCall = canCall, _uId = uId, _userName = userName, _userContact = userContact, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory SupportRequest({String? id, String? category, String? subCategory, String? chargingStation, String? remarks, String? adminRemarks, String? status, String? code, bool? canCall, String? uId, String? userName, String? userContact}) {
    return SupportRequest._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      category: category,
      subCategory: subCategory,
      chargingStation: chargingStation,
      remarks: remarks,
      adminRemarks: adminRemarks,
      status: status,
      code: code,
      canCall: canCall,
      uId: uId,
      userName: userName,
      userContact: userContact);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SupportRequest &&
      id == other.id &&
      _category == other._category &&
      _subCategory == other._subCategory &&
      _chargingStation == other._chargingStation &&
      _remarks == other._remarks &&
      _adminRemarks == other._adminRemarks &&
      _status == other._status &&
      _code == other._code &&
      _canCall == other._canCall &&
      _uId == other._uId &&
      _userName == other._userName &&
      _userContact == other._userContact;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("SupportRequest {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("category=" + "$_category" + ", ");
    buffer.write("subCategory=" + "$_subCategory" + ", ");
    buffer.write("chargingStation=" + "$_chargingStation" + ", ");
    buffer.write("remarks=" + "$_remarks" + ", ");
    buffer.write("adminRemarks=" + "$_adminRemarks" + ", ");
    buffer.write("status=" + "$_status" + ", ");
    buffer.write("code=" + "$_code" + ", ");
    buffer.write("canCall=" + (_canCall != null ? _canCall!.toString() : "null") + ", ");
    buffer.write("uId=" + "$_uId" + ", ");
    buffer.write("userName=" + "$_userName" + ", ");
    buffer.write("userContact=" + "$_userContact" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  SupportRequest copyWith({String? category, String? subCategory, String? chargingStation, String? remarks, String? adminRemarks, String? status, String? code, bool? canCall, String? uId, String? userName, String? userContact}) {
    return SupportRequest._internal(
      id: id,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      chargingStation: chargingStation ?? this.chargingStation,
      remarks: remarks ?? this.remarks,
      adminRemarks: adminRemarks ?? this.adminRemarks,
      status: status ?? this.status,
      code: code ?? this.code,
      canCall: canCall ?? this.canCall,
      uId: uId ?? this.uId,
      userName: userName ?? this.userName,
      userContact: userContact ?? this.userContact);
  }
  
  SupportRequest copyWithModelFieldValues({
    ModelFieldValue<String?>? category,
    ModelFieldValue<String?>? subCategory,
    ModelFieldValue<String?>? chargingStation,
    ModelFieldValue<String?>? remarks,
    ModelFieldValue<String?>? adminRemarks,
    ModelFieldValue<String?>? status,
    ModelFieldValue<String?>? code,
    ModelFieldValue<bool?>? canCall,
    ModelFieldValue<String?>? uId,
    ModelFieldValue<String?>? userName,
    ModelFieldValue<String?>? userContact
  }) {
    return SupportRequest._internal(
      id: id,
      category: category == null ? this.category : category.value,
      subCategory: subCategory == null ? this.subCategory : subCategory.value,
      chargingStation: chargingStation == null ? this.chargingStation : chargingStation.value,
      remarks: remarks == null ? this.remarks : remarks.value,
      adminRemarks: adminRemarks == null ? this.adminRemarks : adminRemarks.value,
      status: status == null ? this.status : status.value,
      code: code == null ? this.code : code.value,
      canCall: canCall == null ? this.canCall : canCall.value,
      uId: uId == null ? this.uId : uId.value,
      userName: userName == null ? this.userName : userName.value,
      userContact: userContact == null ? this.userContact : userContact.value
    );
  }
  
  SupportRequest.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _category = json['category'],
      _subCategory = json['subCategory'],
      _chargingStation = json['chargingStation'],
      _remarks = json['remarks'],
      _adminRemarks = json['adminRemarks'],
      _status = json['status'],
      _code = json['code'],
      _canCall = json['canCall'],
      _uId = json['uId'],
      _userName = json['userName'],
      _userContact = json['userContact'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'category': _category, 'subCategory': _subCategory, 'chargingStation': _chargingStation, 'remarks': _remarks, 'adminRemarks': _adminRemarks, 'status': _status, 'code': _code, 'canCall': _canCall, 'uId': _uId, 'userName': _userName, 'userContact': _userContact, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'category': _category,
    'subCategory': _subCategory,
    'chargingStation': _chargingStation,
    'remarks': _remarks,
    'adminRemarks': _adminRemarks,
    'status': _status,
    'code': _code,
    'canCall': _canCall,
    'uId': _uId,
    'userName': _userName,
    'userContact': _userContact,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<SupportRequestModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<SupportRequestModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CATEGORY = amplify_core.QueryField(fieldName: "category");
  static final SUBCATEGORY = amplify_core.QueryField(fieldName: "subCategory");
  static final CHARGINGSTATION = amplify_core.QueryField(fieldName: "chargingStation");
  static final REMARKS = amplify_core.QueryField(fieldName: "remarks");
  static final ADMINREMARKS = amplify_core.QueryField(fieldName: "adminRemarks");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final CODE = amplify_core.QueryField(fieldName: "code");
  static final CANCALL = amplify_core.QueryField(fieldName: "canCall");
  static final UID = amplify_core.QueryField(fieldName: "uId");
  static final USERNAME = amplify_core.QueryField(fieldName: "userName");
  static final USERCONTACT = amplify_core.QueryField(fieldName: "userContact");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "SupportRequest";
    modelSchemaDefinition.pluralName = "SupportRequests";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.CATEGORY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.SUBCATEGORY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.CHARGINGSTATION,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.REMARKS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.ADMINREMARKS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.STATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.CODE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.CANCALL,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.UID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.USERNAME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: SupportRequest.USERCONTACT,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _SupportRequestModelType extends amplify_core.ModelType<SupportRequest> {
  const _SupportRequestModelType();
  
  @override
  SupportRequest fromJson(Map<String, dynamic> jsonData) {
    return SupportRequest.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'SupportRequest';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [SupportRequest] in your schema.
 */
class SupportRequestModelIdentifier implements amplify_core.ModelIdentifier<SupportRequest> {
  final String id;

  /** Create an instance of SupportRequestModelIdentifier using [id] the primary key. */
  const SupportRequestModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'SupportRequestModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is SupportRequestModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}