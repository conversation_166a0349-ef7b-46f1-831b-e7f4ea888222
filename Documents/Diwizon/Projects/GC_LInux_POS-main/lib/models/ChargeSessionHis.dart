/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the ChargeSessionHis type in your schema. */
class ChargeSessionHis extends amplify_core.Model {
  static const classType = const _ChargeSessionHisModelType();
  final String id;
  final String? _eventType;
  final amplify_core.TemporalDateTime? _eventTime;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  ChargeSessionHisModelIdentifier get modelIdentifier {
      return ChargeSessionHisModelIdentifier(
        id: id
      );
  }
  
  String? get eventType {
    return _eventType;
  }
  
  amplify_core.TemporalDateTime? get eventTime {
    return _eventTime;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const ChargeSessionHis._internal({required this.id, eventType, eventTime, createdAt, updatedAt}): _eventType = eventType, _eventTime = eventTime, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory ChargeSessionHis({String? id, String? eventType, amplify_core.TemporalDateTime? eventTime}) {
    return ChargeSessionHis._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      eventType: eventType,
      eventTime: eventTime);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ChargeSessionHis &&
      id == other.id &&
      _eventType == other._eventType &&
      _eventTime == other._eventTime;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("ChargeSessionHis {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("eventType=" + "$_eventType" + ", ");
    buffer.write("eventTime=" + (_eventTime != null ? _eventTime!.format() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  ChargeSessionHis copyWith({String? eventType, amplify_core.TemporalDateTime? eventTime}) {
    return ChargeSessionHis._internal(
      id: id,
      eventType: eventType ?? this.eventType,
      eventTime: eventTime ?? this.eventTime);
  }
  
  ChargeSessionHis copyWithModelFieldValues({
    ModelFieldValue<String?>? eventType,
    ModelFieldValue<amplify_core.TemporalDateTime?>? eventTime
  }) {
    return ChargeSessionHis._internal(
      id: id,
      eventType: eventType == null ? this.eventType : eventType.value,
      eventTime: eventTime == null ? this.eventTime : eventTime.value
    );
  }
  
  ChargeSessionHis.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _eventType = json['eventType'],
      _eventTime = json['eventTime'] != null ? amplify_core.TemporalDateTime.fromString(json['eventTime']) : null,
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'eventType': _eventType, 'eventTime': _eventTime?.format(), 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'eventType': _eventType,
    'eventTime': _eventTime,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<ChargeSessionHisModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<ChargeSessionHisModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final EVENTTYPE = amplify_core.QueryField(fieldName: "eventType");
  static final EVENTTIME = amplify_core.QueryField(fieldName: "eventTime");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "ChargeSessionHis";
    modelSchemaDefinition.pluralName = "ChargeSessionHis";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargeSessionHis.EVENTTYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: ChargeSessionHis.EVENTTIME,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _ChargeSessionHisModelType extends amplify_core.ModelType<ChargeSessionHis> {
  const _ChargeSessionHisModelType();
  
  @override
  ChargeSessionHis fromJson(Map<String, dynamic> jsonData) {
    return ChargeSessionHis.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'ChargeSessionHis';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [ChargeSessionHis] in your schema.
 */
class ChargeSessionHisModelIdentifier implements amplify_core.ModelIdentifier<ChargeSessionHis> {
  final String id;

  /** Create an instance of ChargeSessionHisModelIdentifier using [id] the primary key. */
  const ChargeSessionHisModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'ChargeSessionHisModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is ChargeSessionHisModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}