/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;
import 'package:collection/collection.dart';


/** This is an auto generated class representing the RFIDSchema type in your schema. */
class RFIDSchema extends amplify_core.Model {
  static const classType = const _RFIDSchemaModelType();
  final String id;
  final String? _rfidValue;
  final amplify_core.TemporalDate? _expires;
  final bool? _isactive;
  final String? _userId;
  final String? _vehical_number;
  final List<String>? _favs;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  RFIDSchemaModelIdentifier get modelIdentifier {
      return RFIDSchemaModelIdentifier(
        id: id
      );
  }
  
  String get rfidValue {
    try {
      return _rfidValue!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  amplify_core.TemporalDate get expires {
    try {
      return _expires!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool? get isactive {
    return _isactive;
  }
  
  String get userId {
    try {
      return _userId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get vehical_number {
    return _vehical_number;
  }
  
  List<String>? get favs {
    return _favs;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const RFIDSchema._internal({required this.id, required rfidValue, required expires, isactive, required userId, vehical_number, favs, createdAt, updatedAt}): _rfidValue = rfidValue, _expires = expires, _isactive = isactive, _userId = userId, _vehical_number = vehical_number, _favs = favs, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory RFIDSchema({String? id, required String rfidValue, required amplify_core.TemporalDate expires, bool? isactive, required String userId, String? vehical_number, List<String>? favs}) {
    return RFIDSchema._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      rfidValue: rfidValue,
      expires: expires,
      isactive: isactive,
      userId: userId,
      vehical_number: vehical_number,
      favs: favs != null ? List<String>.unmodifiable(favs) : favs);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is RFIDSchema &&
      id == other.id &&
      _rfidValue == other._rfidValue &&
      _expires == other._expires &&
      _isactive == other._isactive &&
      _userId == other._userId &&
      _vehical_number == other._vehical_number &&
      DeepCollectionEquality().equals(_favs, other._favs);
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("RFIDSchema {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("rfidValue=" + "$_rfidValue" + ", ");
    buffer.write("expires=" + (_expires != null ? _expires!.format() : "null") + ", ");
    buffer.write("isactive=" + (_isactive != null ? _isactive!.toString() : "null") + ", ");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("vehical_number=" + "$_vehical_number" + ", ");
    buffer.write("favs=" + (_favs != null ? _favs!.toString() : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  RFIDSchema copyWith({String? rfidValue, amplify_core.TemporalDate? expires, bool? isactive, String? userId, String? vehical_number, List<String>? favs}) {
    return RFIDSchema._internal(
      id: id,
      rfidValue: rfidValue ?? this.rfidValue,
      expires: expires ?? this.expires,
      isactive: isactive ?? this.isactive,
      userId: userId ?? this.userId,
      vehical_number: vehical_number ?? this.vehical_number,
      favs: favs ?? this.favs);
  }
  
  RFIDSchema copyWithModelFieldValues({
    ModelFieldValue<String>? rfidValue,
    ModelFieldValue<amplify_core.TemporalDate>? expires,
    ModelFieldValue<bool?>? isactive,
    ModelFieldValue<String>? userId,
    ModelFieldValue<String?>? vehical_number,
    ModelFieldValue<List<String>?>? favs
  }) {
    return RFIDSchema._internal(
      id: id,
      rfidValue: rfidValue == null ? this.rfidValue : rfidValue.value,
      expires: expires == null ? this.expires : expires.value,
      isactive: isactive == null ? this.isactive : isactive.value,
      userId: userId == null ? this.userId : userId.value,
      vehical_number: vehical_number == null ? this.vehical_number : vehical_number.value,
      favs: favs == null ? this.favs : favs.value
    );
  }
  
  RFIDSchema.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _rfidValue = json['rfidValue'],
      _expires = json['expires'] != null ? amplify_core.TemporalDate.fromString(json['expires']) : null,
      _isactive = json['isactive'],
      _userId = json['userId'],
      _vehical_number = json['vehical_number'],
      _favs = json['favs']?.cast<String>(),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'rfidValue': _rfidValue, 'expires': _expires?.format(), 'isactive': _isactive, 'userId': _userId, 'vehical_number': _vehical_number, 'favs': _favs, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'rfidValue': _rfidValue,
    'expires': _expires,
    'isactive': _isactive,
    'userId': _userId,
    'vehical_number': _vehical_number,
    'favs': _favs,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<RFIDSchemaModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<RFIDSchemaModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final RFIDVALUE = amplify_core.QueryField(fieldName: "rfidValue");
  static final EXPIRES = amplify_core.QueryField(fieldName: "expires");
  static final ISACTIVE = amplify_core.QueryField(fieldName: "isactive");
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static final VEHICAL_NUMBER = amplify_core.QueryField(fieldName: "vehical_number");
  static final FAVS = amplify_core.QueryField(fieldName: "favs");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "RFIDSchema";
    modelSchemaDefinition.pluralName = "RFIDSchemas";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.RFIDVALUE,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.EXPIRES,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.date)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.ISACTIVE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.USERID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.VEHICAL_NUMBER,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: RFIDSchema.FAVS,
      isRequired: false,
      isArray: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.collection, ofModelName: amplify_core.ModelFieldTypeEnum.string.name)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _RFIDSchemaModelType extends amplify_core.ModelType<RFIDSchema> {
  const _RFIDSchemaModelType();
  
  @override
  RFIDSchema fromJson(Map<String, dynamic> jsonData) {
    return RFIDSchema.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'RFIDSchema';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [RFIDSchema] in your schema.
 */
class RFIDSchemaModelIdentifier implements amplify_core.ModelIdentifier<RFIDSchema> {
  final String id;

  /** Create an instance of RFIDSchemaModelIdentifier using [id] the primary key. */
  const RFIDSchemaModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'RFIDSchemaModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is RFIDSchemaModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}