/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the Issues type in your schema. */
class Issues extends amplify_core.Model {
  static const classType = const _IssuesModelType();
  final String id;
  final String? _category;
  final String? _sub_category;
  final String? _priority;
  final String? _description;
  final String? _mobile_no;
  final String? _attachments;
  final bool? _status;
  final String? _operator_name;
  final String? _userId;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => id;
  
  IssuesModelIdentifier get modelIdentifier {
      return IssuesModelIdentifier(
        id: id
      );
  }
  
  String? get category {
    return _category;
  }
  
  String get sub_category {
    try {
      return _sub_category!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get priority {
    try {
      return _priority!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get description {
    try {
      return _description!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get mobile_no {
    try {
      return _mobile_no!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get attachments {
    try {
      return _attachments!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  bool get status {
    try {
      return _status!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get operator_name {
    try {
      return _operator_name!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String? get userId {
    return _userId;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const Issues._internal({required this.id, category, required sub_category, required priority, required description, required mobile_no, required attachments, required status, required operator_name, userId, createdAt, updatedAt}): _category = category, _sub_category = sub_category, _priority = priority, _description = description, _mobile_no = mobile_no, _attachments = attachments, _status = status, _operator_name = operator_name, _userId = userId, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory Issues({String? id, String? category, required String sub_category, required String priority, required String description, required String mobile_no, required String attachments, required bool status, required String operator_name, String? userId}) {
    return Issues._internal(
      id: id == null ? amplify_core.UUID.getUUID() : id,
      category: category,
      sub_category: sub_category,
      priority: priority,
      description: description,
      mobile_no: mobile_no,
      attachments: attachments,
      status: status,
      operator_name: operator_name,
      userId: userId);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Issues &&
      id == other.id &&
      _category == other._category &&
      _sub_category == other._sub_category &&
      _priority == other._priority &&
      _description == other._description &&
      _mobile_no == other._mobile_no &&
      _attachments == other._attachments &&
      _status == other._status &&
      _operator_name == other._operator_name &&
      _userId == other._userId;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("Issues {");
    buffer.write("id=" + "$id" + ", ");
    buffer.write("category=" + "$_category" + ", ");
    buffer.write("sub_category=" + "$_sub_category" + ", ");
    buffer.write("priority=" + "$_priority" + ", ");
    buffer.write("description=" + "$_description" + ", ");
    buffer.write("mobile_no=" + "$_mobile_no" + ", ");
    buffer.write("attachments=" + "$_attachments" + ", ");
    buffer.write("status=" + (_status != null ? _status!.toString() : "null") + ", ");
    buffer.write("operator_name=" + "$_operator_name" + ", ");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt!.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt!.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  Issues copyWith({String? category, String? sub_category, String? priority, String? description, String? mobile_no, String? attachments, bool? status, String? operator_name, String? userId}) {
    return Issues._internal(
      id: id,
      category: category ?? this.category,
      sub_category: sub_category ?? this.sub_category,
      priority: priority ?? this.priority,
      description: description ?? this.description,
      mobile_no: mobile_no ?? this.mobile_no,
      attachments: attachments ?? this.attachments,
      status: status ?? this.status,
      operator_name: operator_name ?? this.operator_name,
      userId: userId ?? this.userId);
  }
  
  Issues copyWithModelFieldValues({
    ModelFieldValue<String?>? category,
    ModelFieldValue<String>? sub_category,
    ModelFieldValue<String>? priority,
    ModelFieldValue<String>? description,
    ModelFieldValue<String>? mobile_no,
    ModelFieldValue<String>? attachments,
    ModelFieldValue<bool>? status,
    ModelFieldValue<String>? operator_name,
    ModelFieldValue<String?>? userId
  }) {
    return Issues._internal(
      id: id,
      category: category == null ? this.category : category.value,
      sub_category: sub_category == null ? this.sub_category : sub_category.value,
      priority: priority == null ? this.priority : priority.value,
      description: description == null ? this.description : description.value,
      mobile_no: mobile_no == null ? this.mobile_no : mobile_no.value,
      attachments: attachments == null ? this.attachments : attachments.value,
      status: status == null ? this.status : status.value,
      operator_name: operator_name == null ? this.operator_name : operator_name.value,
      userId: userId == null ? this.userId : userId.value
    );
  }
  
  Issues.fromJson(Map<String, dynamic> json)  
    : id = json['id'],
      _category = json['category'],
      _sub_category = json['sub_category'],
      _priority = json['priority'],
      _description = json['description'],
      _mobile_no = json['mobile_no'],
      _attachments = json['attachments'],
      _status = json['status'],
      _operator_name = json['operator_name'],
      _userId = json['userId'],
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'id': id, 'category': _category, 'sub_category': _sub_category, 'priority': _priority, 'description': _description, 'mobile_no': _mobile_no, 'attachments': _attachments, 'status': _status, 'operator_name': _operator_name, 'userId': _userId, 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'id': id,
    'category': _category,
    'sub_category': _sub_category,
    'priority': _priority,
    'description': _description,
    'mobile_no': _mobile_no,
    'attachments': _attachments,
    'status': _status,
    'operator_name': _operator_name,
    'userId': _userId,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<IssuesModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<IssuesModelIdentifier>();
  static final ID = amplify_core.QueryField(fieldName: "id");
  static final CATEGORY = amplify_core.QueryField(fieldName: "category");
  static final SUB_CATEGORY = amplify_core.QueryField(fieldName: "sub_category");
  static final PRIORITY = amplify_core.QueryField(fieldName: "priority");
  static final DESCRIPTION = amplify_core.QueryField(fieldName: "description");
  static final MOBILE_NO = amplify_core.QueryField(fieldName: "mobile_no");
  static final ATTACHMENTS = amplify_core.QueryField(fieldName: "attachments");
  static final STATUS = amplify_core.QueryField(fieldName: "status");
  static final OPERATOR_NAME = amplify_core.QueryField(fieldName: "operator_name");
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "Issues";
    modelSchemaDefinition.pluralName = "Issues";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.id());
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.CATEGORY,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.SUB_CATEGORY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.PRIORITY,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.DESCRIPTION,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.MOBILE_NO,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.ATTACHMENTS,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.STATUS,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.bool)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.OPERATOR_NAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: Issues.USERID,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _IssuesModelType extends amplify_core.ModelType<Issues> {
  const _IssuesModelType();
  
  @override
  Issues fromJson(Map<String, dynamic> jsonData) {
    return Issues.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'Issues';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [Issues] in your schema.
 */
class IssuesModelIdentifier implements amplify_core.ModelIdentifier<Issues> {
  final String id;

  /** Create an instance of IssuesModelIdentifier using [id] the primary key. */
  const IssuesModelIdentifier({
    required this.id});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'id': id
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'IssuesModelIdentifier(id: $id)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is IssuesModelIdentifier &&
      id == other.id;
  }
  
  @override
  int get hashCode =>
    id.hashCode;
}