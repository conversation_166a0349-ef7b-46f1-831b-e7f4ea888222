import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_charge/constants/const.dart';
import 'package:url_launcher/url_launcher_string.dart';

class TermsPage extends StatefulWidget {
  const TermsPage({super.key});

  @override
  State<TermsPage> createState() => _TermsPageState();
}

class _TermsPageState extends State<TermsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: false,
        title: const Text("Terms & Conditions"),
      ),
      body: Column(
        children: [
          const SizedBox(height: 8),
          // ListTile(
          //   title: const Text("Pricing Policy"),
          //   onTap: () => launchUrlString(PoliciesLink.pricing,
          //       mode: LaunchMode.externalApplication),
          //   trailing: const CupertinoListTileChevron(),
          // ),
          ListTile(
            title: const Text("Privacy Policy"),
            onTap: () => launchUrlString(PoliciesLink.privacy,
                mode: LaunchMode.externalApplication),
            trailing: const CupertinoListTileChevron(),
          ),
          ListTile(
            title: const Text("Cancellation Policy"),
            onTap: () => launchUrlString(PoliciesLink.cancellation,
                mode: LaunchMode.externalApplication),
            trailing: const CupertinoListTileChevron(),
          ),
          ListTile(
            title: const Text("Refund Policy"),
            onTap: () => launchUrlString(PoliciesLink.refund,
                mode: LaunchMode.externalApplication),
            trailing: const CupertinoListTileChevron(),
          ),
          ListTile(
            title: const Text("Terms & Conditions"),
            onTap: () => launchUrlString(PoliciesLink.tandc,
                mode: LaunchMode.externalApplication),
            trailing: const CupertinoListTileChevron(),
          ),
          ListTile(
            title: const Text("Contact Us"),
            onTap: () => launchUrlString(PoliciesLink.contactUs,
                mode: LaunchMode.externalApplication),
            trailing: const CupertinoListTileChevron(),
          ),
        ],
      ),
    );
  }
}
