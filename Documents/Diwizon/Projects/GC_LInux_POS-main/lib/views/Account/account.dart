import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/controllers/auth_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/theme/theme.dart';
import 'package:go_charge/views/Account/acc_tile.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../shared/methods.dart';
import '../../shared/snackbar.dart';
import '../../utils/payment_gateway.dart';
import '../other/loader_page.dart';
import '../other/login_button.dart';
import '../wallet/wallet.dart';

class AccountWid extends StatefulWidget {
  const AccountWid({super.key});

  @override
  State<AccountWid> createState() => _AccountWidState();
}

class _AccountWidState extends State<AccountWid> {
  bool loading = false;
  bool popedUp = false;

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.paddingOf(context).top;
    return FutureBuilder<bool?>(
        future: Get.find<AuthCtrl>().isLoggedIn(),
        builder: (context, snapshot) {
          // print(snapshot.hasError);
          if (snapshot.data == true) {
            return GetBuilder<AppCtrl>(builder: (_) {
              if (_.currentUserData?.user_fullname.isEmpty ??
                  true && !popedUp) {
                WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                  popedUp = true;
                  // onEditName(context); // Uncomment to show name popup if name is empty.
                });
              }
              // debugPrint('${_.currentUserData?.user_fullname?.isEmpty ?? "Your Name"}');
              return Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(height: topPadding + 130),
                        AccountTile(
                          title:
                              '₹${_.currentUserData?.balance?.toStringAsFixed(1) ?? 0}',
                          subTitle: "Wallet Balance",
                          imageName: "wallet",
                          onTap: () {},
                          trailing: loading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator.adaptive(
                                    strokeWidth: 1.2,
                                  ))
                              : _addMoneyButton(context),
                        ),
                        Divider(color: Colors.grey.shade200, height: 0),
                        AccountTile(
                            title: "Charging",
                            subTitle: "Charging History",
                            imageName: "hostory",
                            onTap: () => AppRoutes.goToChargingHistory(context),
                            trailing: const CupertinoListTileChevron()),
                        Divider(color: Colors.grey.shade200, height: 0),
                        AccountTile(
                            title: "All transactions",
                            subTitle: "Transaction History",
                            imageName: "transactions",
                            onTap: () => AppRoutes.goToAllTransactions(context),
                            trailing: const CupertinoListTileChevron()),
                        Divider(color: Colors.grey.shade200, height: 0),
                        AccountTile(
                            title: "Complaints",
                            subTitle: "",
                            imageName: "complaint",
                            onTap: () => AppRoutes.goToComplaints(context),
                            trailing: const CupertinoListTileChevron()),
                        Divider(color: Colors.grey.shade200, height: 0),
                        AccountTile(
                            title: "Terms & Conditions",
                            subTitle: "",
                            imageName: "tandc",
                            onTap: () => AppRoutes.goToTermsPage(context),
                            trailing: const CupertinoListTileChevron()),
                        Divider(color: Colors.grey.shade200, height: 0),
                        AccountTile(
                            title: "Logout",
                            subTitle: "",
                            imageName: "logout",
                            onTap: () => {/* onLogout(context) */},
                            trailing: const SizedBox()),

                        // Divider(),
                        Divider(color: Colors.grey.shade200, height: 0),
                        const SizedBox(height: 8),
                        TextButton(
                            style: TextButton.styleFrom(
                                foregroundColor: Colors.grey.shade700),
                            onPressed: () => {/* onDeleteMyAccount(context) */},
                            child: const Text("Delete My Account")),
                        const SizedBox(height: 20)
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                        top: topPadding + 30, bottom: 20, right: 20, left: 20),
                    decoration:
                        const BoxDecoration(color: Colors.white, boxShadow: [
                      BoxShadow(blurRadius: 6, color: Colors.black12),
                    ]),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () async {
                            try {
                              PackageInfo packageInfo =
                                  await PackageInfo.fromPlatform();
                              String appName = packageInfo.appName;
                              String packageName = packageInfo.packageName;
                              String version = packageInfo.version;
                              String buildNumber = packageInfo.buildNumber;
                              showAppShackBar(
                                  "$appName - version: $version - build no: $buildNumber");
                            } catch (e) {
                              debugPrint(e.toString());
                            }
                          },
                          child: CircleAvatar(
                            radius: 36,
                            child: Text(
                              '${(_.currentUserData?.user_fullname.isEmpty ?? true) ? "Your Name" : _.currentUserData?.user_fullname}'[
                                      0]
                                  .toString()
                                  .toUpperCase(),
                              style: const TextStyle(fontSize: 30),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '${(_.currentUserData?.user_fullname.isEmpty ?? true) ? "Your Name" : _.currentUserData?.user_fullname}',
                                  style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                InkWell(
                                  // onTap: () => onEditName(context),
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 2.0, top: 1, bottom: 2),
                                    child: Icon(
                                      Icons.edit,
                                      color: Colors.grey.shade700,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(_.currentUserData?.contact ?? "-"),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                  color: appColorLite,
                                  borderRadius: BorderRadius.circular(45)),
                              child: const Row(
                                children: [
                                  Text(
                                    "Account Details",
                                    style: TextStyle(fontSize: 10),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_rounded,
                                    size: 8,
                                  )
                                ],
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              );
            });
          } else if (snapshot.data == false) {
            return LoginButton(refresh: () => setState(() {}));
          }
          return const LoaderPage();
        });
  }

  InkWell _addMoneyButton(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(24),
      onTap: () async {
        try {
          final amountCtrl = TextEditingController();
          final amount = await showDialog<num>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text("Wallet Recharge"),
              content: TextField(
                controller: amountCtrl,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                    labelText: "Enter Amount",
                    hintText: "₹",
                    alignLabelWithHint: true),
              ),
              actions: [
                TextButton(
                    onPressed: () => Navigator.of(context)
                        .pop(num.tryParse(amountCtrl.text)),
                    child: const Text("Confirm"))
              ],
            ),
          );
          if (amount != null) {
            if (mounted) setState(() => loading = true);
            // GET USER DATA
            final userDataRequest = ModelQueries.get(
                EndUser.classType,
                EndUserModelIdentifier(
                    id: Get.find<AppCtrl>().currentUserData?.id ?? ""));
            final userDataResponse =
                await Amplify.API.mutate(request: userDataRequest).response;
            final pgTransRef = getRandomId(34).toUpperCase();
            final transDoc = Transaction(
              currentBalance: userDataResponse.data?.balance,
              pgTransRef: pgTransRef,
              walletAmountUsed: null,
              bookingId: "",
              transRef: "",
              status: TransactionStatus.pending,
              amount: (amount).toDouble(),
              dateTime: TemporalDateTime.now(),
              reason: "Wallet",
              method: "",
              uId: Get.find<AppCtrl>().currentAuthUser?.userId,
              userName: Get.find<AppCtrl>().currentUserData?.user_fullname,
              userContact: Get.find<AppCtrl>().currentUserData?.contact,
            );
            final request = ModelMutations.create(transDoc);
            final transResponse =
                await Amplify.API.mutate(request: request).response;
            if (transResponse.data != null) {
              PaymentGateway.initPhonePePayment(
                amount: amount * 100,
                receipt: pgTransRef,
                isWalletTopup: true,
                handlePaymentErrorResponse: () {
                  showAppShackBar("Payment Failed!");
                  if (mounted) setState(() => loading = false);
                },
                handlePaymentSuccessResponse: () {
                  if (mounted) setState(() => loading = false);
                  showAppShackBar("Payment was successfull!");
                },
                mobileNumber:
                    Get.find<AppCtrl>().currentUserData?.contact ?? "",
              );
              /*  final resp = await APIManager.createPGOrder(
                amount: amount * 100,
                receipt: transResponse.data!.id,
                bId: transResponse.data!.id,
                uId: Get.find<AppCtrl>().currentAuthUser!.userId,
                type: true ? "Wallet" : "Booking",
              );
              // return;
              if (resp != null) {
                RazorPayPG.initRazorPayment(
                    pgOrder: resp,
                    isWalletTopup: true,
                    handlePaymentErrorResponse: (d) {
                      showAppShackBar("Payment Failed!");
                      if (mounted) setState(() => loading = false);
                    },
                    handlePaymentSuccessResponse: (d) {
                      if (mounted) setState(() => loading = false);
                      showAppShackBar("Payment was successfull!");
                    },
                    handleExternalWalletSelected: (d) {
                      showAppShackBar("External Wallet Selected");
                      if (mounted) setState(() => loading = false);
                    });
              } */
            }
          }
        } catch (e) {
          debugPrint(e.toString());
          if (mounted) setState(() => loading = false);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
            color: appColorLite, borderRadius: BorderRadius.circular(24)),
        child: const Row(
          children: [
            Icon(
              CupertinoIcons.add,
              color: appColor,
              size: 17,
            ),
            SizedBox(width: 4),
            Text(
              "Add Money",
              style: TextStyle(color: appColor),
            )
          ],
        ),
      ),
    );
  }
/* 
  onLogout(BuildContext context) async {
    try {
      final res = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog.adaptive(
                title: const Text("Confirm"),
                content: const Text("Are you sure want to logout?"),
                actions: [
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text("No")),
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text("Yes")),
                ],
              ));
      if (res == true) {
        final result = await Amplify.Auth.signOut();
        if (result is CognitoCompleteSignOut) {
          safePrint('Sign out completed successfully');
          removeLocalUserId();
          setState(() {});
          if (context.mounted) AppRoutes.popAll(context);
        } else if (result is CognitoFailedSignOut) {
          safePrint('Error signing user out: ${result.exception.message}');
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  } */
/* 
  onDeleteMyAccount(BuildContext context) async {
    try {
      final res = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog.adaptive(
                title: const Text("Confirm"),
                content: const Text("Are you sure want to delete account?"),
                actions: [
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text("No")),
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text("Yes")),
                ],
              ));
      if (res == true) {
        await Amplify.Auth.deleteUser();
        final response = await Amplify.API
            .mutate(
                request: ModelMutations.deleteById(
                    EndUser.classType,
                    EndUserModelIdentifier(
                        id: Get.find<AppCtrl>().currentAuthUser?.userId ?? "")))
            .response;
        if (response.data != null) {
          final result = await Amplify.Auth.signOut();
          if (result is CognitoCompleteSignOut) {
            safePrint('Sign out completed successfully');
            removeLocalUserId();
            if (context.mounted) AppRoutes.popAll(context);
          } else if (result is CognitoFailedSignOut) {
            safePrint('Error signing user out: ${result.exception.message}');
          }
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }
 */ /* 
  onEditName(BuildContext context) async {
    try {
      final nameCtrl = TextEditingController();
      final res = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
                title: const Text("Update Username"),
                content: TextField(
                    controller: nameCtrl,
                    decoration: const InputDecoration(hintText: "Enter Name"),
                    // textCapitalization: TextCapitalization.characters,
                    inputFormatters: [
                      UpperCaseTextFormatter(),
                    ]),
                // content: const Text("Are you sure want to logout?"),
                actions: [
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text("Cancel")),
                  TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text("Confirm")),
                ],
              ));
      if (res == true) {
        final request = ModelMutations.update(Get.find<AppCtrl>()
            .currentUserData!
            .copyWith(user_fullname: nameCtrl.text.toUpperCase()));
        final result = await Amplify.API.query(request: request).response;
        if (result is CognitoCompleteSignOut) {
          safePrint('Sign out completed successfully');
          if (context.mounted) AppRoutes.popAll(context);
        } else if (result is CognitoFailedSignOut) {
          safePrint('Error signing user out: ${result.errors}');
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  } */
}
