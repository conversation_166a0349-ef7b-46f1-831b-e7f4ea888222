import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/Transaction.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;
import 'package:go_charge/views/wallet/wallet.dart';
import '../../constants/const.dart';

class Alltransactions extends StatefulWidget {
  const Alltransactions({super.key});

  @override
  State<Alltransactions> createState() => _AlltransactionsState();
}

class _AlltransactionsState extends State<Alltransactions> {
  List<Transaction>? transactions;
  @override
  void initState() {
    super.initState();
    getTransaction();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Transaction History"),
      ),
      body: transactions == null
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : ListView.builder(
              itemCount: transactions?.length ?? 0,
              itemBuilder: (context, index) {
                final trans = transactions?[index];
                return TransactionTile(trans: trans);
                /* return ListTile(
                  leading: CircleAvatar(
                    backgroundColor:
                        (trans?.reason?.contains("Wallet") ?? false)
                            ? appColorLite
                            : Colors.grey.shade200,
                    child: Icon(
                      (trans?.reason?.contains("Wallet") ?? false)
                          ? CupertinoIcons.arrow_down_right
                          : CupertinoIcons.arrow_up_left,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  title: Text(trans?.reason ?? "-"),
                  subtitle: Text(
                      '${trans?.dateTime?.getDateTimeInUtc().toLocal().goodDayDate() ?? "-"} ${trans?.dateTime?.getDateTimeInUtc().toLocal().goodTime() ?? "-"}'),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        (trans?.walletAmountUsed ?? -0) > 0
                            ? '₹${trans?.walletAmountUsed?.toStringAsFixed(1)} & '
                                '₹${((trans?.amount ?? 0) / 100).toStringAsFixed(1)}'
                            : '₹${((trans?.amount ?? 0) / 100).toStringAsFixed(1)}',
                        style: TextStyle(
                            fontSize: 14,
                            color: trans?.reason == "Wallet"
                                ? appColor
                                : Colors.grey.shade800,
                            fontWeight: FontWeight.bold),
                      ),
                      Text(
                        trans?.method ?? "-",
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ); */
              },
            ),
    );
  }

  void getTransaction() async {
    try {
      safePrint("Fethcing Global Vehicles...");
      final request = ModelQueries.list(Transaction.classType,
          limit: unlimitedLimit,
          where:
              Transaction.UID.eq(Get.find<AppCtrl>().currentAuthUser?.userId));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Transaction: ${result.data?.items.length}');
      transactions = result.data?.items.whereType<Transaction>().toList() ?? [];
      transactions?.sort(
        (a, b) =>
            b.createdAt?.compareTo(a.createdAt ?? TemporalDateTime.now()) ?? 0,
      );
      if (mounted) setState(() {});
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying Transaction: ${e.message}');
      return null;
    } catch (e) {
      safePrint('Something went wrong querying Transaction: $e');
      return null;
    }
  }
}
