import 'package:flutter/material.dart';

class AccountTile extends StatelessWidget {
  const AccountTile(
      {super.key,
      required this.title,
      required this.subTitle,
      required this.imageName,
      required this.onTap,
      required this.trailing});
  final String title;
  final String subTitle;
  final String imageName;
  final Function onTap;
  final Widget trailing;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 14),
        child: Row(
          children: [
            CircleAvatar(
                radius: 26,
                backgroundColor: Colors.grey.shade200,
                child: Image.asset(
                  'assets/$imageName.png',
                  height: 18,
                  color: Colors.grey,
                )),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 18),
                ),
                if (subTitle.isNotEmpty)
                  Text(
                    subTitle,
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
            const Spacer(),
            trailing
          ],
        ),
      ),
    );
  }
}
