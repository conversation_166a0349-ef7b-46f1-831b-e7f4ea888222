import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/shared/loader.dart';
import '../../constants/const.dart';
import '../../controllers/auth_ctrl.dart';
import '../../theme/theme.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({super.key, this.popTillName});

  @override
  State<OtpPage> createState() => _OtpPageState();
  final String? popTillName;
}

bool isFinished = false;

class _OtpPageState extends State<OtpPage> {
  Duration _duration = const Duration(seconds: 4);
  Timer? _timer;
  int _countdownValue = 5;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_duration.inSeconds <= 0) {
        _timer?.cancel();
        isFinished = true;
        if (mounted) setState(() {});
      } else {
        if (mounted) {
          setState(() {
            _countdownValue = _duration.inSeconds;
            _duration = _duration - const Duration(seconds: 1);
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      body: Stack(
        fit: StackFit.expand,
        alignment: Alignment.topCenter,
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: Image.asset("assets/Frame.png"),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GetBuilder<AuthCtrl>(builder: (_) {
                return Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(40.r),
                          topRight: Radius.circular(40.r))),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // const Spacer(),
                      SizedBox(height: 28.h),
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: IconButton(
                          onPressed: () {
                            // AppRoutes.goToLoginPage(context);
                            Navigator.pop(context);
                          },
                          icon: const Icon(Icons.arrow_back),
                        ),
                      ),
                      // const Spacer(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 10.h),
                            Text(
                              "OTP code Verification",
                              style: TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 30.h),
                            ),
                            SizedBox(height: 10.h),
                            Text.rich(TextSpan(children: [
                              const TextSpan(
                                  text:
                                      "We have sent an OTP code to phone number "),
                              if (_.phoneCtrl.text.length > 2)
                                TextSpan(
                                    text:
                                        "***** ***${_.phoneCtrl.text.substring(_.phoneCtrl.text.length - 2, _.phoneCtrl.text.length)}."),
                              const TextSpan(
                                  text:
                                      "please share otp code below to continue"),
                            ])),
                            // const Spacer(),
                            SizedBox(height: 28.h),
                            TextField(
                              controller: _.otpCtrl,
                              keyboardType:
                                  const TextInputType.numberWithOptions(),
                              decoration: InputDecoration(
                                  hintText: "Enter your 6 digit otp",
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10.r)))),
                            ),
                            // const Spacer(),
                            SizedBox(height: 20.h),
                            isFinished
                                ? const SizedBox()
                                : Text.rich(TextSpan(children: [
                                    TextSpan(
                                      text: "You can resend code in ",
                                      style: TextStyle(
                                          color: const Color(0xff828282),
                                          fontSize: 16.h,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    TextSpan(
                                      text: "$_countdownValue",
                                      style: TextStyle(
                                          color: Colors.green,
                                          fontSize: 16.h,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    TextSpan(
                                      text: " s",
                                      style: TextStyle(
                                          color: const Color(0xff828282),
                                          fontSize: 16.h,
                                          fontWeight: FontWeight.w600),
                                    )
                                  ])),
                            SizedBox(height: 8.h),
                            Row(
                              children: [
                                Expanded(
                                    child: ElevatedButton(
                                        style: ButtonStyle(
                                            shape: WidgetStateProperty.all(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5.r))),
                                            backgroundColor:
                                                const WidgetStatePropertyAll(
                                                    appColor)),
                                        onPressed:
                                            () {} /*  _.confirmSignIn(
                                            context,
                                            popTillName: widget.popTillName), */
                                        ,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 12.0.h),
                                          child: Obx(() => _.verifying.value
                                              ? const AppLoader(white: true)
                                              : Text(
                                                  "Verify",
                                                  style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 18.h,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                )),
                                        ))),
                              ],
                            ),
                            // const Spacer(flex: 6)
                            SizedBox(height: 80.h),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          )
        ],
      ),
    );
  }
}
