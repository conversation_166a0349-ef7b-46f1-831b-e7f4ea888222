import 'package:flutter/material.dart';

class SignUpOtpDialog extends StatelessWidget {
  const SignUpOtpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final otpCtrl = TextEditingController();
    return AlertDialog(
      title: const Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("SignUp Verification"),
          SizedBox(height: 6),
          Text(
            "Please, verify otp for registeration",
            style: TextStyle(fontSize: 14, letterSpacing: 1.2),
          ),
        ],
      ),
      content: TextField(
        controller: otpCtrl,
        autofocus: true,
        keyboardType: const TextInputType.numberWithOptions(),
      ),
      actions: [
        ElevatedButton(
            onPressed: () => otpCtrl.text.isNotEmpty
                ? Navigator.pop(context, otpCtrl.text)
                : null,
            child: const Text("Confirm"))
      ],
    );
  }
}
