import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_charge/controllers/auth_ctrl.dart';
import '../../constants/const.dart';
import '../../shared/loader.dart';
import '../../theme/theme.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key, this.popTillName});
  final String? popTillName;
  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthCtrl>(builder: (_) {
      return Scaffold(
        backgroundColor: bgColor,
        body: Stack(
          fit: StackFit.expand,
          alignment: Alignment.topCenter,
          children: [
            Align(
                alignment: Alignment.topCenter,
                child: Padding(
                  padding: const EdgeInsets.only(top: 20.0),
                  child: Image.asset("assets/Frame.png"),
                )),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(40.r),
                          topRight: Radius.circular(40.r))),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // const Spacer(flex: 2),
                        SizedBox(height: 28.h),
                        Text(
                          "Log in or Sign Up with GoCharge",
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 30.h),
                        ),
                        // const Spacer(flex: 2),
                        SizedBox(height: 32.h),
                        Text(
                          "Mobile Number",
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 18.h),
                        ),
                        SizedBox(height: 5.h),
                        TextField(
                            controller: _.phoneCtrl,
                            keyboardType: TextInputType.phone,
                            decoration: _decor().copyWith(prefixText: "+91 ")),
                        /*   PhoneFieldHint(
                            controller: _.phoneCtrl, decoration: _decor()), */
                        // const Spacer(flex: 1),
                        SizedBox(height: 32.h),
                        Row(
                          children: [
                            Expanded(
                                child: ElevatedButton(
                                    style: ButtonStyle(
                                        shape: WidgetStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        5.r))),
                                        backgroundColor:
                                            const WidgetStatePropertyAll(
                                                appColor)),
                                    onPressed: () {
                                      // if (_.phoneNo() == testingNo) {
                                      //   _.getTestPassword(context);
                                      // } else {
                                      //   _.signIn(context,
                                      //       popTillName: widget.popTillName);
                                      // }
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 12.0.h),
                                      child: Obx(() => _.loding.value
                                          ? const AppLoader(white: true)
                                          : Text(
                                              "Continue",
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 18.h,
                                                  fontWeight: FontWeight.w600),
                                            )),
                                    ))),
                          ],
                        ),
                        // const Spacer(flex: 5)
                        SizedBox(height: 100.h),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  InputDecoration _decor() {
    return InputDecoration(
        hintText: "Enter your mobile number",
        border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10.r))));
  }
}
