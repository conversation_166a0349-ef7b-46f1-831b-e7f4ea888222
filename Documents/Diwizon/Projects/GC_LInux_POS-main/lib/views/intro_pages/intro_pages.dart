import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/auth_ctrl.dart';
import '../../configuration/configs.dart';
import '../../constants/const.dart';
import '../../theme/theme.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

PageController controller = PageController();
int _curr = 0;
List<Widget> _list = <Widget>[
  const PageIntroWid(
    image: "assets/OBJECTS.png",
    tittle: "Welcome to GoCharge!",
    subtittle: "Easily find EV charging stations around you.",
    num: 1,
  ),
  const PageIntroWid(
    image: "assets/object2.png",
    tittle: "Fast Simple to Make Reservation & Check in",
    subtittle: "Make Reservation for easy and simple vehicle charging.",
    num: 2,
  ),
  const PageIntroWid(
    image: "assets/object3.png",
    tittle: "WMake Payment Safely & Quickly with GoCharge",
    subtittle: "Make Reservation for easy and simple vehicle charging. ",
    num: 3,
  ),
];

class _IntroScreenState extends State<IntroScreen> {
  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.paddingOf(context);
    return Scaffold(
        backgroundColor: bgColor,
        body: Padding(
          padding: EdgeInsets.only(bottom: padding.bottom + 12),
          child: SizedBox(
            height: double.infinity.h,
            width: double.infinity.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 25.0.h, top: padding.top),
                      child: ElevatedButton(
                          style: ButtonStyle(
                              elevation: WidgetStateProperty.all(0),
                              backgroundColor:
                                  WidgetStateProperty.all(Colors.white)),
                          onPressed: () {
                            firstTime = false;
                            Get.find<AuthCtrl>().update();
                          },
                          child: Text(
                            "Skip",
                            style: TextStyle(
                                color: const Color(0xff828282),
                                fontWeight: FontWeight.w400,
                                fontSize: 16.sp),
                          )),
                    ),
                  ],
                ),
                const Spacer(),
                Image.asset(
                  "assets/Logo.png",
                  width: 210.w,
                ),
                const Spacer(),
                SizedBox(
                  height: 540.h,
                  child: PageView(
                    scrollDirection: Axis.horizontal,

                    // reverse: true,
                    // physics: BouncingScrollPhysics(),
                    controller: controller,
                    onPageChanged: (numb) {
                      if (mounted) {
                        setState(() {
                          _curr = numb;
                        });
                      }
                    },
                    children: _list,
                  ),
                ),
                const Spacer(),
                _indicator(_curr),
                SizedBox(height: 10.h),
                _button(_curr),
              ],
            ),
          ),
        ));
  }

  Row _button(ind) {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 25.0.h),
            child: ElevatedButton(
                onPressed: () {
                  if (ind == 2) {
                    firstTime = false;
                    Get.find<AuthCtrl>().update();
                    return;
                  }
                  controller.animateToPage(ind + 1,
                      duration: Durations.medium1, curve: Curves.linear);
                },
                style: ButtonStyle(
                    padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(vertical: 20.h)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r))),
                    backgroundColor: WidgetStateProperty.all(appColor)),
                child: Text(
                  ind == 2 ? "Get Started" : "Next",
                  style: TextStyle(
                      color: const Color(0XFFFFFFFF),
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: "Poppins"),
                )),
          ),
        ),
      ],
    );
  }

  Row _indicator(num no) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 4.h,
          width: 10.w,
          decoration: BoxDecoration(
              color: const Color(0xff4F4F4F),
              borderRadius: BorderRadius.circular(5.r)),
        ),
        SizedBox(
          width: 2.w,
        ),
        Container(
          height: 4.h,
          width: 10.w,
          decoration: BoxDecoration(
              color: no == 1
                  ? const Color(0xff4F4F4F)
                  : no == 2
                      ? const Color(0xff4F4F4F)
                      : const Color(0xffBDBDBD),
              borderRadius: BorderRadius.circular(5.r)),
        ),
        const SizedBox(
          width: 2,
        ),
        Container(
          height: 4.h,
          width: 10.w,
          decoration: BoxDecoration(
              color:
                  no == 2 ? const Color(0xff4F4F4F) : const Color(0xffBDBDBD),
              borderRadius: BorderRadius.circular(5.r)),
        )
      ],
    );
  }
}

class Pages extends StatelessWidget {
  final String? text;
  const Pages({super.key, this.text});
  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
          Text(
            text ?? "",
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold),
          ),
        ]));
  }
}

class PageIntroWid extends StatelessWidget {
  const PageIntroWid({
    super.key,
    required this.image,
    required this.tittle,
    required this.subtittle,
    required this.num,
  });
  final int num;
  final String image;
  final String tittle;
  final String subtittle;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 350.h, width: 350.w, child: Image.asset(image)),
        if (num == 1) SizedBox(height: 50.h),
        Text(
          tittle,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: const Color(0xff314A3C),
              fontSize: 34.h,
              fontWeight: FontWeight.w500),
        ),
        SizedBox(
          height: 20.h,
        ),
        Container(
          constraints: BoxConstraints(maxWidth: 300.w),
          child: Text(
            subtittle,
            style: TextStyle(
                color: const Color(0xff404040),
                fontWeight: FontWeight.w400,
                fontSize: 18.h),
            textAlign: TextAlign.center,
          ),
        ),
        // Spacer(flex: 2),
      ],
    );
  }
}
