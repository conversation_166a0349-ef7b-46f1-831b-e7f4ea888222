import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/configuration/configs.dart';
import 'package:go_charge/views/base/app_frame.dart';
import 'package:go_charge/views/intro_pages/intro_pages.dart';
import '../../controllers/auth_ctrl.dart';

class BaseWid extends StatelessWidget {
  const BaseWid({super.key});

  @override
  Widget build(BuildContext context) {
    contextOfHome = context;
    return GetBuilder<AuthCtrl>(builder: (_) {
      if (firstTime == true) return const IntroScreen();
      return const AppFrame();
/*       return FutureBuilder(
        future: _.isLoggedIn(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const LoaderPage();
          }
          return snapshot.data == true ? const AppFrame() : const LoginPage();
        },
      ); */
    });
  }
}

// 22.31113079818919, 73.17977035689493
// Vadodara Railway station 120KW