import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/views/wallet/wallet.dart';
import '../../controllers/app_ctrl.dart';
import '../../theme/theme.dart';
import '../Account/account.dart';
import '../Home/home_page.dart';
import '../Schedules/schedule_page.dart';
import '../Vehicle/vehicle_page.dart';

class AppFrame extends StatefulWidget {
  const AppFrame({super.key});

  @override
  State<AppFrame> createState() => _AppFrameState();
}

class _AppFrameState extends State<AppFrame> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    Get.put(AppCtrl());
    // initialChecks();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: [
        const HomePage(),
        const WalletPage(),
        const VehicleWid(),
        const ScheduleWid(),
        const AccountWid(),
      ][currentIndex],
      bottomNavigationBar: BottomNavigationBar(
          backgroundColor: Colors.white,
          currentIndex: currentIndex,
          unselectedItemColor: Colors.black,
          selectedItemColor: appColor,
          showSelectedLabels: true,
          showUnselectedLabels: true,
          unselectedIconTheme: const IconThemeData(color: Color(0xff828282)),
          onTap: (value) {
            currentIndex = value;
            if (mounted) setState(() {});
          },
          items: const [
            // BottomNavigationBarItem(
            //   label: "Go Charge",
            //   icon: Image.asset(
            //     'assets/gc_marker.png',
            //     width: 30.w,
            //   ),
            // ),
            BottomNavigationBarItem(
              label: "Search",
              icon: Icon(
                CupertinoIcons.search,
              ),
            ),
            BottomNavigationBarItem(
              label: "Wallet",
              icon: Icon(
                CupertinoIcons.creditcard,
              ),
            ),
            BottomNavigationBarItem(
              label: "Vehicles",
              icon: Icon(
                CupertinoIcons.bolt_horizontal_circle,
              ),
            ),
            BottomNavigationBarItem(
              label: "Schedules",
              icon: Icon(
                CupertinoIcons.list_bullet_below_rectangle,
              ),
            ),
            BottomNavigationBarItem(
              label: "More",
              icon: Icon(
                CupertinoIcons.ellipsis_circle,
              ),
            ),
          ]),
    );
  }
}
