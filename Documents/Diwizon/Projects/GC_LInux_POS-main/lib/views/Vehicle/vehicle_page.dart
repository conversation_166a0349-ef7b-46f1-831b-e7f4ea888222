import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/vehicles_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/views/Vehicle/my_vehicle_tile.dart';
import '../../controllers/auth_ctrl.dart';
import '../../routers/routers.dart';
import '../../theme/theme.dart';
import '../other/loader_page.dart';
import '../other/login_button.dart';

class VehicleWid extends StatefulWidget {
  const VehicleWid({super.key});

  @override
  State<VehicleWid> createState() => _VehicleWidState();
}

class _VehicleWidState extends State<VehicleWid> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool?>(
        future: Get.find<AuthCtrl>().isLoggedIn(),
        builder: (context, snapshot) {
          if (snapshot.data == true) {
            return Column(
              children: [
                AppBar(
                    backgroundColor: const Color(0xffE1FFE4),
                    title: Text(
                      "My Vehicle",
                      style: TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 18.h),
                    )),
                Expanded(
                  child: SingleChildScrollView(
                    child: GetBuilder<VehiclesCtrl>(builder: (_) {
                      // print(_.)
                      return Padding(
                        padding: EdgeInsets.all(20.0.h),
                        child: Column(
                          children: [
                            OutlinedButton(
                                style: ButtonStyle(
                                    shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.r),
                                        side: const BorderSide(color: appColor),
                                      ),
                                    ),
                                    side: const WidgetStatePropertyAll(
                                        BorderSide(color: appColor))),
                                onPressed: () async =>
                                    AppRoutes.goToAddNewVehicle(context).then(
                                        (selected) => selected != null &&
                                                context.mounted
                                            ? takeGaadiNo(context, selected)
                                                .then((value) => value != null
                                                    ? Get.find<VehiclesCtrl>()
                                                        .addToMyVehicles(
                                                            selected, value)
                                                    : null)
                                            : null),
                                child: Padding(
                                  padding: EdgeInsets.all(15.0.h),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.add,
                                        color: appColor,
                                        size: 20.h,
                                      ),
                                      Text(
                                        " Add  New Vehicle",
                                        style: TextStyle(
                                            color: appColor,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 16.h),
                                      )
                                    ],
                                  ),
                                )),
                            SizedBox(height: 8.h),
                            ...List.generate(
                                _.myVehicles.length,
                                (index) => MyVehicleTile(
                                      myVehicle: _.myVehicles[index],
                                    ))
                          ],
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          } else if (snapshot.data == false) {
            return LoginButton(refresh: () => setState(() {}));
          }
          return const LoaderPage();
        });
  }
}

Future<String?> takeGaadiNo(BuildContext context, Vehicle vehicle) async {
  final vNoCtrl = TextEditingController();
  return await showDialog<String?>(
      context: context,
      builder: (context) => AlertDialog(
            title: const Text("Vehicle Details"),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: vNoCtrl,
                  autofocus: true,
                  decoration: const InputDecoration(
                    labelText: "Vehicle No*",
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Colors.red),
                  )),
              ElevatedButton(
                  onPressed: () => Navigator.pop(context, vNoCtrl.text),
                  child: const Text(
                    'Confirm',
                    style: TextStyle(),
                  )),
            ],
          ));
}


/* 
import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-**************************";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();


/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENTTTTTT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let chargingBody;
  let response;
  let data;
  let MeterValue;
  let document_Id;
  let userId;
  let userBalance;
  let booking_id;
  let amountToRefund;
  let refundedToWallet;
  let bodyObject;

  try {
    await new Promise(resolve => setTimeout(resolve, 2000));
    bodyObject = JSON.parse(event.body);
    console.log("Body", bodyObject);
    userId = bodyObject.userId;
    booking_id = bodyObject.bookingId;
    // Define parameters for DynamoDB get operation
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': booking_id
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    data = await dynamodb.scan(params).promise();
    console.log(data);
    if (data.Items.length > 0) {
      body = data.Items[0];
      chargingBody = data.Items[0];
      document_Id = body.id;
      console.log("Force: ", bodyObject.force);
      console.log("body.refundedAmount != null", body.refundedAmount != null);
      if (body.status === "Canceled") {
        console.log('Returning !! Already canceled!');
        return { status: "Cannot cancel after charge command!" };
      }
      if (bodyObject.force != true) {
        // if (body.status !== "Inactive") {
        if (body.status !== "Inactive" || body.transactionId || body.lastCommand === "StartTransaction") {
          console.log('Returning !! Cannot cancel now ', body.status !== "Inactive", body.transactionId == true, body.lastCommand === "StartTransaction");
          return { status: "Cannot cancel after charge command!" };
        }
      }
      if (body.amountFromWallet != 0 && body.amountFromWallet != null)
        amountToRefund = body.charging_fee - (body.costOfConsump ?? 0);
    } else {
      return { status: "Charging not found!" };
    }

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  console.log("amountToRefund", amountToRefund);
  // REFUND TO USER
  if (amountToRefund != null) {
    console.log("In refund to wallet....");

    try {
      // Extract the ID from the event object
      // Define parameters for DynamoDB get operation
      const params = {
        TableName: "EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod",
        FilterExpression: '#id = :id',
        ExpressionAttributeNames: {
          '#id': 'id'
        },
        ExpressionAttributeValues: {
          ':id': userId
        }
        /*      FilterExpression: "#uId = :userId",
             ExpressionAttributeNames: {
               "#uId": "uId"
             },
             ExpressionAttributeValues: {
               ":userId": uId
             } */
      };

      console.log("USER ID is", userId);

      // Call DynamoDB get operation to read data for the specified ID
      const userData = await dynamodb.scan(params).promise();
      console.log(userData);
      if (userData.Items.length > 0) {
        body = userData.Items[0];
        console.log("OLD Balance is ", body.balance);
        userBalance = body.balance;
        const newBalance = body.balance + amountToRefund;
        try {
          const variables = { id: body.id, newBalance: newBalance };
          console.log(variables);

          const query = /* GraphQL */ `
          mutation MyMutation($id: ID!,$newBalance: Float!) {
            updateEndUser(
              input: {id: $id,balance: $newBalance}
            ) {
              id
              user_fullname
              dob
              joining_date
              email
              contact
              balance
              default_vehicle_id
              favs
              uId
              deviceId
              createdAt
              updatedAt
            }
          }`;

          /** @type {import('node-fetch').RequestInit} */
          const transacOptions = {
            method: 'POST',
            headers: {
              'x-api-key': GRAPHQL_API_KEY,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, variables })
          };

          const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

          console.log("Running Upate end user Mutation...");
          const response2 = await fetch(transacReq);
          console.log(response2);
          const body2 = await response2.json();
          if (body2.errors) statusCode = 400;
          console.log(body2);
          refundedToWallet = true;
        } catch (error) {
          console.log("Inside Create Transaction Mutation Catch");
          statusCode = 400;
          const bodyy = {
            errors: [
              {
                status: response.status,
                message: error.message,
                stack: error.stack
              }
            ]
          };
          console.error(bodyy);
        }
      }

    } catch (error) {
      // Return error response if an error occurs
      console.error('Error in updateUserBalance():', error);
      statusCode = 400;
      body = {
        errors: [
          {
            message: error.message,
            stack: error.stack
          }
        ]
      };
    }
  } else {
    console.log("Need to refund in bank!!");
    /** @type {import('node-fetch').RequestInit} */
    const options = {
      method: 'POST',
      body: JSON.stringify({
        amount: chargingBody.charging_fee - (chargingBody.costOfConsump ?? 0),
        transRef: chargingBody.pgTransRef,
        pg: bodyObject.pg ?? "PhonePe",
      })
    };

    const requestRefund = new Request("https://442aqpovec6i4iikhpxtkrfrsa0jlcry.lambda-url.ap-south-1.on.aws/", options);

    try {
      console.log("Calling Refund Order...")
      response = await fetch(requestRefund);
      console.log(response);
      body = await response.json();
      if (body.errors) statusCode = 400;
      console.log(body);
    } catch (error) {
      statusCode = 400;
      body = {
        errors: [
          {
            status: response.status,
            message: error.message,
            stack: error.stack
          }
        ]
      };
      console.error(body);
    }
  }
  const originalCost = (chargingBody.costOfConsump ?? 0) *
    100 /
    (100 + (chargingBody.taxPercent ?? 0));

  console.log("TRANS....", userId);
  const variables = {
    id: document_Id, refundedAmount: amountToRefund,
    status: (chargingBody.costOfConsump ?? 0) > 0 ? 'Completed' : 'Canceled',
    baseAmount: originalCost
  };
  if ((chargingBody.costOfConsump ?? 0) > 0) {
    const invoiceDetails = await getSetInvoiceNo(chargerId);
    variables.invoiceNo = invoiceDetails['no'];
    variables.invoiceId = invoiceDetails['id'];
  }

  //Add Values from DB and Event here TODO by Arbaaz
  const query = /* GraphQL */ `
  mutation MyMutation($id: ID!, $refundedAmount: Float, $status: String, $baseAmount: Float) {
    updateChargingTable(
      input: {id: $id, refundedAmount: $refundedAmount,status: $status, baseAmount: $baseAmount}
    ) {
      createdAt
      updatedAt
      id
booking_id
start_time
end_time
status
connector_no
CurrentMeterWatt
city
charging_fee
payment_status
createdAt
tax_amount
vehical_number
chargePointId
user_id
station_id
vehicle_id
charging_percent
MeterStartWatt
MeterEndWatt
booking_type
compareValue
pricePerKw
geoState
isPaid
chargerId
transactionId
estimatedDuration
estimatedUnits
startedAtPercent
stopedAtPercent
unitsBurned
costOfConsump
refundedAmount
startedAtTime
stopedAtTime
amountFromWallet
transDocId
payment_Id
paymentTime
lastCommand
gstin
gstName
userName
userContact
overchargeDueCleared
invoiceNo
dueAmount
stationName
cpoName
taxPercent
isIgst
igstAmount
sgstAmount
cgstAmount
invoiceId
rfid
pgTransRef
baseAmount
    }
    }`;

  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  // UPDATE BOOKING
  let body3;
  try {
    console.log("Running Mutation...")
    response = await fetch(request);
    console.log(response);
    body3 = await response.json();
    if (body3.errors) statusCode = 400;
    console.log(body3);
  } catch (error) {
    statusCode = 400;
    body3 = {
      errors: [
        {
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.error(body3);
  }


  if (refundedToWallet == true) {
    await createRefundTransaction(userId, chargingBody.booking_id, amountToRefund, body.user_fullname, body.contact, userBalance);
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};

async function createRefundTransaction(userId, booking_id, amountToRefund, uName, contact, userBalance) {
  let statusCode = 200;
  let response;
  try {
    let variables = {
      uId: userId, method: "Refund", dateTime: new Date(), bookingId: booking_id, amount: amountToRefund,
      status: "Successful", reason: "Wallet", userName: uName, userContact: contact, currentBalance: userBalance
    };

    console.log("In create transaction....", userId, variables);

    const query = /* GraphQL */ `
      mutation MyMutation($uId: String!,$method: String, $dateTime: AWSDateTime, $bookingId: String, $amount: Float, $currentBalance: Float, $status: String, $reason: String, $userName: String, $userContact: String) {
        createTransaction(
          input: {uId: $uId, method: $method, dateTime: $dateTime, bookingId: $bookingId, amount: $amount,currentBalance: $currentBalance, status: $status, reason :$reason , userName :$userName, userContact :$userContact }
        ) {
          createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
        }
      }
      `;

    /** @type {import('node-fetch').RequestInit} */
    const transacOptions = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables })
    };

    const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

    console.log("Running create transaction Mutation...");
    response = await fetch(transacReq);
    console.log(response);
    const body = await response.json();
    // if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    console.log("Inside create Transaction Mutation Catch");
    console.error(error);
  }
}


async function getSetInvoiceNo(station_id, cpoId) {
  try {

    let cpoDocId = cpoId;
    if (cpoDocId == null) {
      console.log("cpoDocId == null");
      const params = {
        TableName: "Station-r6cw5zqo7zb37hhq7w4ympiugy-prod",
        FilterExpression: '#id = :id',
        ExpressionAttributeNames: {
          '#id': 'id'
        },
        ExpressionAttributeValues: {
          ':id': station_id
        }
      };

      // Call DynamoDB get operation to read data for the specified ID
      const data = await dynamodb.scan(params).promise();
      console.log(data);

      if (data.Items.length > 0) {
        const stationData = data.Items[0];
        console.log(stationData);
        //
        cpoDocId = stationData.cpoId;
      }
    }
    console.log("cpoDocId: ", cpoDocId);
    if (cpoDocId == null) {
      return null;
    }
    const params2 = {
      TableName: "CPO-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': cpoDocId
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    console.log("...1");
    const data2 = await dynamodb.scan(params2).promise();
    console.log("...2");
    console.log(data2);
    if (data2.Items.length > 0) {
      console.log("...3");
      const cpoData = data2.Items[0];
      console.log(cpoData);
      //
      let oldInvoiceNo = cpoData.invoiceNo;
      oldInvoiceNo ??= 0;
      let newInvoiceNo = oldInvoiceNo + 1;
      console.log(newInvoiceNo);
      console.log("...4");
      //
      const finDateUpdated = cpoData.currentFinYear != null ? checkCurrentFinYearDate(cpoData.currentFinYear) : true;
      console.log("finDateUpdated", finDateUpdated);
      //
      newInvoiceNo = finDateUpdated ? newInvoiceNo : 1;

      const variables = {
        id: cpoData.id, invoiceNo: newInvoiceNo, currentFinYear: finDateUpdated ? (cpoData.currentFinYear == null ? getCurrentFinancialYearStartDate() : cpoData.currentFinYear) : getCurrentFinancialYearStartDate()
      };
      console.log(variables);

      console.log("...5");
      const query = /* GraphQL */ `
            mutation MyMutation2($id: ID!, $invoiceNo: Int, $currentFinYear: AWSDateTime) {
              updateCharger(
                input: {id: $id, invoiceNo: $invoiceNo, currentFinYear: $currentFinYear}
              ) {
                id
                chargingPointId
                last_heart_beat
                service_date
                status
                pricePerKW
                manufactId
                capacityId
                stationId
                stationName
                simNo
                simCompany
                serialNo
                faulted
                invoicePrefix
                invoiceNo
                currentFinYear
                createdAt
                updatedAt
              }
            }`;
      console.log("...6");

      const cpoOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const cpoReq = new Request(GRAPHQL_ENDPOINT, cpoOptions);
      console.log("...7");

      console.log("Running Update Mutation...");
      const response2 = await fetch(cpoReq);
      console.log("...8");
      console.log(response2);
      const body2 = await response2.json();
      if (body2.errors) statusCode = 400;
      console.log(body2);
      return { "no": newInvoiceNo, "id": generateInvoiceIdString(cpoData.invoicePrefix, newInvoiceNo, 6) };
    }

  } catch (error) {
    console.log("Inside getSetInvoiceNo Update Mutation Catch");
    console.error(error);
  }
}


// async function getSetInvoiceNo(chargerId) {
//   try {

//     const params2 = {
//       TableName: "Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod",
//       FilterExpression: '#id = :id',
//       ExpressionAttributeNames: {
//         '#id': 'id'
//       },
//       ExpressionAttributeValues: {
//         ':id': chargerId
//       }
//     };

//     // Call DynamoDB get operation to read data for the specified ID
//     console.log("...1");
//     const data2 = await dynamodb.scan(params2).promise();
//     console.log("...2");
//     console.log(data2);
//     if (data2.Items.length > 0) {
//       console.log("...3");
//       const chargerData = data2.Items[0];
//       console.log(chargerData);
//       //
//       let oldInvoiceNo = chargerData.invoiceNo;
//       oldInvoiceNo ??= 0;
//       let newInvoiceNo = oldInvoiceNo + 1;
//       console.log(newInvoiceNo);
//       console.log("...4");
//       //
//       const finDateUpdated = chargerData.currentFinYear != null ? checkCurrentFinYearDate(chargerData.currentFinYear) : true;
//       console.log("finDateUpdated", finDateUpdated);
//       //
//       newInvoiceNo = finDateUpdated ? newInvoiceNo : 1;

//       const variables = {
//         id: chargerData.id, invoiceNo: newInvoiceNo, currentFinYear: finDateUpdated ? (chargerData.currentFinYear == null ? getCurrentFinancialYearStartDate() : chargerData.currentFinYear) : getCurrentFinancialYearStartDate()
//       };
//       console.log(variables);

//       console.log("...5");
//       const query = /* GraphQL */ `
//           mutation MyMutation2($id: ID!, $invoiceNo: Int, $currentFinYear: AWSDateTime) {
//             updateCharger(
//               input: {id: $id, invoiceNo: $invoiceNo, currentFinYear: $currentFinYear}
//             ) {
//               id
//               chargingPointId
//               last_heart_beat
//               service_date
//               status
//               pricePerKW
//               manufactId
//               capacityId
//               stationId
//               stationName
//               simNo
//               simCompany
//               serialNo
//               faulted
//               invoicePrefix
//               invoiceNo
//               currentFinYear
//               createdAt
//               updatedAt
//             }
//           }`;
//       console.log("...6");

//       const cpoOptions = {
//         method: 'POST',
//         headers: {
//           'x-api-key': GRAPHQL_API_KEY,
//           'Content-Type': 'application/json'
//         },
//         body: JSON.stringify({ query, variables })
//       };

//       const cpoReq = new Request(GRAPHQL_ENDPOINT, cpoOptions);
//       console.log("...7");

//       console.log("Running Update Mutation...");
//       const response2 = await fetch(cpoReq);
//       console.log("...8");
//       console.log(response2);
//       const body2 = await response2.json();
//       if (body2.errors) statusCode = 400;
//       console.log(body2);
//       return { "no": newInvoiceNo, "id": generateInvoiceIdString(chargerData.invoicePrefix, newInvoiceNo, 6) };
//     }
//   } catch (error) {
//     console.log("Inside getSetInvoiceNo Update Mutation Catch");
//     console.error(error);
//   }
// }

function generateInvoiceIdString(prefix, number, totalLength) {
  // Convert the number to a string and pad with leading zeros
  const paddedNumber = number.toString().padStart(totalLength - prefix.length, '0');
  // Combine the prefix and padded number
  return `${prefix}${paddedNumber}`;
}

function checkCurrentFinYearDate(currentFinYear) {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  // Determine the financial year start date
  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  // Convert currentFinYear to a Date object if it's not already one
  const currentFinYearDate = new Date(currentFinYear);

  // Check if currentFinYearDate is the same as finYearStart
  const finDateUpdated = currentFinYearDate.getTime() === finYearStart.getTime();

  return finDateUpdated;
}


function getCurrentFinancialYearStartDate() {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  return finYearStart;
}
 */