import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/models/DueRequest.dart';
import 'package:go_charge/models/ModelProvider.dart';
import '../../controllers/vehicles_ctrl.dart';
import '../../theme/theme.dart';

class AddNewVehiclePage extends StatefulWidget {
  const AddNewVehiclePage({super.key});

  @override
  State<AddNewVehiclePage> createState() => _AddNewVehiclePageState();
}

class _AddNewVehiclePageState extends State<AddNewVehiclePage> {
  Vehicle? seltedVehicle;
  final searchCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(
          "Add Vehicle",
          style: TextStyle(
              color: const Color(0xff333333),
              fontSize: 24.h,
              fontWeight: FontWeight.w500),
        ),
      ),
      backgroundColor: Colors.white,
      body:
          // final manufacturers = _.globalVehicles.map((e) => e.m);
          Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20.h),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: CupertinoSearchTextField(
                controller: searchCtrl,
                onChanged: (value) => Get.find<VehiclesCtrl>().update(),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(7)),
                    border: Border.fromBorderSide(BorderSide(
                        color: Color(
                      0xffE0E0E0,
                    )))),
                padding: EdgeInsets.symmetric(vertical: 12.h),
                placeholder: "   Search Vehicle"),
          ),
          SizedBox(height: 30.h),
          GetBuilder<VehiclesCtrl>(initState: (state) {
            Get.find<VehiclesCtrl>().getAllVehiclesData();
          }, builder: (_) {
            final list = _.manufacturers
                .where((element) =>
                    (_.globalVehicles
                            .where((ele) => ele.manufactId == element.id)
                            .toList()
                            .firstWhereOrNull((e) => e.model
                                .toLowerCase()
                                .contains(searchCtrl.text.toLowerCase())) !=
                        null) ||
                    element.manufacturer_name
                        .toLowerCase()
                        .contains(searchCtrl.text.toLowerCase()))
                .toList();
            return Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 10.h),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0),
                      child: Text(
                        "Manufacturer",
                        style: TextStyle(
                            color: const Color(0xff333333),
                            fontSize: 16.h,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                    SizedBox(height: 10.h),
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: list.length,
                      physics: const ClampingScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemBuilder: (context, index) {
                        final vehiclesOfMannu = _.globalVehicles
                            .where((element) =>
                                element.manufactId == list[index].id)
                            .toList();
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: ExpansionTile(
                            title: Text(
                              list[index].manufacturer_name,
                              style: TextStyle(
                                  color: const Color(0xff4F4F4F),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 18.h),
                            ),
                            collapsedShape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8)),
                                side: BorderSide(
                                    width: .7, color: Color(0xffBDBDBD))),
                            shape: RoundedRectangleBorder(
                                side: BorderSide(
                                    color: const Color(0xffBDBDBD), width: 1.h),
                                borderRadius: BorderRadius.circular(8)),
                            childrenPadding: EdgeInsets.zero,
                            children: <Widget>[
                              ...List.generate(
                                vehiclesOfMannu.length,
                                (idx) => listTata(vehiclesOfMannu[idx]),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.fromLTRB(20.0, 8, 20, 15),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                  style: ButtonStyle(
                      shape: WidgetStateProperty.all(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5))),
                      backgroundColor: const WidgetStatePropertyAll(appColor)),
                  onPressed: () {
                    Navigator.pop(context, seltedVehicle);
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.0.h),
                    child: Text(
                      "Save Vehicle",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.h,
                          fontWeight: FontWeight.w600),
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  Widget listTata(Vehicle vehicle) {
    return InkWell(
      onTap: () => mounted ? setState(() => seltedVehicle = vehicle) : null,
      child: Row(
        children: [
          Radio(
            value: vehicle,
            groupValue: seltedVehicle,
            onChanged: (value) {
              if (mounted) {
                setState(() {
                  seltedVehicle = value!;
                });
              }
            },
          ),
          Text(
            vehicle.model,
            style: TextStyle(
                color: const Color(0xff828282),
                fontWeight: FontWeight.w400,
                fontSize: 16.h),
          ),
        ],
      ),
    );
  }
}
