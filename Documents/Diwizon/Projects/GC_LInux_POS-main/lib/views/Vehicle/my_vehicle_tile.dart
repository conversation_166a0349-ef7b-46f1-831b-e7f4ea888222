import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/controllers/vehicles_ctrl.dart';
import '../../models/UserVehicle.dart';

class MyVehicleTile extends StatelessWidget {
  const MyVehicleTile(
      {super.key, required this.myVehicle, this.callback, this.groupValue});

  final UserVehicle? groupValue;
  final UserVehicle myVehicle;
  final Function(UserVehicle)? callback;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(5.r)),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0.h),
        child: InkWell(
          onTap: callback != null ? () => callback!(myVehicle) : _onTap,
          onLongPress: () => _onLongPress(context),
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.r),
                border: Border.all(color: const Color(0xff828282))),
            child: Padding(
              padding: EdgeInsets.all(10.0.h),
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        myVehicle.vehical_name,
                        style: TextStyle(
                            color: const Color.fromARGB(255, 44, 44, 44),
                            fontWeight: FontWeight.bold,
                            fontSize: 16.h),
                      ),
                      Text(
                        myVehicle.vehical_number,
                        style: TextStyle(
                            color: const Color(0xff828282),
                            fontWeight: FontWeight.w400,
                            fontSize: 16.h),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Radio(
                    value: myVehicle.id,
                    groupValue: callback != null && groupValue == null
                        ? null
                        : (groupValue?.id ??
                            Get.find<AppCtrl>()
                                .currentUserData
                                ?.default_vehicle_id),
                    onChanged: (value) => _onTap(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onTap() {
    callback != null
        ? callback!(myVehicle)
        : Get.find<VehiclesCtrl>().setDefaultVehicle(myVehicle);
  }

  void _onLongPress(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog.adaptive(
          title: const Text("Confirm"),
          content: const Text("Are you sure want to delete is vehicle?"),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text("Cancel")),
            TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Get.find<VehiclesCtrl>().deleteFromMyVehicles(myVehicle);
                },
                child: const Text(
                  "Delete",
                  style: TextStyle(color: Colors.red),
                )),
          ],
        );
      },
    );
  }
}
