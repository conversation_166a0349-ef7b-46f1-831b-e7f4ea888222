import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/theme.dart';

class AddVehicle extends StatelessWidget {
  const AddVehicle({super.key});

  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.paddingOf(context);
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.0.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: padding.top),
            Text(
              "Add your Vehicle ",
              style: TextStyle(fontSize: 30.h, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 10.h),
            Text(
              "Personalise your Experience by adding a Vehicle",
              style: TextStyle(
                  fontSize: 16.h,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xff828282)),
            ),
            const Spacer(),
            Center(child: Image.asset("assets/Group.png")),
            const Spacer(),
            <PERSON>(
              children: [
                Expanded(
                  child: ElevatedButton(
                      style: ButtonStyle(
                          shape: WidgetStateProperty.all(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5.r))),
                          backgroundColor:
                              const WidgetStatePropertyAll(appColor)),
                      onPressed: () async {
                        // Vehicle? selectedVehicle =
                        //     await Navigator.push<Vehicle?>(
                        //         context,
                        //         MaterialPageRoute(
                        //             builder: (context) =>
                        //                 const AddNewVehiclePage()));
                        // print(selectedVehicle);
                        // if (selectedVehicle != null) {
                        //   print(selectedVehicle.company);
                        // }
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0.h),
                        child: Text(
                          "Add Vehicle",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.h,
                              fontWeight: FontWeight.w600),
                        ),
                      )),
                ),
              ],
            ),
            SizedBox(height: 10.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                      style: ButtonStyle(
                          side: const WidgetStatePropertyAll(BorderSide(
                              color: Color.fromARGB(255, 213, 207, 207))),
                          shape: WidgetStateProperty.all(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5.r))),
                          backgroundColor:
                              const WidgetStatePropertyAll(Colors.white)),
                      onPressed: () {},
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0.h),
                        child: Text(
                          "Later",
                          style: TextStyle(
                              color: const Color(0xff828282),
                              fontSize: 18.h,
                              fontWeight: FontWeight.w600),
                        ),
                      )),
                ),
              ],
            ),
            SizedBox(height: padding.bottom),
          ],
        ),
      ),
    );
  }
}
