import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/model/pg_order.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/snackbar.dart';
import '../../shared/methods.dart';
import '../other/qr_button.dart';
import 'station_sheet.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  PgOrder? tmpOrder;

  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.paddingOf(context);
    return GetBuilder<AppCtrl>(builder: (_) {
      return const StationsListSheet();
    });
  }
}
