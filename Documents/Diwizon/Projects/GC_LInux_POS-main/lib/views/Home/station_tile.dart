import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/models/Station.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/views/station/detail_page.dart';
import '../../shared/methods.dart';
import '../../theme/theme.dart';

class StationTile extends StatelessWidget {
  const StationTile({super.key, required this.station});

  final Station station;

  @override
  Widget build(BuildContext context) {
    final status = (station.last_heart_beat
                ?.getDateTimeInUtc()
                .toLocal()
                .isBefore(
                    DateTime.now().subtract(const Duration(minutes: 5))) ??
            true)
        ? ChargerStatus.notAvailable
        : (station.status ?? "");
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.0.h),
      child: InkWell(
        child: Container(
          height: 120,
          decoration: BoxDecoration(
              border: Border.all(color: const Color(0xffE0E0E0)),
              borderRadius: BorderRadius.circular(8.r)),
          child: Padding(
            padding: EdgeInsets.all(8.0.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.power_settings_new,
                      color: getStatusColor(status),
                      size: 20.h,
                    ),
                    const SizedBox(width: 3),
                    Material(
                      surfaceTintColor: Colors.white,
                      color: Colors.white,
                      child: Text(
                        status,
                        style: TextStyle(
                            color: getStatusColor(status),
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    const Spacer(),
                    Image.asset("assets/s.png"),
                    const Text(
                      "2",
                      style: TextStyle(color: appColor),
                    )
                  ],
                ),
                Material(
                  surfaceTintColor: Colors.white,
                  color: Colors.white,
                  child: Text(
                    station.station_name,
                    style:
                        TextStyle(fontSize: 16.h, fontWeight: FontWeight.w600),
                  ),
                ),
                const Spacer(),
                const Row(
                  children: [
                    Material(
                      surfaceTintColor: Colors.white,
                      color: Colors.white,
                      child: Text(
                        "Distance",
                        style: TextStyle(color: Color(0xff828282)),
                      ),
                    ),
                    Spacer(),
                    Text(
                      "CCS/SAE Type 2",
                      style: TextStyle(color: Color(0xff828282)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        onTap: () {
          if (status != ChargerStatus.available) return;
          AppRoutes.goToStationDetails(context, station);
        },
      ),
    );
  }
}
