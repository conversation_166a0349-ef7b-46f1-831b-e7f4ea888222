// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:go_charge/views/Home/detail_page_chargeby.dart';
// import 'package:go_charge/views/Home/detail_page_connector.dart';

// class DetailPageVehicle extends StatefulWidget {
//   const DetailPageVehicle({super.key});

//   @override
//   State<DetailPageVehicle> createState() => _DetailPageVehicleState();
// }

// var isLike = false;
// List<String> tata = <String>["Ace EV 2023", "Tata EV 2023"];

// String selCarType = "Ace EV 2023";

// class _DetailPageVehicleState extends State<DetailPageVehicle> {
//   var buttonStyle = const ButtonStyle(
//       backgroundColor: MaterialStatePropertyAll(Colors.white));
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Stack(
//         children: [
//           const Placeholder(),
//           DraggableScrollableSheet(
//             maxChildSize: .85.h,
//             minChildSize: .3.h,
//             initialChildSize: .5.h,
//             builder: (context, scrollController) {
//               return SingleChildScrollView(
//                 physics: const ClampingScrollPhysics(),
//                 controller: scrollController,
//                 child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(20.r),
//                         topRight: Radius.circular(20.r)),
//                     color: Colors.white,
//                   ),
//                   child: Padding(
//                     padding:
//                         EdgeInsets.only(left: 20.h, right: 20.h, bottom: 20.h),
//                     child: Column(
//                       children: [
//                         Padding(
//                           padding: EdgeInsets.only(top: 10.0.h, bottom: 20.h),
//                           child: Container(
//                             height: 6.h,
//                             width: 80.h,
//                             decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(5.r),
//                                 color: const Color(0xffD9D9D9)),
//                           ),
//                         ),
//                         Padding(
//                           padding: EdgeInsets.symmetric(vertical: 10.0.h),
//                           child: Padding(
//                             padding: EdgeInsets.all(8.0.h),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Icon(
//                                       Icons.power_settings_new,
//                                       color: const appColor,
//                                       size: 20.h,
//                                     ),
//                                     SizedBox(width: 3.h),
//                                     const Text(
//                                       "Available",
//                                       style: TextStyle(
//                                           color: appColor,
//                                           fontWeight: FontWeight.w600),
//                                     ),
//                                     const Spacer(),
//                                     IconButton(
//                                         onPressed: () {
//                                           isLike = !isLike;
//                                           setState(() {});
//                                         },
//                                         icon: Icon(
//                                           isLike
//                                               ? CupertinoIcons.heart
//                                               : CupertinoIcons.heart_fill,
//                                           color: const appColor,
//                                         ))
//                                   ],
//                                 ),
//                                 Text(
//                                   "Vadodara Railway station 120KW",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 Text(
//                                   "DC Super charger",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 SizedBox(height: 10.h),
//                                 //const Spacer(),
//                                 const Text(
//                                   "Distance 500 meters",
//                                   style: TextStyle(color: Color(0xff828282)),
//                                 ),
//                                 SizedBox(height: 10.h),
//                                 SingleChildScrollView(
//                                   scrollDirection: Axis.horizontal,
//                                   child: Row(
//                                     children: [
//                                       OutlinedButton(
//                                           onPressed: () {},
//                                           style: buttonStyle.copyWith(
//                                               padding: MaterialStatePropertyAll(
//                                                   EdgeInsets.all(10.h))),
//                                           child: Row(
//                                             children: [
//                                               Icon(
//                                                 Icons.navigation,
//                                                 size: 20.h,
//                                                 color: const Color(0xff828282),
//                                               ),
//                                               SizedBox(width: 5.h),
//                                               Text(
//                                                 "Available",
//                                                 style: TextStyle(
//                                                     fontSize: 16.h,
//                                                     color: const Color(
//                                                         0xff828282)),
//                                               ),
//                                             ],
//                                           )),
//                                       SizedBox(width: 10.h),
//                                       OutlinedButton(
//                                           onPressed: () {},
//                                           style: buttonStyle.copyWith(
//                                               padding:
//                                                   const MaterialStatePropertyAll(
//                                                       EdgeInsets.all(10))),
//                                           child: Row(
//                                             children: [
//                                               Icon(
//                                                 Icons.call,
//                                                 size: 20.h,
//                                                 color: const Color(0xff828282),
//                                               ),
//                                               SizedBox(width: 5.h),
//                                               Text(
//                                                 "Call Operator",
//                                                 style: TextStyle(
//                                                     fontSize: 16.h,
//                                                     color: const Color(
//                                                         0xff828282)),
//                                               ),
//                                             ],
//                                           )),
//                                       SizedBox(width: 10.h),
//                                       OutlinedButton(
//                                           onPressed: () {},
//                                           style: buttonStyle.copyWith(
//                                               padding: MaterialStatePropertyAll(
//                                                   EdgeInsets.all(10.h))),
//                                           child: Row(
//                                             children: [
//                                               Icon(
//                                                 Icons.share,
//                                                 size: 20.h,
//                                                 color: const Color(0xff828282),
//                                               ),
//                                               SizedBox(width: 5.h),
//                                               Text(
//                                                 "Share",
//                                                 style: TextStyle(
//                                                     fontSize: 16.h,
//                                                     color: const Color(
//                                                         0xff828282)),
//                                               ),
//                                             ],
//                                           )),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 25.h),
//                                 Text(
//                                   "Selector",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 SizedBox(height: 25.h),
//                                 const Selector(x: 2),
//                                 const SizedBox(height: 10),
//                                 const Row(
//                                   children: [
//                                     Text("Connectors"),
//                                     Spacer(),
//                                     Text("Vehicle"),
//                                     Spacer(),
//                                     Text("Charge by")
//                                   ],
//                                 ),
//                                 SizedBox(height: 20.h),
//                                 OutlinedButton(
//                                     style: ButtonStyle(
//                                         shape: MaterialStatePropertyAll(
//                                           RoundedRectangleBorder(
//                                             borderRadius:
//                                                 BorderRadius.circular(5.r),
//                                             side: const BorderSide(
//                                                 color: appColor),
//                                           ),
//                                         ),
//                                         side: const MaterialStatePropertyAll(
//                                             BorderSide(
//                                                 color: appColor))),
//                                     onPressed: () {},
//                                     child: Padding(
//                                       padding: EdgeInsets.all(15.0.h),
//                                       child: Row(
//                                         mainAxisAlignment:
//                                             MainAxisAlignment.center,
//                                         children: [
//                                           Icon(
//                                             Icons.add,
//                                             color: const appColor,
//                                             size: 20.h,
//                                           ),
//                                           Text(
//                                             " Add Vehicle",
//                                             style: TextStyle(
//                                                 color: const appColor,
//                                                 
//                                                 fontWeight: FontWeight.w600,
//                                                 fontSize: 16.h),
//                                           )
//                                         ],
//                                       ),
//                                     )),
//                                 SizedBox(height: 8.h),
//                                 ...List.generate(
//                                     2,
//                                     (index) => Padding(
//                                           padding: EdgeInsets.symmetric(
//                                               vertical: 8.0.h),
//                                           child: InkWell(
//                                             child: Container(
//                                               decoration: BoxDecoration(
//                                                   borderRadius:
//                                                       BorderRadius.circular(
//                                                           5.r),
//                                                   border: Border.all(
//                                                       color: const Color(
//                                                           0xff828282))),
//                                               child: Padding(
//                                                 padding: EdgeInsets.all(10.0.h),
//                                                 child: Row(
//                                                   children: [
//                                                     Text(
//                                                       tata[index].toString(),
//                                                       style: TextStyle(
//                                                           color: const Color(
//                                                               0xff828282),
//                                                           
//                                                           fontWeight:
//                                                               FontWeight.w400,
//                                                           fontSize: 16.h),
//                                                     ),
//                                                     const Spacer(),
//                                                     Radio(
//                                                       value: tata[index],
//                                                       groupValue: selCarType,
//                                                       onChanged: (value) {
//                                                         setState(() {
//                                                           selCarType = value!;
//                                                         });
//                                                       },
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ),
//                                             ),
//                                             onTap: () {
//                                               setState(() {
//                                                 selCarType = tata[index];
//                                               });
//                                               Navigator.push(
//                                                   context,
//                                                   MaterialPageRoute(
//                                                       builder: (context) =>
//                                                           const DetailPageCharegby()));
//                                             },
//                                           ),
//                                         ))
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               );
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
