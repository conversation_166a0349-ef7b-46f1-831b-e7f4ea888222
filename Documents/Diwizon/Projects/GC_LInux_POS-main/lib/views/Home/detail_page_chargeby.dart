// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:go_charge/views/Home/detail_page_connector.dart';
// import 'package:go_charge/views/payment/pay.dart';
// import 'package:syncfusion_flutter_sliders/sliders.dart';

// class DetailPageCharegby extends StatefulWidget {
//   const DetailPageCharegby({super.key});

//   @override
//   State<DetailPageCharegby> createState() => _DetailPageCharegbyState();
// }

// var buttonStyle2 = const ButtonStyle(
//     backgroundColor: MaterialStatePropertyAll(appColor));
// bool isLike = false;
// var buttonStyle = const ButtonStyle(
//     backgroundColor:
//         MaterialStatePropertyAll(Color.fromARGB(255, 255, 255, 255)));
// double _currentSliderValue = 20;
// int dau = 0;
// double _value = 40.0;
// double duration = 0.0;
// int price = 0;
// TextEditingController amount = TextEditingController();

// final class _DetailPageCharegbyState extends State<DetailPageCharegby> {
//   var buttonStyle = const ButtonStyle(
//       backgroundColor: MaterialStatePropertyAll(Colors.white));
//   var textStyle = TextStyle(
//       fontSize: 14.h,
//       color: const Color(0xff4F4F4F),
//       fontWeight: FontWeight.w500);
//   var textStyle2 = TextStyle(
//       fontSize: 14.h, fontWeight: FontWeight.w600, color: Color(0xff333333));
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Stack(
//         children: [
//           const Placeholder(),
//           DraggableScrollableSheet(
//             maxChildSize: .85.h,
//             minChildSize: .3.h,
//             initialChildSize: .5.h,
//             builder: (context, scrollController) {
//               return SingleChildScrollView(
//                 physics: const ClampingScrollPhysics(),
//                 controller: scrollController,
//                 child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(20.r),
//                         topRight: Radius.circular(20.r)),
//                     color: Colors.white,
//                   ),
//                   child: Padding(
//                     padding:
//                         EdgeInsets.only(left: 20.h, right: 20.h, bottom: 20.h),
//                     child: Column(
//                       children: [
//                         Padding(
//                           padding: EdgeInsets.only(top: 10.0.h, bottom: 20.h),
//                           child: Container(
//                             height: 6.h,
//                             width: 80.h,
//                             decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(5.r),
//                                 color: const Color(0xffD9D9D9)),
//                           ),
//                         ),
//                         Padding(
//                           padding: EdgeInsets.symmetric(vertical: 10.0.h),
//                           child: Padding(
//                             padding: EdgeInsets.all(8.0.h),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Icon(
//                                       Icons.power_settings_new,
//                                       color: const appColor,
//                                       size: 20.h,
//                                     ),
//                                     SizedBox(width: 3.h),
//                                     const Text(
//                                       "Available",
//                                       style: TextStyle(
//                                           color: appColor,
//                                           fontWeight: FontWeight.w600),
//                                     ),
//                                     const Spacer(),
//                                     IconButton(
//                                         onPressed: () {
//                                           isLike = !isLike;
//                                           setState(() {});
//                                         },
//                                         icon: Icon(
//                                           isLike
//                                               ? CupertinoIcons.heart
//                                               : CupertinoIcons.heart_fill,
//                                           color: const appColor,
//                                         ))
//                                   ],
//                                 ),
//                                 Text(
//                                   "Vadodara Railway station 120KW",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 Text(
//                                   "DC Super charger",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 SizedBox(height: 10.h),
//                                 //const Spacer(),
//                                 const Text(
//                                   "Distance 500 meters",
//                                   style: TextStyle(color: Color(0xff828282)),
//                                 ),
//                                 SizedBox(height: 10.h),
//                                 SingleChildScrollView(
//                                   scrollDirection: Axis.horizontal,
//                                   child: Row(
//                                     children: [
//                                       OutButton(
//                                         buttonStyle: buttonStyle,
//                                         txt: 'Available',
//                                         onPressed: () {
//                                           Navigator.push(
//                                               context,
//                                               MaterialPageRoute(
//                                                   builder: (context) =>
//                                                       const Pay()));
//                                         },
//                                       ),
//                                       SizedBox(width: 10.h),
//                                       OutButton(
//                                         buttonStyle: buttonStyle,
//                                         txt: 'Call Operator',
//                                         onPressed: () {},
//                                       ),
//                                       SizedBox(width: 10.h),
//                                       OutButton(
//                                         buttonStyle: buttonStyle,
//                                         txt: 'Share',
//                                         onPressed: () {},
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 25.h),
//                                 Text(
//                                   "Selector",
//                                   style: TextStyle(
//                                       fontSize: 16.h,
//                                       fontWeight: FontWeight.w600),
//                                 ),
//                                 SizedBox(height: 25.h),
//                                 const Selector(x: 3),
//                                 SizedBox(height: 10.h),
//                                 const Row(
//                                   children: [
//                                     Text("Connectors"),
//                                     Spacer(),
//                                     Text("Vehicle"),
//                                     Spacer(),
//                                     Text("Charge by")
//                                   ],
//                                 ),
//                                 SizedBox(height: 30),
//                                 Row(
//                                   mainAxisAlignment:
//                                       MainAxisAlignment.spaceBetween,
//                                   children: [
//                                     OutButton(
//                                       buttonStyle: buttonStyle,
//                                       txt: 'Duration',
//                                       onPressed: () {
//                                         dau = 0;
//                                         setState(() {});
//                                       },
//                                     ),
//                                     //SizedBox(width: 10.h),
//                                     OutButton(
//                                       buttonStyle: buttonStyle,
//                                       txt: 'Amount',
//                                       onPressed: () {
//                                         dau = 1;
//                                         setState(() {});
//                                       },
//                                     ),
//                                     //SizedBox(width: 10.h),
//                                     OutButton(
//                                       buttonStyle: buttonStyle,
//                                       txt: 'Units',
//                                       onPressed: () {
//                                         dau = 2;
//                                         setState(() {});
//                                       },
//                                     ),
//                                   ],
//                                 ),
//                                 SizedBox(height: 20),
//                                 if (dau == 0)
//                                   Column(
//                                     children: [
//                                       Row(
//                                         children: [
//                                           Container(
//                                             height: 18,
//                                             width: 18,
//                                             decoration: BoxDecoration(
//                                                 border: Border.all(
//                                                     color: const Color(
//                                                         0xff33A63F)),
//                                                 borderRadius:
//                                                     BorderRadius.circular(15)),
//                                             child: Center(
//                                               child: Icon(
//                                                 Icons.circle,
//                                                 size: 15,
//                                                 color: const appColor,
//                                               ),
//                                             ),
//                                           ),
//                                           Expanded(
//                                             child: SfSlider(
//                                               tooltipShape:
//                                                   SfPaddleTooltipShape(),
//                                               min: 0.0,
//                                               max: 100.0,
//                                               edgeLabelPlacement:
//                                                   EdgeLabelPlacement.auto,
//                                               activeColor: appColor,
//                                               value: _value,
//                                               interval: 20,
//                                               enableTooltip: true,
//                                               minorTicksPerInterval: 0,
//                                               onChanged: (dynamic value) {
//                                                 setState(() {
//                                                   _value = value;
//                                                   duration = value;
//                                                 });
//                                               },
//                                             ),
//                                           ),
//                                           Container(
//                                             height: 18,
//                                             width: 18,
//                                             decoration: BoxDecoration(
//                                                 border: Border.all(
//                                                     color: const Color(
//                                                         0xff33A63F)),
//                                                 borderRadius:
//                                                     BorderRadius.circular(15)),
//                                             child: Center(
//                                               child: Icon(
//                                                 Icons.circle,
//                                                 size: 15,
//                                                 color: const appColor,
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                       SizedBox(height: 20),
//                                       Container(
//                                           decoration: BoxDecoration(
//                                               border: Border.all(
//                                                 color: Color.fromARGB(
//                                                     24, 52, 51, 51),
//                                               ),
//                                               borderRadius:
//                                                   BorderRadius.circular(5)),
//                                           child: Padding(
//                                             padding: const EdgeInsets.all(10.0),
//                                             child: Column(
//                                               children: [
//                                                 Row(
//                                                   children: [
//                                                     Text("Duration",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "${duration.toStringAsFixed(2)} min",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text(
//                                                         "Estimated Consumption (kwh)",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "6:11 PM",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text(
//                                                         "Estimated Price (Incl GST)",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "6:11 PM",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text("Estimated Km",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "6:11 PM",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ],
//                                             ),
//                                           )),
//                                     ],
//                                   ),
//                                 if (dau == 1)
//                                   Column(
//                                     children: [
//                                       Row(
//                                         children: [
//                                           Expanded(
//                                             child: TextField(
//                                               controller: amount,
//                                               decoration: InputDecoration(
//                                                   border: OutlineInputBorder(
//                                                       borderRadius:
//                                                           BorderRadius.circular(
//                                                               5))),
//                                               onSubmitted: (value) {
//                                                 // amount =value;
//                                                 setState(() {
//                                                   amount.text =
//                                                       value.toString();
//                                                   print(amount.text.toString());
//                                                 });
//                                               },
//                                             ),
//                                           )
//                                         ],
//                                       ),
//                                       SizedBox(height: 20),
//                                       Container(
//                                           decoration: BoxDecoration(
//                                               border: Border.all(
//                                                 color: Color.fromARGB(
//                                                     24, 52, 51, 51),
//                                               ),
//                                               borderRadius:
//                                                   BorderRadius.circular(5)),
//                                           child: Padding(
//                                             padding: const EdgeInsets.all(10.0),
//                                             child: Column(
//                                               children: [
//                                                 Row(
//                                                   children: [
//                                                     Text("Price (Incl GST)",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "₹ ${amount.text}",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text("Estimated Duration",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "${duration.toStringAsFixed(2)} min",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text(
//                                                         "Estimated Consumption (kwh)",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "6:11 PM",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 SizedBox(height: 20),
//                                                 Row(
//                                                   children: [
//                                                     Text("Estimated Km",
//                                                         style: textStyle),
//                                                     const Spacer(),
//                                                     Text(
//                                                       "6:11 PM",
//                                                       style: textStyle2,
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ],
//                                             ),
//                                           )),
//                                     ],
//                                   ),
//                                 // Column(
//                                 //   children: [
//                                 //     Slider(
//                                 //       value: _currentSliderValue,
//                                 //       min: 0,
//                                 //       max: 100,
//                                 //       onChanged: (double value) {
//                                 //         setState(() {
//                                 //           _currentSliderValue = value;
//                                 //         });
//                                 //       },
//                                 //       label: _currentSliderValue
//                                 //           .round()
//                                 //           .toString(),
//                                 //     ),
//                                 //     Row(
//                                 //       mainAxisAlignment:
//                                 //           MainAxisAlignment.spaceBetween,
//                                 //       children: [
//                                 //         Text("0"),
//                                 //         Text("100"),
//                                 //       ],
//                                 //     ),
//                                 //   ],
//                                 // ),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               );
//             },
//           ),
//         ],
//       ),
//       bottomNavigationBar: BottomAppBar(
//           color: Colors.white,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 15.0),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: OutlinedButton(
//                       onPressed: () {},
//                       style: buttonStyle.copyWith(
//                           shape: MaterialStatePropertyAll(
//                               RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(5.r)))),
//                       child: const Text(
//                         "Book Now",
//                         style: TextStyle(color: Colors.black),
//                       )),
//                 ),
//                 SizedBox(width: 10),
//                 Expanded(
//                   child: ElevatedButton(
//                       onPressed: () {},
//                       style: buttonStyle2.copyWith(
//                           shape: MaterialStatePropertyAll(
//                               RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(5.r)))),
//                       child: const Text(
//                         "Start Change",
//                         style: TextStyle(color: Colors.white),
//                       )),
//                 ),
//               ],
//             ),
//           )),
//     );
//   }
// }

// class OutButton extends StatelessWidget {
//   const OutButton({
//     super.key,
//     required this.buttonStyle,
//     required this.txt,
//     required this.onPressed,
//   });
//   final String txt;
//   final ButtonStyle buttonStyle;
//   final Function() onPressed;

//   @override
//   Widget build(BuildContext context) {
//     return OutlinedButton(
//         onPressed: onPressed,
//         style: buttonStyle.copyWith(
//             padding: MaterialStatePropertyAll(EdgeInsets.all(10.h))),
//         child: Row(
//           children: [
//             Icon(
//               Icons.share,
//               size: 20.h,
//               color: const Color(0xff828282),
//             ),
//             SizedBox(width: 5.h),
//             Text(
//               txt,
//               style: TextStyle(fontSize: 16.h, color: const Color(0xff828282)),
//             ),
//           ],
//         ));
//   }
// }
