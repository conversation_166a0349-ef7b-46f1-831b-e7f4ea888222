import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/shared/shimmer_tile.dart';
import '../../shared/methods.dart';
import '../../theme/theme.dart';
import '../other/loader_ripple.dart';
import '../other/sheet_handle.dart';
import 'station_tile.dart';

class StationsListSheet extends StatelessWidget {
  const StationsListSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      maxChildSize: .75,
      minChildSize: .3,
      initialChildSize: .4,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            color: Colors.white,
          ),
          child: GetBuilder<AppCtrl>(
            builder: (_) {
              final stationsList = _.stationListing == StationListing.all
                  ? _.stations
                  : _.stations
                      .where((element) =>
                          _.currentUserData?.favs?.contains(element.id) ??
                          false)
                      .toList();
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SheetHandle(),
                  SizedBox(height: 4.h),
                  const _StationFilterTabs(),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: _.chargerLoaded
                          ? stationsList.isEmpty
                              ? const LoaderRipple()
                              : ListView.builder(
                                  padding: EdgeInsets.zero,
                                  physics: const ClampingScrollPhysics(),
                                  controller: scrollController,
                                  itemCount: stationsList.length,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) =>
                                      StationTile(station: stationsList[index]),
                                )
                          : const ShimmerListBuilder(count: 3),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }
}

class _StationFilterTabs extends StatefulWidget {
  const _StationFilterTabs();

  @override
  State<_StationFilterTabs> createState() => _StationFilterTabsState();
}

class _StationFilterTabsState extends State<_StationFilterTabs> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      child: GetBuilder<AppCtrl>(builder: (_) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _FilterButton(
                name: "All",
                onTap: () => _.onStationListingChange(StationListing.all),
                selected: _.stationListing == StationListing.all),
            SizedBox(width: 12.h),
            // _FilterButton(name: "Available", onTap: () {}, selected: false),
            // SizedBox(width: 12.h),
            // _FilterButton(name: "Visited", onTap: () {}, selected: false),
            // SizedBox(width: 12.h),
            _FilterButton(
                name: "Favourite",
                onTap: () => _.onStationListingChange(StationListing.favourite),
                selected: _.stationListing == StationListing.favourite),
            SizedBox(width: 12.h),
          ],
        );
      }),
    );
  }

  final activeButtonStyle = ElevatedButton.styleFrom(
    padding: EdgeInsets.zero,
    backgroundColor: appColor,
    elevation: 0,
    foregroundColor: Colors.white,
    surfaceTintColor: Colors.transparent,
  );

  final inActiveButtonStyle = ElevatedButton.styleFrom(
      elevation: 0,
      padding: EdgeInsets.zero,
      foregroundColor: Colors.black,
      surfaceTintColor: Colors.transparent,
      side: const BorderSide(color: Colors.black26),
      backgroundColor: Colors.transparent);
}

class _FilterButton extends StatelessWidget {
  const _FilterButton({
    required this.name,
    required this.onTap,
    required this.selected,
  });

  final String name;
  final Function onTap;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () => onTap(),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
          decoration: BoxDecoration(
              color: selected ? appColor : Colors.transparent,
              borderRadius: BorderRadius.circular(24),
              border: selected ? null : Border.all(color: Colors.grey)),
          child: Text(
            name,
            style: TextStyle(
                color: selected ? Colors.white : Colors.grey.shade700),
          ),
        ));
  }
}
