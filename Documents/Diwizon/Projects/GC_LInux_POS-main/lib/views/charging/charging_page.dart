import 'dart:async';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:battery_indicator/battery_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/shared/snackbar.dart';
import 'package:go_charge/theme/theme.dart';
import 'package:go_charge/utils/api_manager.dart';
import 'package:go_charge/utils/extentions.dart';
import 'package:go_charge/views/charging/bottom_buttons.dart';
import '../../constants/const.dart';
import '../../controllers/booking_ctrl.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;

List cancelingQueue = [];

class ChargingPage extends StatefulWidget {
  const ChargingPage({super.key, required this.bookingDocId});
  final String bookingDocId;

  @override
  State<ChargingPage> createState() => _ChargingPageState();
}

class _ChargingPageState extends State<ChargingPage> {
  // StreamSubscription<QuerySnapshot<ChargingTable>>? _chargingStream;
  StreamSubscription<amplify.GraphQLResponse<ChargingTable>>? _chargingStream;
  ChargingTable? charging;
  Connector? connector;
  bool buttonTimeout = false;
  bool loading = false;

  @override
  void initState() {
    super.initState();
    getCurrentUserData();
  }

  @override
  void dispose() {
    super.dispose();
    _chargingStream?.cancel();
  }

  getCurrentUserData() async {
    try {
      try {
        final request = amplify.ModelQueries.get(
          ChargingTable.classType,
          ChargingTableModelIdentifier(id: widget.bookingDocId),
        );
        final response = await Amplify.API.query(request: request).response;
        charging = response.data;
        if (mounted) setState(() {});
        observeCharging();
      } on ApiException catch (e) {
        safePrint('Query failed: $e');
        return null;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void observeCharging() async {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        ChargingTable.classType,
        where: ChargingTable.ID.eq(widget.bookingDocId));
    // Stream
    final Stream<amplify.GraphQLResponse<ChargingTable>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    _chargingStream = operation.listen(
      (event) async {
        safePrint('Subscription event data receivedcharging: ${event.data}');
        if (event.data != null) {
          charging = event.data!;
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  @override
  Widget build(BuildContext context) {
    final station = Get.find<AppCtrl>()
        .stations
        .firstWhereOrNull((element) => element.id == charging?.station_id);
    final isLate = (charging?.start_time
                ?.getDateTimeInUtc()
                .toLocal()
                .add(const Duration(minutes: autoCancelDuration))
                .isBefore(DateTime.now()) ??
            true) &&
        (charging?.status == ChargingStatus.inActive);
    return Scaffold(
      body: charging == null
          ? const Center(child: CircularProgressIndicator())
          : CustomScrollView(
              slivers: <Widget>[
                SliverAppBar(
                  pinned: true,
                  snap: false,
                  floating: false,
                  expandedHeight: 240.0,
                  titleTextStyle: TextStyle(
                    fontSize: 20.sp,
                  ),
                  title: const Text('Charging'),
                  elevation: 12,
                  surfaceTintColor: Colors.white,
                  shadowColor: Colors.black54,
                  stretch: true,
                  stretchTriggerOffset: 10,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Image.asset(
                      'assets/object2.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: station != null
                                  ? Text(
                                      station.station_name,
                                      style: _stationTextStyle(),
                                    )
                                  : FutureBuilder<Station?>(
                                      future: Amplify.API
                                          .query(
                                              request: ModelQueries.get(
                                            Station.classType,
                                            StationModelIdentifier(
                                                id: charging?.station_id ?? ""),
                                          ))
                                          .response
                                          .then((value) => value.data),
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          return Text(
                                            snapshot.data?.station_name ??
                                                "GO Charge Charging Station",
                                            style: _stationTextStyle(),
                                          );
                                        }
                                        return const SizedBox();
                                      },
                                    ),
                            ),
                            if (charging?.charging_percent != null)
                              Row(
                                children: [
                                  if (charging?.status == ChargingStatus.active)
                                    SizedBox(
                                      height: 45,
                                      width: 20,
                                      child: FittedBox(
                                        fit: BoxFit.fitHeight,
                                        child: Image.asset(
                                          'assets/charging2.gif',
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  BatteryIndicator(
                                    batteryFromPhone: false,
                                    batteryLevel: int.tryParse(
                                            charging?.charging_percent ?? "") ??
                                        0,
                                    colorful: true,
                                    size: 20,
                                    mainColor: Colors.black,
                                    showPercentNum: true,
                                    showPercentSlide: true,
                                    percentNumSize: 12,
                                  ),
                                ],
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        if (charging?.status == ChargingStatus.canceled &&
                            charging?.amountFromWallet == 0 &&
                            charging?.refundedAmount == null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 5),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.red.withOpacity(.1)),
                            child: Row(
                              children: [
                                Icon(CupertinoIcons.info,
                                    color: Colors.red.shade600),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                      "It may take up to 5-7 business days for the refund to appear in your account.",
                                      style: TextStyle(
                                          color: Colors.red.shade600)),
                                ),
                              ],
                            ),
                          ),
                        const SizedBox(height: 8),
                        Container(
                          margin: const EdgeInsets.symmetric(vertical: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                              color: appColorLite,
                              borderRadius: BorderRadius.circular(6)),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                  flex: 3,
                                  child: Column(
                                    children: [
                                      DetailTextTile(
                                          name: "Booking ID",
                                          value: charging?.booking_id
                                                  ?.toUpperCase() ??
                                              "-"),
                                      DetailTextTile(
                                          name: "Vehicle No",
                                          value: charging?.vehical_number
                                                  ?.toUpperCase() ??
                                              "-"),
                                      DetailTextTile(
                                          name: "Connector",
                                          value: charging?.connector_no
                                                  ?.toString() ??
                                              "-"),
                                    ],
                                  )),
                              const Spacer(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 3.0),
                                    child: Text(
                                      charging?.status ?? "Waiting",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: appColor,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 3.0),
                                    child: Text(
                                      charging?.chargePointId ?? "-",
                                      style: TextStyle(
                                          fontSize: 12.sp,
                                          color: appColor,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (charging?.lastCommand ==
                                ChargingCommands.startTransaction &&
                            charging?.status == ChargingStatus.inActive)
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text(
                                "Preparing for charge...",
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                          ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: Column(
                            children: [
                              DetailTextTile(
                                  name: "Start Time",
                                  value: charging?.start_time
                                          ?.getDateTimeInUtc()
                                          .toLocal()
                                          .goodTime() ??
                                      "-"),
                              DetailTextTile(
                                  name: "End Time",
                                  value: charging?.end_time
                                          ?.getDateTimeInUtc()
                                          .toLocal()
                                          .goodTime() ??
                                      "-"),
                              DetailTextTile(
                                  name: "Charging By",
                                  value: charging?.booking_type ?? "-"),
                              DetailTextTile(
                                  name: "Estimated Time",
                                  value:
                                      '${charging?.estimatedDuration?.toStringAsFixed(1) ?? "-"} mins'),
                              DetailTextTile(
                                  name: "Estimated Units",
                                  value: charging?.estimatedUnits
                                          ?.toStringAsFixed(2) ??
                                      "-"),
                              DetailTextTile(
                                  name: "Estimated Amount",
                                  value:
                                      '₹${charging?.charging_fee?.toStringAsFixed(1) ?? "-"}'),
                              DetailTextTile(
                                  name: "GST Amount",
                                  value:
                                      '₹${charging?.tax_amount?.toStringAsFixed(1) ?? "-"}'),
                              DetailTextTile(
                                  name: "Wallet Used",
                                  value:
                                      '₹${charging?.amountFromWallet?.toStringAsFixed(1) ?? "-"}'),
                              DetailTextTile(
                                  name: "Meter Val",
                                  value: charging?.CurrentMeterWatt
                                          ?.toStringAsFixed(0) ??
                                      "-"),
                              DetailTextTile(
                                  name: "Meter Start",
                                  value: charging?.MeterStartWatt
                                          ?.toStringAsFixed(0) ??
                                      "-"),
                              DetailTextTile(
                                  name: "Meter End",
                                  value: charging?.MeterEndWatt
                                          ?.toStringAsFixed(0) ??
                                      "--"),
                              DetailTextTile(
                                  name: "Started at",
                                  value:
                                      '${charging?.startedAtPercent?.toStringAsFixed(0) ?? ""}%'),
                              DetailTextTile(
                                  name: "Stoped at",
                                  value:
                                      '${charging?.stopedAtPercent?.toStringAsFixed(0) ?? ""}%'),
                              DetailTextTile(
                                  name: "Units Burned",
                                  value: charging?.unitsBurned
                                          ?.toStringAsFixed(2) ??
                                      "--"),
                              DetailTextTile(
                                  name: "Consumption Cost",
                                  value:
                                      '₹${charging?.costOfConsump?.toStringAsFixed(2) ?? "--"}'),
                              DetailTextTile(
                                  name: "Started at",
                                  value: charging?.startedAtTime
                                          ?.getDateTimeInUtc()
                                          .toLocal()
                                          .goodTime() ??
                                      "-"),
                              DetailTextTile(
                                  name: "Stoped at",
                                  value: charging?.stopedAtTime
                                          ?.getDateTimeInUtc()
                                          .toLocal()
                                          .goodTime() ??
                                      "-"),
                              if (charging?.refundedAmount != null)
                                DetailTextTile(
                                    name: "Refunded Amount",
                                    value:
                                        '₹${charging?.refundedAmount?.toStringAsFixed(2) ?? "--"}'),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        if (charging?.status == ChargingStatus.inActive &&
                            charging?.lastCommand != null)
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                    style: OutlinedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        side: BorderSide(
                                            color: Colors.red.shade300),
                                        foregroundColor: Colors.red.shade600),
                                    onPressed: () async {
                                      if (charging?.lastCommand != null) {
                                        await APIManager.callMessageProcessor(
                                            connectorNo: charging?.connector_no,
                                            idTag: charging?.booking_id,
                                            transactionId:
                                                charging?.transactionId,
                                            action: "RemoteStopTransaction",
                                            chargerId:
                                                charging?.chargePointId ?? "");
                                      }
                                      if (context.mounted) {
                                        cancelBooking(context, charging!);
                                      }
                                    },
                                    child: const Text("Cancel Charging!")),
                              ),
                            ],
                          ),
                        const SizedBox(height: 80)
                      ],
                    ),
                  ),
                ),
              ],
            ),
      bottomNavigationBar: charging?.status == ChargingStatus.canceled ||
              isLate ||
              charging?.status == ChargingStatus.completed ||
              charging == null
          ? null
          : ChargingBottomBar(
              charging: charging,
              showStop: charging?.status == ChargingStatus.active,
              onStartCharge: loading || buttonTimeout
                  ? null
                  : () async {
                      try {
                        if (loading) return;
                        final isLateNow = (charging?.start_time
                                    ?.getDateTimeInUtc()
                                    .toLocal()
                                    .add(const Duration(
                                        minutes: autoCancelDuration))
                                    .isBefore(DateTime.now()) ??
                                true) &&
                            (charging?.status == ChargingStatus.inActive);
                        if (isLateNow) {
                          showAppShackBar("Booking timeout!");
                          cancelBooking(null, charging!);
                          return;
                        }
                        if (charging?.start_time
                                ?.getDateTimeInUtc()
                                .toLocal()
                                .isAfter(DateTime.now()) ??
                            true) {
                          showAppShackBar(
                              "Scheduled on ${charging?.start_time?.getDateTimeInUtc().toLocal().goodDayDate()} at ${charging?.start_time?.getDateTimeInUtc().toLocal().goodTime()}");
                          return;
                        }
                        setLoadingAndTimeout(isLoading: true, isTimeout: true);
                        await APIManager.callMessageProcessor(
                            connectorNo: charging?.connector_no,
                            idTag: charging?.booking_id,
                            transactionId: null,
                            action: "RemoteStartTransaction",
                            chargerId: charging?.chargePointId ?? "");
                        showAppShackBar("Charging is been started...");
                        setLoadingAndTimeout(isLoading: false);
                      } catch (e) {
                        setLoadingAndTimeout(isLoading: false);
                        debugPrint(e.toString());
                      }
                    },
              onStopCharge: loading || buttonTimeout
                  ? null
                  : () async {
                      try {
                        if (loading) return;
                        setLoadingAndTimeout(isLoading: true, isTimeout: true);
                        final chargingData = await Amplify.API
                            .query(
                                request: ModelQueries.get(
                              ChargingTable.classType,
                              ChargingTableModelIdentifier(
                                  id: widget.bookingDocId),
                            ))
                            .response
                            .then((value) => value.data);
                        if (chargingData != null) {
                          await APIManager.callMessageProcessor(
                              connectorNo: charging?.connector_no,
                              idTag: charging?.booking_id,
                              transactionId: charging?.transactionId,
                              action: "RemoteStopTransaction",
                              chargerId: charging?.chargePointId ?? "");
                          showAppShackBar("Charging is been stopped...");
                        }
                        setLoadingAndTimeout(isLoading: false);
                      } catch (e) {
                        debugPrint(e.toString());
                        setLoadingAndTimeout(isLoading: false);
                      }
                    },
            ),
    );
  }

  setLoadingAndTimeout({bool? isLoading, bool? isTimeout}) {
    if (isLoading != null) loading = isLoading;
    if (isTimeout == true) setTimeoutFalse();
    if (isTimeout != null) buttonTimeout = isTimeout;
    if (mounted) setState(() {});
  }

  setTimeoutFalse() async {
    await Future.delayed(const Duration(seconds: 7));
    buttonTimeout = false;
    if (mounted) setState(() {});
  }

  TextStyle _stationTextStyle() {
    return const TextStyle(fontSize: 18, fontWeight: FontWeight.bold);
  }
}

class DetailTextTile extends StatelessWidget {
  const DetailTextTile({
    super.key,
    required this.name,
    required this.value,
  });
  final String name;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text(
            name,
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
                fontSize: 16.sp),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black),
          ),
        ],
      ),
    );
  }
}

cancelBooking(BuildContext? context, ChargingTable charging) async {
  try {
    if (cancelingQueue.contains(charging.id)) return;
    bool? popupLoading;
    if (context == null) {
      cancelingQueue.add(charging.id);
      await Future.delayed(const Duration(seconds: 1));
      await APIManager.cancelCharging(charging);
    } else {
      if (!context.mounted) return;
      await showDialog(
        context: context,
        builder: (context) => StatefulBuilder(builder: (context, popupState) {
          return AlertDialog.adaptive(
            title: const Text("Confirm"),
            content: Column(
              children: [
                const Text("Are you sure want to cancel?"),
                if (charging.amountFromWallet == 0)
                  const Text(
                    "It may take up to 5-7 business days for the refund to appear in your account.",
                    style: TextStyle(color: Colors.red),
                  ),
              ],
            ),
            actions: popupLoading == true
                ? const [Center(child: CircularProgressIndicator.adaptive())]
                : [
                    TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text("No")),
                    TextButton(
                        onPressed: () async {
                          try {
                            popupState(() => popupLoading = true);
                            cancelingQueue.add(charging.id);
                            await APIManager.cancelCharging(charging);
                            popupState(() => popupLoading = false);
                            if (context.mounted) {
                              Navigator.of(context).pop(true);
                            }
                          } catch (e) {
                            popupState(() => popupLoading = false);
                            debugPrint(e.toString());
                            if (context.mounted) {
                              Navigator.of(context).pop(true);
                            }
                          }
                        },
                        child: const Text("Yes")),
                  ],
          );
        }),
      );
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}
