import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/models/ChargingTable.dart';
import '../../theme/theme.dart';

class ChargingBottomBar extends StatelessWidget {
  const ChargingBottomBar(
      {super.key,
      required this.charging,
      required this.onStartCharge,
      required this.onStopCharge,
      required this.showStop});

  final bool showStop;
  final ChargingTable? charging;
  final Function? onStartCharge;
  final Function? onStopCharge;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
        padding: EdgeInsets.zero,
        elevation: 12,
        shadowColor: Colors.black,
        surfaceTintColor: Colors.white,
        color: Colors.white,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey.shade100))),
          child: Row(children: [
            showStop
                ? Expanded(
                    child: OutlinedButton(
                        onPressed:
                            onStopCharge == null ? null : () => onStopCharge!(),
                        style: OutlinedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5)),
                          side: const BorderSide(color: Colors.red),
                          // backgroundColor: Colors.red.withOpacity(.1),
                        ),
                        child: const Text(
                          "Stop Charge",
                          style: TextStyle(color: Colors.red),
                        )),
                  )
                : Expanded(
                    child: ElevatedButton(
                        onPressed:
                            onStartCharge == null || charging?.isPaid != true
                                ? null
                                : () => onStartCharge!(),
                        style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 20.h),
                            backgroundColor: appColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5))),
                        child: Text(
                          charging?.isPaid != true
                              ? "Waiting for payment..."
                              : "Start Charge",
                          style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold),
                        )),
                  )
          ]),
        ));
  }
}
