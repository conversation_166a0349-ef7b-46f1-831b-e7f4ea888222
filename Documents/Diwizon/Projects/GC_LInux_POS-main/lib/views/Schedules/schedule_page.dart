import 'dart:async';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/booking_ctrl.dart';
import 'package:go_charge/models/ChargingTable.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/snackbar.dart';
import 'package:go_charge/utils/extentions.dart';
import 'package:go_charge/views/charging/charging_page.dart';
import '../../constants/const.dart';
import '../../controllers/app_ctrl.dart';
import '../../controllers/auth_ctrl.dart';
import '../../models/Station.dart';
import '../../theme/theme.dart';
import '../other/loader_page.dart';
import '../other/login_button.dart';

class ScheduleWid extends StatefulWidget {
  const ScheduleWid({super.key});

  @override
  State<ScheduleWid> createState() => _ScheduleWidState();
}

class _ScheduleWidState extends State<ScheduleWid> {
  StreamSubscription<amplify.GraphQLResponse<ChargingTable>>? _chargingStream;
  List<ChargingTable>? chargingList;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool?>(
        future: Get.find<AuthCtrl>().isLoggedIn(),
        builder: (context, snapshot) {
          if (snapshot.data == true) {
            return Stack(fit: StackFit.expand, children: [
              chargingList?.isEmpty ?? true
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(24.0),
                        child: Text("No charges have been scheduled yet!"),
                      ),
                    )
                  : Column(
                      children: [
                        Expanded(
                            child: ListView.builder(
                          padding: EdgeInsets.only(
                              top: MediaQuery.of(context).padding.top + 80),
                          itemCount: chargingList?.length ?? 0,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            final charging = chargingList?[index];
                            final isLate = (charging?.paymentTime
                                        ?.getDateTimeInUtc()
                                        .toLocal()
                                        .add(const Duration(
                                            minutes: autoCancelDuration))
                                        .isBefore(DateTime.now()) ??
                                    true) &&
                                (charging?.status == ChargingStatus.inActive);
                            return charging == null
                                ? const SizedBox()
                                : InkWell(
                                    onTap: () => AppRoutes.goToChargingDetails(
                                        context, charging.id),
                                    child: Card(
                                      color: Colors.white,
                                      shadowColor: Colors.black,
                                      surfaceTintColor: Colors.white,
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 6),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.all(10.0.h),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Text(charging.id),
                                                Row(
                                                  children: [
                                                    Text(
                                                        DateTime.fromMicrosecondsSinceEpoch(charging
                                                                    .paymentTime
                                                                    ?.getDateTimeInUtc()
                                                                    .microsecondsSinceEpoch ??
                                                                DateTime.now()
                                                                    .microsecondsSinceEpoch)
                                                            .goodDayDate(),
                                                        style: textStyle),
                                                    if (isLate)
                                                      const Text(
                                                        " (Cancelled)",
                                                        style: TextStyle(
                                                            color: Colors.red),
                                                      ),
                                                    const Spacer(),
                                                    Text(
                                                        DateTime.fromMicrosecondsSinceEpoch(charging
                                                                .paymentTime!
                                                                .getDateTimeInUtc()
                                                                .toLocal()
                                                                .microsecondsSinceEpoch)
                                                            .goodTime(),
                                                        style: textStyle),
                                                  ],
                                                ),
                                                const SizedBox(height: 12),
                                                FutureBuilder<Station?>(
                                                  future: Amplify.API
                                                      .query(
                                                          request:
                                                              ModelQueries.get(
                                                        Station.classType,
                                                        StationModelIdentifier(
                                                            id: charging
                                                                    .station_id ??
                                                                ""),
                                                      ))
                                                      .response
                                                      .then((value) =>
                                                          value.data),
                                                  builder: (context, snapshot) {
                                                    if (snapshot.hasData) {
                                                      return Text(
                                                        snapshot.data
                                                                ?.station_name ??
                                                            "GO Charge Charging Station",
                                                        style: const TextStyle(
                                                            fontSize: 18,
                                                            fontWeight:
                                                                FontWeight
                                                                    .bold),
                                                      );
                                                    }
                                                    return const SizedBox();
                                                  },
                                                ),
                                                /* const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("Charger", style: textStyle),
                                const SizedBox(height: 12),
                                Text(
                                  charging.chargePointId ?? "",
                                  style: textStyle2,
                                ),
                              ],
                            ), */
                                                Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(vertical: 6.0),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text("Connector",
                                                          style: textStyle),
                                                      const SizedBox(
                                                          height: 12),
                                                      Text(
                                                        charging.connector_no
                                                                ?.toString() ??
                                                            "",
                                                        style: textStyle2,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                if (charging.booking_type ==
                                                    "Amount")
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 6.0),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Text("Estimated Price",
                                                            style: textStyle),
                                                        const SizedBox(
                                                            height: 12),
                                                        Text(
                                                          '₹${charging.charging_fee?.toStringAsFixed(1) ?? ""}',
                                                          style: textStyle2,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                if (charging.booking_type ==
                                                    "Units")
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 6.0),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Text("Estimated Units",
                                                            style: textStyle),
                                                        const SizedBox(
                                                            height: 12),
                                                        Text(
                                                          '${charging.estimatedUnits?.toStringAsFixed(1) ?? ""} KW',
                                                          style: textStyle2,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                if (charging.booking_type ==
                                                    "Duration")
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 6.0),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Text("Estimated Time",
                                                            style: textStyle),
                                                        const SizedBox(
                                                            height: 12),
                                                        Text(
                                                          '${charging.estimatedDuration?.toStringAsFixed(0) ?? ""} mins',
                                                          style: textStyle2,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                          if (charging.status !=
                                              ChargingStatus.canceled)
                                            const Divider(height: 0),
                                          if (charging.status !=
                                              ChargingStatus.canceled)
                                            IntrinsicHeight(
                                              child: Row(
                                                children: [
                                                  if (!isLate &&
                                                      charging.status ==
                                                          ChargingStatus
                                                              .inActive &&
                                                      charging.lastCommand ==
                                                          null)
                                                    Expanded(
                                                      child: TextButton(
                                                        style: TextButton
                                                            .styleFrom(
                                                                foregroundColor:
                                                                    Colors.red
                                                                        .shade900),
                                                        onPressed: () async {
                                                          if (cancelingQueue
                                                              .contains(charging
                                                                  .id)) {
                                                            showAppShackBar(
                                                                "Booking timeout, canceling!");
                                                            return;
                                                          }
                                                          await cancelBooking(
                                                              context,
                                                              charging);
                                                          if (mounted) {
                                                            setState(() {});
                                                          }
                                                        },
                                                        child: const Text(
                                                            "Cancel"),
                                                      ),
                                                    ),
                                                  if (!isLate &&
                                                      charging.status ==
                                                          ChargingStatus
                                                              .inActive)
                                                    const VerticalDivider(
                                                        width: 0),
                                                  if (charging.status !=
                                                      ChargingStatus.canceled)
                                                    Expanded(
                                                      child: TextButton(
                                                        style: TextButton
                                                            .styleFrom(
                                                                foregroundColor:
                                                                    Colors.green
                                                                        .shade700),
                                                        onPressed: () => AppRoutes
                                                            .goToChargingDetails(
                                                                context,
                                                                charging.id),
                                                        child: const Row(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Text("View Status"),
                                                            SizedBox(width: 4),
                                                            Icon(
                                                              Icons
                                                                  .arrow_forward_ios_rounded,
                                                              size: 14,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  );
                          },
                        )),
                      ],
                    ),
              Column(
                children: [
                  AppBar(
                    backgroundColor: const Color(0xffE1FFE4),
                    surfaceTintColor: const Color(0xffE1FFE4),
                    elevation: 8,
                    title: Text(
                      "Charging Schedules",
                      style: TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 18.h),
                    ),
                  ),
                ],
              ),
            ]);
          } else if (snapshot.data == false) {
            return LoginButton(refresh: () => setState(() {}));
          }
          return const LoaderPage();
        });
  }

  @override
  void initState() {
    super.initState();
    getChargings();
  }

  @override
  void dispose() {
    super.dispose();
    _chargingStream?.cancel();
  }

  void getChargings() async {
    try {
      // print(Get.find<AppCtrl>().currentAuthUser?.userId);
      final request = amplify.ModelQueries.list(ChargingTable.classType,
          limit: unlimitedLimit,
          where: ChargingTable.USER_ID
              .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
              .and(ChargingTable.ISPAID.eq(true))
              .and(ChargingTable.STATUS
                  .eq(ChargingStatus.inActive)
                  .or(ChargingTable.STATUS.eq(ChargingStatus.active))));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      chargingList =
          result.data?.items.whereType<ChargingTable>().toList() ?? [];

      // Fetch Paginated Results
      amplify.GraphQLResponse<PaginatedResult<ChargingTable>> paginatedResult =
          result;
      while (paginatedResult.data?.hasNextResult ?? false) {
        try {
          debugPrint(paginatedResult.data?.hasNextResult.toString());
          final res = await Amplify.API
              .query(request: paginatedResult.data!.requestForNextResult!)
              .response;
          chargingList?.addAll(
              res.data?.items.whereType<ChargingTable>().toList() ?? []);
          paginatedResult = res;
        } catch (e) {
          debugPrint(e.toString());
        }
      }

      safePrint('ChargingTable Found: ${result.data?.items.length}');

      chargingList?.sort((b, a) =>
          a.paymentTime?.compareTo(b.paymentTime ?? TemporalDateTime.now()) ??
          0);
      if (mounted) setState(() {});
      // Check for Auto Cancel //
      chargingList?.forEach((element) {
        final isLate = (element.paymentTime
                    ?.getDateTimeInUtc()
                    .toLocal()
                    .add(const Duration(minutes: autoCancelDuration))
                    .isBefore(DateTime.now()) ??
                true) &&
            (element.status == ChargingStatus.inActive);
        if (isLate &&
            element.status != ChargingStatus.active &&
            element.status != ChargingStatus.canceled) {
          debugPrint("Canceling booking id ${element.booking_id}");
          // showAppShackBar("Canceling booking id ${element.booking_id}");
          // showAppShackBar("Going to cancel...${element.id}");
          // cancelBooking(null, element);
        }
      });

      observeCharging();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying ChargingTable: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying ChargingTable: $e');
    }
  }

  void observeCharging() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        ChargingTable.classType,
        where: ChargingTable.USER_ID
            .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
            .and(ChargingTable.ISPAID.eq(true))
            .and(ChargingTable.STATUS
                .ne(ChargingStatus.canceled)
                .or(ChargingTable.STATUS.ne(ChargingStatus.completed))));
    // Stream
    final Stream<amplify.GraphQLResponse<ChargingTable>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    _chargingStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received charging: ${event.data}');
        if (event.data != null && chargingList != null) {
          chargingList![chargingList!
                  .indexWhere((element) => element.id == event.data?.id)] =
              event.data!;
          chargingList?.sort((b, a) =>
              a.paymentTime
                  ?.compareTo(b.paymentTime ?? TemporalDateTime.now()) ??
              0);
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  final buttonStyle2 =
      const ButtonStyle(backgroundColor: WidgetStatePropertyAll(appColor));
  final buttonStyle = const ButtonStyle(
      backgroundColor:
          WidgetStatePropertyAll(Color.fromARGB(255, 255, 255, 255)));
  final textStyle = TextStyle(
      fontSize: 14.h,
      color: const Color.fromARGB(255, 88, 88, 88),
      fontWeight: FontWeight.w500);
  final textStyle2 = TextStyle(fontSize: 14.h, fontWeight: FontWeight.w600);
}
