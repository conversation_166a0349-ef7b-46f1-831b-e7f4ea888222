import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/models/ChargingTable.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/utils/extentions.dart';
import '../../controllers/app_ctrl.dart';
import '../../models/Station.dart';

class DuesPage extends StatefulWidget {
  const DuesPage({super.key, required this.list});

  final List<ChargingTable> list;

  @override
  State<DuesPage> createState() => _DuesPageState();
}

class _DuesPageState extends State<DuesPage> {
  List<ChargingTable>? list;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: list?.isEmpty ?? false,
      child: Scaffold(
          appBar: AppBar(title: const Text("Overcharge Dues")),
          body: ListView.builder(
            itemCount: list?.length ?? 0,
            // padding: const EdgeInsets.all(20),
            itemBuilder: (context, index) {
              return _DuePageCard(charging: list![index]);
            },
          ),
          bottomNavigationBar: BottomAppBar(
            color: Colors.white,
            child: ElevatedButton(
                onPressed: _onPayNow,
                child: Text("Pay Now ₹${list?.map((e) => e.dueAmount).reduce(
                      (value, element) => (value ?? 0) + (element ?? 0),
                    )}")),
          )),
    );
  }

  _onPayNow() async {
    try {
      list
          ?.map((e) => e.dueAmount)
          .reduce((value, element) => (value ?? 0) + (element ?? 0));
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  reCheck() async {
    try {
      // await checkForPendingPayments();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    list = widget.list;
  }
}

class _DuePageCard extends StatelessWidget {
  _DuePageCard({
    required this.charging,
  });

  final ChargingTable charging;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => AppRoutes.goToChargingDetails(context, charging.id),
      child: Card(
        color: Colors.white,
        shadowColor: Colors.black,
        surfaceTintColor: Colors.white,
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(10.0.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                          DateTime.fromMicrosecondsSinceEpoch(charging
                                  .start_time!
                                  .getDateTimeInUtc()
                                  .microsecondsSinceEpoch)
                              .goodDayDate(),
                          style: textStyle),
                      const Spacer(),
                      Text(
                          DateTime.fromMicrosecondsSinceEpoch(charging
                                  .start_time!
                                  .getDateTimeInUtc()
                                  .toLocal()
                                  .microsecondsSinceEpoch)
                              .goodTime(),
                          style: textStyle),
                    ],
                  ),
                  const SizedBox(height: 12),
                  FutureBuilder<Station?>(
                    future: Amplify.API
                        .query(
                            request: ModelQueries.get(
                          Station.classType,
                          StationModelIdentifier(id: charging.station_id ?? ""),
                        ))
                        .response
                        .then((value) => value.data),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Text(
                          snapshot.data?.station_name ??
                              "GO Charge Charging Station",
                          style: const TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                  /* const SizedBox(height: 12),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text("Charger", style: textStyle),
                                  const SizedBox(height: 12),
                                  Text(
                                    charging.chargePointId ?? "",
                                    style: textStyle2,
                                  ),
                                ],
                              ), */
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Connector", style: textStyle),
                        const SizedBox(height: 12),
                        Text(
                          charging.connector_no?.toString() ?? "",
                          style: textStyle2,
                        ),
                      ],
                    ),
                  ),
                  if (charging.booking_type == "Amount")
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text("Estimated Price", style: textStyle),
                          const SizedBox(height: 12),
                          Text(
                            '₹${charging.charging_fee?.toStringAsFixed(1) ?? ""}',
                            style: textStyle2,
                          ),
                        ],
                      ),
                    ),
                  if (charging.booking_type == "Units")
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text("Estimated Units", style: textStyle),
                          const SizedBox(height: 12),
                          Text(
                            '${charging.estimatedUnits?.toStringAsFixed(1) ?? ""} KW',
                            style: textStyle2,
                          ),
                        ],
                      ),
                    ),
                  if (charging.booking_type == "Duration")
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text("Estimated Time", style: textStyle),
                          const SizedBox(height: 12),
                          Text(
                            '${charging.estimatedDuration?.toStringAsFixed(0) ?? ""} mins',
                            style: textStyle2,
                          ),
                        ],
                      ),
                    ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Due Amount", style: textStyle),
                        const SizedBox(height: 12),
                        Text(
                          '₹${charging.dueAmount?.toStringAsFixed(1) ?? ""}',
                          style: textStyle2.copyWith(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  final textStyle = TextStyle(
      fontSize: 14.h,
      color: const Color.fromARGB(255, 88, 88, 88),
      fontWeight: FontWeight.w500);
  final textStyle2 = TextStyle(fontSize: 14.h, fontWeight: FontWeight.w600);
}
