import 'dart:async';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/utils/extentions.dart';
import 'package:go_charge/utils/invoice_generator.dart';
import '../../constants/const.dart';
import '../../controllers/app_ctrl.dart';
import '../../controllers/booking_ctrl.dart';
import '../../models/ChargingTable.dart';
import '../../models/Station.dart';
import '../../routers/routers.dart';
import '../../theme/theme.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;

class ChargingHistory extends StatefulWidget {
  const ChargingHistory({super.key});

  @override
  State<ChargingHistory> createState() => _ChargingHistoryState();
}

class _ChargingHistoryState extends State<ChargingHistory> {
  StreamSubscription<amplify.GraphQLResponse<ChargingTable>>? _chargingStream;
  List<ChargingTable>? chargingList;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Charging History")),
      body: chargingList == null
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: chargingList?.length ?? 0,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                final charging = chargingList?[index];
                final isLate = (charging?.start_time
                            ?.getDateTimeInUtc()
                            .toLocal()
                            .add(const Duration(minutes: autoCancelDuration))
                            .isBefore(DateTime.now()) ??
                        true) &&
                    (charging?.status == ChargingStatus.inActive);
                return charging == null
                    ? const SizedBox()
                    : InkWell(
                        onTap: () => AppRoutes.goToChargingDetails(
                            context, charging.id,
                            popTillFirst: false),
                        child: Card(
                          color: Colors.white,
                          shadowColor: Colors.black,
                          surfaceTintColor: Colors.white,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.all(10.0.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                            DateTime.fromMicrosecondsSinceEpoch(
                                                    charging.start_time
                                                            ?.getDateTimeInUtc()
                                                            .microsecondsSinceEpoch ??
                                                        0)
                                                .goodDayDate(),
                                            style: textStyle),
                                        if (isLate)
                                          const Text(
                                            " (Cancelled)",
                                            style: TextStyle(color: Colors.red),
                                          ),
                                        const Spacer(),
                                        Text(
                                            DateTime.fromMicrosecondsSinceEpoch(
                                                    charging.start_time
                                                            ?.getDateTimeInUtc()
                                                            .toLocal()
                                                            .microsecondsSinceEpoch ??
                                                        0)
                                                .goodTime(),
                                            style: textStyle),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    FutureBuilder<Station?>(
                                      future: Amplify.API
                                          .query(
                                              request: ModelQueries.get(
                                            Station.classType,
                                            StationModelIdentifier(
                                                id: charging.station_id ?? ""),
                                          ))
                                          .response
                                          .then((value) => value.data),
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          return Text(
                                            snapshot.data?.station_name ??
                                                "GO Charge Charging Station",
                                            style: const TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold),
                                          );
                                        }
                                        return const SizedBox();
                                      },
                                    ),
                                    /* const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("Charger", style: textStyle),
                                const SizedBox(height: 12),
                                Text(
                                  charging.chargePointId ?? "",
                                  style: textStyle2,
                                ),
                              ],
                            ), */
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("Booking Id", style: textStyle),
                                          const SizedBox(height: 12),
                                          Text(
                                            charging.booking_id?.toString() ??
                                                "",
                                            style: textStyle2,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("Connector", style: textStyle),
                                          const SizedBox(height: 12),
                                          Text(
                                            charging.connector_no?.toString() ??
                                                "",
                                            style: textStyle2,
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (charging.booking_type == "Amount")
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 6.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text("Estimated Price",
                                                style: textStyle),
                                            const SizedBox(height: 12),
                                            Text(
                                              '₹${charging.charging_fee?.toStringAsFixed(1) ?? ""}',
                                              style: textStyle2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    if (charging.booking_type == "Units")
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 6.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text("Estimated Units",
                                                style: textStyle),
                                            const SizedBox(height: 12),
                                            Text(
                                              '${charging.estimatedUnits?.toStringAsFixed(1) ?? ""} KW',
                                              style: textStyle2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    if (charging.booking_type == "Duration")
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 6.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text("Estimated Time",
                                                style: textStyle),
                                            const SizedBox(height: 12),
                                            Text(
                                              '${charging.estimatedDuration?.toStringAsFixed(0) ?? ""} mins',
                                              style: textStyle2,
                                            ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              if (charging.status == ChargingStatus.completed)
                                IntrinsicHeight(
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextButton.icon(
                                          style: TextButton.styleFrom(
                                              foregroundColor:
                                                  Colors.blue.shade900),
                                          onPressed: () {
                                            // cancelBooking(context, charging);
                                            generateInvoice(charging);
                                          },
                                          icon: const Icon(
                                            CupertinoIcons.arrow_down_to_line,
                                            size: 18,
                                          ),
                                          label: const Text("Invoice"),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
              },
            ),
    );
  }

  @override
  void initState() {
    super.initState();
    getChargings();
  }

  @override
  void dispose() {
    super.dispose();
    _chargingStream?.cancel();
  }

  void getChargings() async {
    try {
      final request = amplify.ModelQueries.list(ChargingTable.classType,
          limit: unlimitedLimit,
          where: ChargingTable.USER_ID
              .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
              .and(ChargingTable.ISPAID.eq(true))
              .and(ChargingTable.STATUS.ne(ChargingStatus.inActive)));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('ChargingTable Found: ${result.data?.items.length}');
      chargingList =
          result.data?.items.whereType<ChargingTable>().toList() ?? [];
      chargingList?.sort((b, a) =>
          a.start_time?.compareTo(b.start_time ?? TemporalDateTime.now()) ?? 0);
      if (mounted) setState(() {});
      // observeCharging();
    } on amplify.ApiException catch (e) {
      safePrint('Something went wrong querying ChargingTable: ${e.message}');
    } catch (e) {
      safePrint('Something went wrong querying ChargingTable: $e');
    }
  }

  void observeCharging() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        ChargingTable.classType,
        where: ChargingTable.USER_ID
            .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
            .and(ChargingTable.ISPAID.eq(true))
            .and(ChargingTable.STATUS.ne(ChargingStatus.inActive)));
    // Stream
    final Stream<amplify.GraphQLResponse<ChargingTable>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    _chargingStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received charging: ${event.data}');
        if (event.data != null && chargingList != null) {
          chargingList![chargingList!
                  .indexWhere((element) => element.id == event.data?.id)] =
              event.data!;
          chargingList?.sort((b, a) =>
              a.start_time?.compareTo(b.start_time ?? TemporalDateTime.now()) ??
              0);
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  final buttonStyle2 =
      const ButtonStyle(backgroundColor: WidgetStatePropertyAll(appColor));
  final buttonStyle = const ButtonStyle(
      backgroundColor:
          WidgetStatePropertyAll(Color.fromARGB(255, 255, 255, 255)));
  final textStyle = TextStyle(
      fontSize: 14.h,
      color: const Color.fromARGB(255, 88, 88, 88),
      fontWeight: FontWeight.w500);
  final textStyle2 = TextStyle(fontSize: 14.h, fontWeight: FontWeight.w600);
}
