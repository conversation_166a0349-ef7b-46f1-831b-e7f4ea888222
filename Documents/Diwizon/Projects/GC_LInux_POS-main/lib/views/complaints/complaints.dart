import 'dart:async';
import 'package:amplify_api/amplify_api.dart' as amplify;
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/utils/extentions.dart';
import '../../constants/const.dart';
import '../../controllers/app_ctrl.dart';

class ComplaintsPage extends StatefulWidget {
  const ComplaintsPage({super.key});

  @override
  State<ComplaintsPage> createState() => _ComplaintsPageState();
}

class _ComplaintsPageState extends State<ComplaintsPage> {
  StreamSubscription<amplify.GraphQLResponse<SupportRequest>>? complaintStream;
  List<SupportRequest>? complaints;
  bool loading = false;
  bool loadingTrans = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Compliants"),
      ),
      body: ListView.builder(
        itemCount: complaints?.length ?? 0,
        shrinkWrap: true,
        itemBuilder: (context, index) =>
            _ComplaintCard(complaint: complaints![index]),
      ),
      floatingActionButton: FloatingActionButton.extended(
          icon: const Icon(CupertinoIcons.add),
          onPressed: () => AppRoutes.goToNewComplaint(context),
          label: const Text("New Complaint")),
    );
  }

  @override
  void initState() {
    super.initState();
    getComplaints();
  }

  void getComplaints() async {
    try {
      safePrint("Fethcing Complaints...");
      if (mounted) setState(() => loading = true);
      final request = amplify.ModelQueries.list(SupportRequest.classType,
          limit: unlimitedLimit,
          where: SupportRequest.UID
              .eq(Get.find<AppCtrl>().currentAuthUser?.userId));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Transaction: ${result.data?.items.length}');
      // manufacturers = result;
      complaints =
          result.data?.items.whereType<SupportRequest>().toList() ?? [];
      complaints?.sort((a, b) =>
          b.createdAt?.compareTo(a.createdAt ?? TemporalDateTime.now()) ?? 0);
      if (mounted) setState(() => loading = false);
      observeComplaints();
    } on amplify.ApiException catch (e) {
      if (mounted) setState(() => loading = false);
      safePrint('Something went wrong querying Transaction: ${e.message}');
      return null;
    } catch (e) {
      if (mounted) setState(() => loading = false);
      safePrint('Something went wrong querying Transaction: $e');
      return null;
    }
  }

  void observeComplaints() {
    final subscriptionRequest = amplify.ModelSubscriptions.onCreate(
        SupportRequest.classType,
        where:
            SupportRequest.UID.eq(Get.find<AppCtrl>().currentAuthUser?.userId));
    // Stream
    final Stream<amplify.GraphQLResponse<SupportRequest>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    complaintStream?.cancel();
    complaintStream = operation.listen(
      (event) async {
        safePrint(
            'Subscription event data received SupportRequest: ${event.data}');
        if (event.data != null) {
          complaints?.insert(0, event.data!);
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }
}

class _ComplaintCard extends StatelessWidget {
  const _ComplaintCard({
    required this.complaint,
  });

  final SupportRequest complaint;

  @override
  Widget build(BuildContext context) {
    return Card(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      complaint.category ?? "",
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w600),
                    )),
                    Text(complaint.createdAt
                            ?.getDateTimeInUtc()
                            .toLocal()
                            .goodDayDate() ??
                        ""),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      complaint.subCategory ?? "",
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w500),
                    )),
                    Text(complaint.status ?? ""),
                  ],
                ),
                Text(
                  '${complaint.chargingStation ?? ''} - ${complaint.remarks ?? ""}',
                  style: TextStyle(color: Colors.grey.shade800),
                )
              ],
            )));
  }
}
