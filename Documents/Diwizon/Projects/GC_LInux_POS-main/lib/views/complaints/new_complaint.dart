import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/globals.dart';
import 'package:go_charge/shared/methods.dart';
import 'package:go_charge/shared/snackbar.dart';
import 'package:go_charge/theme/theme.dart';

import '../../controllers/app_ctrl.dart';

class NewComplaint extends StatefulWidget {
  const NewComplaint({super.key});

  @override
  State<NewComplaint> createState() => _NewComplaintState();
}

class _NewComplaintState extends State<NewComplaint> {
  String? selectedCategory;
  String? selectedSubCategory;
  late TextEditingController stationNameCtrl;
  late TextEditingController remarkCtrl;
  bool authToCall = false;
  bool ack = false;
  bool loading = false;
  late GlobalKey<FormState> _formKey;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("New Complaint")),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              DropdownButtonFormField(
                validator: (value) =>
                    value?.isEmpty ?? true ? "Required" : null,
                decoration: _inputDecor().copyWith(hintText: "Select Category"),
                value: selectedCategory,
                items: supportOptions.keys
                    .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                    .toList(),
                onChanged: (value) => setState(() {
                  selectedCategory = value;
                  selectedSubCategory = (supportOptions[selectedCategory]
                              ?.contains(selectedSubCategory) ??
                          false)
                      ? selectedSubCategory
                      : null;
                }),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField(
                validator: (value) =>
                    value?.isEmpty ?? true ? "Required" : null,
                decoration:
                    _inputDecor().copyWith(hintText: "Select Sub Category"),
                value: selectedSubCategory,
                items: supportOptions[selectedCategory]
                    ?.map((e) => DropdownMenuItem(value: e, child: Text(e)))
                    .toList(),
                onChanged: (value) =>
                    setState(() => selectedSubCategory = value),
              ),
              const SizedBox(height: 16),
              TextFormField(
                validator: (value) =>
                    value?.isEmpty ?? true ? "Required" : null,
                controller: stationNameCtrl,
                decoration: _inputDecor().copyWith(hintText: "Station Name"),
              ),
              const SizedBox(height: 16),
              TextFormField(
                validator: (value) =>
                    value?.isEmpty ?? true ? "Required" : null,
                controller: remarkCtrl,
                minLines: 5,
                maxLines: 5,
                decoration: _inputDecor().copyWith(hintText: "Remarks"),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Checkbox.adaptive(
                      value: authToCall,
                      onChanged: (value) =>
                          setState(() => authToCall = value!)),
                  const Text("Authorize to call")
                ],
              ),
              Row(
                children: [
                  Checkbox.adaptive(
                      value: ack,
                      onChanged: (value) => setState(() => ack = value!)),
                  const Expanded(
                    child: Text(
                        "I hereby declare that the above furnished details are true to the best of my knoledge."),
                  )
                ],
              ),
              const SizedBox(height: 28),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: appColor,
                        minimumSize: const Size.fromHeight(45),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4)),
                      ),
                      onPressed: onSubmit,
                      child: loading
                          ? const SizedBox(
                              height: 30,
                              width: 30,
                              child: CircularProgressIndicator(
                                  strokeWidth: 1,
                                  valueColor:
                                      AlwaysStoppedAnimation(Colors.white)),
                            )
                          : const Text(
                              "Submit Complaint",
                              style: TextStyle(color: Colors.white),
                            ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  InputDecoration _inputDecor() {
    return InputDecoration(
      filled: true,
      fillColor: Colors.white,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: Colors.grey.shade200)),
    );
  }

  onSubmit() async {
    try {
      if (!(_formKey.currentState?.validate() ?? false)) return;
      if (!ack) {
        showAppShackBar("Please accept declaration!");
        return;
      }
      setState(() => loading = true);
      // Create Complaint
      final response = await Amplify.API
          .mutate(
              request: ModelMutations.create(SupportRequest(
                  uId: Get.find<AppCtrl>().currentAuthUser?.userId,
                  userName: Get.find<AppCtrl>().currentUserData?.user_fullname,
                  userContact: Get.find<AppCtrl>().currentUserData?.contact,
                  category: selectedCategory,
                  code: getRandomId(8),
                  subCategory: selectedSubCategory,
                  status: "Pending",
                  chargingStation: stationNameCtrl.text,
                  remarks: remarkCtrl.text)))
          .response;
      if (response.data != null) {
        if (mounted) AppRoutes.popTill(context, "complaint");
        showAppShackBar("Your complaint was registered successfully");
        clearFields();
      } else {
        showAppShackBar("Something went wrong!");
      }
      setState(() => loading = false);
    } catch (e) {
      debugPrint(e.toString());
      showAppShackBar("Something went wrong!");
      setState(() => loading = false);
    }
  }

  clearFields() {
    selectedCategory = null;
    selectedSubCategory = null;
    stationNameCtrl.clear();
    remarkCtrl.clear();
    ack = false;
    authToCall = false;
    if (mounted) setState(() {});
  }

  @override
  void initState() {
    super.initState();
    stationNameCtrl = TextEditingController();
    remarkCtrl = TextEditingController();
    _formKey = GlobalKey<FormState>();
  }

  @override
  void dispose() {
    super.dispose();
    stationNameCtrl.dispose();
    remarkCtrl.dispose();
  }
}
