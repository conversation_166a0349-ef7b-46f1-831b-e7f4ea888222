import 'package:flutter/material.dart';
import 'package:go_charge/shared/methods.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
// import 'dart:html' as html; // For handling the redirect

class PaymentScreen extends StatefulWidget {
  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  TextEditingController transactionIdController =
      TextEditingController(text: generateId(12));
  TextEditingController amountController = TextEditingController(text: "1000");
  TextEditingController mobileNumberController =
      TextEditingController(text: "+916353817704");
  String paymentStatus = '';

  Future<void> initiatePayment() async {
    setState(() {
      paymentStatus = 'Initiating Payment...';
    });

    final lambdaFunctionUrl =
        'https://guqw4emxlljmjynmw2al4dztry0uutpm.lambda-url.ap-south-1.on.aws/'; // Replace with your actual Function URL

    try {
      final response = await http.post(
        Uri.parse(lambdaFunctionUrl),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, dynamic>{
          'transactionId': transactionIdController.text,
          'amount': double.parse(amountController
              .text), // Ensure amount is in paise if required by PhonePe
          'mobileNumber': mobileNumberController.text,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true &&
            responseData['data'] != null &&
            responseData['data']['instrumentResponse'] != null &&
            responseData['data']['instrumentResponse']['redirectInfo'] !=
                null) {
          final redirectUrl =
              responseData['data']['instrumentResponse']['redirectInfo']['url'];
          print('Redirecting to: $redirectUrl');
          // html.window.location.href =
          //     redirectUrl; // Perform the redirect in the same tab
        } else {
          setState(() {
            paymentStatus =
                'Payment initiation failed: ${responseData['message'] ?? 'Unknown error'}';
          });
        }
      } else {
        setState(() {
          paymentStatus =
              'Error communicating with the server: Status ${response.statusCode}';
        });
      }
    } catch (error) {
      setState(() {
        paymentStatus = 'An error occurred: $error';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('PhonePe Payment'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            TextField(
              controller: transactionIdController,
              decoration: InputDecoration(labelText: 'Transaction ID'),
            ),
            TextField(
              controller: amountController,
              decoration: InputDecoration(labelText: 'Amount (in paise)'),
              keyboardType: TextInputType.number,
            ),
            TextField(
              controller: mobileNumberController,
              decoration: InputDecoration(labelText: 'Mobile Number'),
              keyboardType: TextInputType.phone,
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: initiatePayment,
              child: Text('Initiate Payment'),
            ),
            SizedBox(height: 20),
            Text(
              paymentStatus,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}
