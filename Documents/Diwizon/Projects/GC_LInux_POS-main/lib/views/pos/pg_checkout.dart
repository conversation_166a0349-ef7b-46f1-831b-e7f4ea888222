// import 'dart:html' as html;
// import 'dart:js' as js;

import 'package:flutter/material.dart';

class PhonePeCheckoutWeb extends StatefulWidget {
  final String tokenUrl;

  PhonePeCheckoutWeb({required this.tokenUrl});

  @override
  _PhonePeCheckoutWebState createState() => _PhonePeCheckoutWebState();
}

class _PhonePeCheckoutWebState extends State<PhonePeCheckoutWeb> {
  void _paymentCallback(response) {
    print("PhonePe Checkout Response: $response");
    if (response == 'USER_CANCEL') {
      // Add merchant's logic for user cancellation on the UI
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Payment Cancelled by User')),
      );
      return;
    } else if (response == 'CONCLUDED') {
      // Add merchant's logic for successful transaction on the UI
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Payment Successful!')),
      );
      // You might want to navigate to a success page or update the UI
      return;
    } else {
      // Handle other responses or errors
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Payment Status: $response')),
      );
    }
  }

  void _handlePayPage() {
    // if (js.context.hasProperty('checkout')) {
    //   // The package is loaded
    //   final tokenUrl = widget.tokenUrl;
    //   const type = "IFRAME";
    //   js.context
    //       .callMethod('checkout', [tokenUrl, type, js.allowInterop(callback)]);
    // }
  }

  void callback(response) {
    if (response == 'USER_CANCEL') {
      print("Payment Cancelled by User");
      // Add merchant's logic if they have any custom thing to trigger on UI after the transaction is canceled by the user
      return;
    } else if (response == 'CONCLUDED') {
      print("Payment Successful!");
      // Add merchant's logic if they have any custom thing to trigger on UI after the transaction is in terminal state
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('PhonePe Checkout (Web)'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: _handlePayPage,
          child: Text('Pay with PhonePe'),
        ),
      ),
    );
  }
}


/* import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class CheckoutPageInAppWebView extends StatefulWidget {
  final String tokenUrl;

  CheckoutPageInAppWebView({required this.tokenUrl});

  @override
  _CheckoutPageInAppWebViewState createState() =>
      _CheckoutPageInAppWebViewState();
}

class _CheckoutPageInAppWebViewState extends State<CheckoutPageInAppWebView> {
  InAppWebViewController? _webViewController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('PhonePe Checkout'),
      ),
      body: InAppWebView(
        initialOptions: InAppWebViewGroupOptions(
          crossPlatform: InAppWebViewOptions(
            javaScriptEnabled: true,
          ),
        ),
        initialUrlRequest: URLRequest(
            url: WebUri.uri(
          Uri.dataFromString('''
            <!DOCTYPE html>
            <html>
            <head>
              <script src="https://mercury.phonepe.com/web/bundle/checkout.js"></script>
              <script>
                function initiatePayment(tokenUrl) {
                  window.PhonePeCheckout.transact({
                    tokenUrl: tokenUrl,
                    callback: function(response) {
                      // Send the response back to Flutter
                      window.flutter_inappwebview.callHandler('paymentStatus', response);
                    },
                    type: 'REDIRECT' // Or 'IFRAME'
                  });
                }
              </script>
            </head>
            <body>
              <button onclick="initiatePayment('${widget.tokenUrl}')">Pay with PhonePe</button>
            </body>
            </html>
          ''', mimeType: 'text/html'),
        )),
        onWebViewCreated: (controller) {
          _webViewController = controller;
        },
        onLoadStop: (controller, url) async {
          // You might want to inject the token URL after the page loads
          // controller.evaluateJavascript(
          //     'initiatePayment("${widget.tokenUrl}");');
        },
        androidOnPermissionRequest: (controller, origin, resources) async {
          return PermissionRequestResponse(
              resources: resources,
              action: PermissionRequestResponseAction.GRANT);
        },
        onConsoleMessage: (controller, consoleMessage) {
          print("WebView Console: ${consoleMessage.message}");
        },
        onLoadError: (controller, url, code, message) {
          print("WebView Load Error: $message");
        },
        onLoadHttpError: (controller, url, statusCode, description) {
          print("WebView HTTP Error: $statusCode - $description");
        },
        // javaScriptHandlers: {
        //   JavascriptChannel(
        //     name: 'paymentStatus',
        //     javascriptMessageHandler: (JavaScriptMessage message) {
        //       // Handle the payment status received from JavaScript
        //       print('Payment Status: ${message.message}');
        //       if (message.message == 'CONCLUDED') {
        //         // Navigate to success page or show success message
        //       } else if (message.message == 'USER_CANCEL') {
        //         // Navigate to cancellation page or show cancellation message
        //       } else {
        //         // Handle other statuses or errors
        //       }
        //     },
        //   ),
        // },
      ),
    );
  }
}
 */