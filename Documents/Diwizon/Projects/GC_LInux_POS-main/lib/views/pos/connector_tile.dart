import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';

import '../../models/ChargerCapacity.dart';
import '../../models/Connector.dart';
import '../../models/ConnectorType.dart';

class ConnectorTile extends StatelessWidget {
  const ConnectorTile({
    super.key,
    required this.connectorType,
    required this.capacity,
    required this.connector,
  });

  final ConnectorType? connectorType;
  final ChargerCapacity? capacity;
  final Connector? connector;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Material(
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        color: Colors.white,
        child: InkWell(
          child: ListTile(
            shape: RoundedRectangleBorder(
              side: const BorderSide(color: Colors.grey, width: 1),
              borderRadius: BorderRadius.circular(5),
            ),
            leading: Image.asset(
              connectorType == null
                  ? "assets/connectors/ccs2_dc.png"
                  : "assets/connectors/${connectorType?.name.replaceAll("/", ":").toLowerCase()}${capacity?.isDC ?? true ? "_dc" : ""}.png",
              height: 48,
              width: 48,
              errorBuilder: (context, error, stackTrace) =>
                  const Icon(CupertinoIcons.circle_grid_hex),
            ),
            title: Text("Connector ${connector?.connector_number}"),
            // "${Get.find<AppCtrl>().connectorTypes.firstWhereOrNull((element) => connectorsOfCharger[idx].connectorConnectorTypesRelId == element.id)?.name ?? "-"} ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.charger_capacity?.toInt() ?? "-"}"),
            // "${Get.find<AppCtrl>().connectorTypes.firstWhereOrNull((element) => connectorsOfCharger[idx].connectorConnectorTypesRelId == element.id)?.name ?? "-"} ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.charger_capacity ?? "-"}"),
            subtitle: Text(connector?.connectorStatus ?? "-"),
            // subtitle: Text(charger.chargingPointId ?? ""),
            // title: const Text("GUJ DC 024"),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    /*   const Text(""),
                    Container(
                      padding: const EdgeInsets.fromLTRB(
                          2, 0, 5, 0),
                      decoration: BoxDecoration(
                          color: appColor.withOpacity(.1),
                          borderRadius:
                              BorderRadius.circular(24)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            'assets/s.png',
                            height: 18,
                          ),
                          Text(
                            connectorsOfCharger[idx]
                                    .connector_number
                                    ?.toString() ??
                                "",
                            style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                color: appColor),
                          ),
                        ],
                      ),
                    ), */
                    Text(Get.find<AppCtrl>()
                            .connectorTypes
                            .firstWhereOrNull(
                                (element) => connector?.typeId == element.id)
                            ?.name ??
                        "-"),
                  ],
                ),
              ],
            ),
            onTap: () => {},
          ),
        ),
      ),
    );
  }
}
