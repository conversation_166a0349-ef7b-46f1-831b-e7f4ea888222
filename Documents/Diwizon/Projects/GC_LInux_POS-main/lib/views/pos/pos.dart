import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/controllers/auth_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/methods.dart';
import 'package:go_charge/utils/payment_handler.dart';
import 'package:go_charge/utils/payment_page.dart';
import 'package:go_charge/utils/test_page.dart';
import 'package:go_charge/views/other/app_logo.dart';
import 'package:go_charge/views/station/detail_page.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

class POSWid extends StatefulWidget {
  const POSWid({super.key});

  @override
  State<POSWid> createState() => _POSWidState();
}

class _POSWidState extends State<POSWid> {
  @override
  Widget build(BuildContext context) {
    // return IframeExample();
    // return WebpageInHtmlElementView(webpageUrl: "https://www.google.com");

    return GetBuilder<AppCtrl>(builder: (ctrl) {
      return Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: InkWell(
              onTap: () async {
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //       builder: (context) =>
                //           WebViewExample(paymentUrl: "https://www.google.com")),
                // );
                // return;

                // PaymentHandler.initiatePhonePePayment(
                //     context: context,
                //     transactionId: generateId(12).toUpperCase(),
                //     amount: 100,
                //     mobileNumber: '+916353817704');

                //    PaymentHandler.initPayment(
                // transId: generateId(8),
                // context: context,
                // mode: PaymentModes.payPage,
                // mobileNumber: "+916353817704",
                // callbackUrl: PhonePe.paymentRoute,
                // amount: 100);
              },
              onLongPress: () => onTapFunc(context),
              child: const AppLogo(width: 40)),
          // actions: [
          //   Expanded(
          //     child: SingleChildScrollView(
          //       scrollDirection: Axis.horizontal,
          //       child: Row(
          //         children: List.generate(
          //           ctrl.chargings.length,
          //           (index) => ChargingCircle(charging: ctrl.chargings[index]),
          //         ),
          //       ),
          //     ),
          //   )
          // ],
        ),
        body: Center(
          child: ctrl.charger == null
              ? const CircularProgressIndicator.adaptive()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const Spacer(),
                    const SizedBox(height: 20),
                    // const Padding(
                    //   padding: EdgeInsets.all(12.0),
                    //   child: Text(
                    //     "Select connector to start",
                    //     style: TextStyle(color: appColor),
                    //   ),
                    // ),
                    if (ctrl.station != null)
                      Expanded(child: DetailPage(station: ctrl.station!)),
                  ],
                ),
        ),
      );
    });
  }

  onTapFunc(BuildContext context) async {
    final res = await showDialog<String>(
        context: context, builder: (context) => SettingDialog());
    if (res?.isNotEmpty ?? false) {
      setLocalChargerId(res!.trim());
    }
  }
}

class SettingDialog extends StatefulWidget {
  const SettingDialog({super.key});

  @override
  State<SettingDialog> createState() => _SettingDialogState();
}

class _SettingDialogState extends State<SettingDialog> {
  bool authenticated = false;
  final passwordCtrl = TextEditingController();
  final chargeIdCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Authenticate to access"),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: authenticated
            ? [
                TextField(
                  controller: chargeIdCtrl,
                  decoration: InputDecoration(labelText: "Charger Id"),
                )
              ]
            : [
                TextField(
                  controller: passwordCtrl,
                  decoration: InputDecoration(labelText: "Enter Password"),
                )
              ],
      ),
      actions: authenticated
          ? [
              TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text("Cancel")),
              TextButton(
                  onPressed: () => Navigator.of(context).pop(chargeIdCtrl.text),
                  child: Text("Confirm"))
            ]
          : [
              TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text("Cancel")),
              TextButton(
                  onPressed: () {
                    if (passwordCtrl.text == "1234") {
                      setState(() => authenticated = true);
                    }
                  },
                  child: Text("Continue")),
            ],
    );
  }
}

class ChargingCircle extends StatelessWidget {
  const ChargingCircle({
    super.key,
    required this.charging,
  });
  final ChargingTable charging;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: FloatingActionButton.extended(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.scale(
              scale: 2.0,
              child: Image.asset(
                'assets/charging2.gif',
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),
            Text('${charging.charging_percent ?? "-"}%'),
          ],
        ),
        onPressed: () => AppRoutes.goToChargingDetails(context, charging.id),
      ),
    );
  }
}
