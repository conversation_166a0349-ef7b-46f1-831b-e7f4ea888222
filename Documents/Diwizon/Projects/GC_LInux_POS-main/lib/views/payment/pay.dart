import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../theme/theme.dart';

class Pay extends StatefulWidget {
  const Pay({super.key});

  @override
  State<Pay> createState() => _PayState();
}

class _PayState extends State<Pay> {
  List<String> mode = <String>["wallet", "card", "UPI"];
  final int totalamount = 2200;
  String intialmode = "wallet";
  @override
  Widget build(BuildContext context) {
    var buttonStyle2 =
        const ButtonStyle(backgroundColor: WidgetStatePropertyAll(appColor));
    var textStyle = TextStyle(
        fontSize: 14.h,
        color: const Color(0xff828282),
        fontWeight: FontWeight.w500);
    var textStyle2 = TextStyle(fontSize: 14.h, fontWeight: FontWeight.w600);
    var textStyle3 = TextStyle(
        fontSize: 18.h,
        fontWeight: FontWeight.w500,
        color: const Color(0xff4F4F4F));
    return Scaffold(
      body: Column(children: [
        AppBar(
            backgroundColor: const Color(0xffE1FFE4),
            title: Text(
              "Payment",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18.h),
            )),
        Padding(
          padding: EdgeInsets.all(20.0.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color.fromARGB(24, 52, 51, 51),
                    ),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: EdgeInsets.all(10.0.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("ORDER DETAILS",
                          style: TextStyle(
                              fontSize: 14.h, fontWeight: FontWeight.w600)),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text("Start Time", style: textStyle),
                          const Spacer(),
                          Text(
                            "6:11 PM",
                            style: textStyle2,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text("End Time", style: textStyle),
                          const Spacer(),
                          Text(
                            "6:11 PM",
                            style: textStyle2,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text("Duration", style: textStyle),
                          const Spacer(),
                          Text(
                            "6:11 PM",
                            style: textStyle2,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text("Estimated Consumption (kwh)", style: textStyle),
                          const Spacer(),
                          Text(
                            "6:11 PM",
                            style: textStyle2,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Text("Estimated Price (Incl GST)", style: textStyle),
                          const Spacer(),
                          Text(
                            "6:11 PM",
                            style: textStyle2,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                "Select Payment Mode",
                style: TextStyle(
                    color: const Color(0xff4F4F4F),
                    fontSize: 18.h,
                    fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 20.h),
              Container(
                  decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color.fromARGB(24, 52, 51, 51),
                      ),
                      borderRadius: BorderRadius.circular(5)),
                  child: Padding(
                    padding: EdgeInsets.all(12.0.h),
                    child: Column(
                      children: [
                        InkWell(
                          child: Row(
                            children: [
                              Radio(
                                  value: mode[0],
                                  groupValue: intialmode,
                                  onChanged: (value) {
                                    intialmode = value!;
                                    if (mounted) setState(() {});
                                  }),
                              Text.rich(TextSpan(children: [
                                TextSpan(
                                    text: "Wallet balance ", style: textStyle3),
                                TextSpan(
                                    text: "(₹2,500)",
                                    style:
                                        textStyle3.copyWith(color: appColor)),
                              ]))
                            ],
                          ),
                          onTap: () {
                            intialmode = mode[0];
                            if (mounted) setState(() {});
                          },
                        ),
                        if (intialmode == mode[0])
                          ConfirmPay(
                              totalamount: totalamount,
                              buttonStyle2: buttonStyle2),
                        SizedBox(height: 20.h),
                        InkWell(
                          child: Row(
                            children: [
                              Radio(
                                  value: mode[1],
                                  groupValue: intialmode,
                                  onChanged: (value) {
                                    intialmode = value!;
                                    if (mounted) setState(() {});
                                  }),
                              Text("Credit / Debit / ATM Card",
                                  style: textStyle3)
                            ],
                          ),
                          onTap: () {
                            intialmode = mode[1];
                            if (mounted) setState(() {});
                          },
                        ),
                        if (intialmode == mode[1])
                          ConfirmPay(
                              totalamount: totalamount,
                              buttonStyle2: buttonStyle2),
                        SizedBox(height: 20.h),
                        InkWell(
                          child: Row(
                            children: [
                              Radio(
                                  value: mode[2],
                                  groupValue: intialmode,
                                  onChanged: (value) {
                                    intialmode = value!;
                                    if (mounted) setState(() {});
                                  }),
                              Text("UPI", style: textStyle3)
                            ],
                          ),
                        ),
                        if (intialmode == mode[2])
                          ConfirmPay(
                              totalamount: totalamount,
                              buttonStyle2: buttonStyle2),
                      ],
                    ),
                  )),
            ],
          ),
        )
      ]),
    );
  }
}

class ConfirmPay extends StatelessWidget {
  const ConfirmPay({
    super.key,
    required this.totalamount,
    required this.buttonStyle2,
  });

  final int totalamount;
  final ButtonStyle buttonStyle2;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(10.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 10.h),
          Text(
            "₹$totalamount.00",
            style: TextStyle(
                fontSize: 22.h,
                fontWeight: FontWeight.w500,
                color: const Color(0xff4F4F4F)),
          ),
          SizedBox(height: 30.h),
          Row(children: [
            Expanded(
              child: ElevatedButton(
                  onPressed: () {},
                  style: buttonStyle2.copyWith(
                      shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5.r)))),
                  child: Padding(
                    padding: EdgeInsets.all(8.0.h),
                    child: const Text(
                      "Confirm and Proceed",
                      style: TextStyle(color: Colors.white),
                    ),
                  )),
            ),
          ]),
          SizedBox(height: 10.h),
        ],
      ),
    );
  }
}
