import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/views/Home/station_tile.dart';
import 'package:go_charge/views/other/qr_button.dart';

import '../../shared/methods.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final searchCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: searchCtrl,
          onChanged: (value) => mounted ? setState(() {}) : null,
          autofocus: true,
          decoration: const InputDecoration(
              border: InputBorder.none, hintText: "Type someting here..."),
        ),
        actions: const [QrScanButton()],
      ),
      body: GetBuilder<AppCtrl>(
        builder: (_) {
          final filteredList = _.stations
              .where((element) => (element.station_name
                  .toLowerCase()
                  .contains(searchCtrl.text.toLowerCase())))
              .toList();
          return ListView.builder(
            itemCount: filteredList.length,
            padding: const EdgeInsets.all(12),
            itemBuilder: (context, index) {
              return StationTile(station: filteredList[index]);
            },
          );
        },
      ),
    );
  }
}
