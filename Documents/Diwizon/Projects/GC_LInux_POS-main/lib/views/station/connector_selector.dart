import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/Charger.dart';
import 'package:go_charge/models/ChargingTable.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/views/other/loader_ripple.dart';
import 'package:go_charge/views/station/detail_page.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/Connector.dart';

class ConnectorSelector extends StatelessWidget {
  const ConnectorSelector(
      {super.key,
      required this.onSelect,
      required this.connectors,
      required this.chargers,
      required this.selectedConnector,
      required this.chargings});

  final Connector? selectedConnector;
  final List<Connector> connectors;
  final List<Charger> chargers;
  final List<ChargingTable> chargings;
  final Function(Connector i) onSelect;

  @override
  Widget build(BuildContext context) {
    return connectors.isEmpty
        ? const LoaderRipple(size: 200)
        : ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: chargers.length,
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              final charger = chargers[index];
              final connectorsOfCharger = connectors
                  .where((element) => element.chargerId == charger.id)
                  .toList();
              connectorsOfCharger.sort(
                  (a, b) => a.connector_number.compareTo(b.connector_number));
              final chargingOfCharger = chargings.firstWhereOrNull(
                  (element) => element.chargerId == charger.id);
              return Container(
                decoration: BoxDecoration(
                    // color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(7)),
                margin: EdgeInsets.only(
                  bottom: 4,
                ),
                padding: const EdgeInsets.only(
                  bottom: 4,
                  top: 4,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0.0),
                      child: Row(
                        children: [
                          SizedBox(width: 5.h),
                          Image.asset(
                            getImage(charger.status ?? ""),
                            height: 18.h,
                          ),
                          SizedBox(width: 10.h),
                          Expanded(
                            child: Text.rich(
                              TextSpan(
                                text: charger.chargingPointId,
                                style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold),
                                children: [
                                  TextSpan(
                                      text:
                                          '  ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.capacityId)?.charger_capacity.toInt().toString() ?? "-"}',
                                      style: GoogleFonts.poppins(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 10.sp,
                                          color: Colors.grey.shade800)),
                                  TextSpan(
                                      text:
                                          ' ${(Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.capacityId)?.perKW ?? true) ? "KW" : "W"}',
                                      style: GoogleFonts.poppins(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 10.sp,
                                          color: Colors.grey.shade800)),
                                ],
                              ),
                            ),
                          ),
                          Text('₹${charger.pricePerKW}/kWh',
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w700,
                              )),
                        ],
                      ),
                      // child: Text(
                      //   '${charger.chargingPointId ?? "-"} ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.charger_capacity?.toInt() ?? "-"} ${(Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.perKW ?? true) ? "KW" : "W"}',
                      //   style: const TextStyle(fontWeight: FontWeight.bold),
                      // ),
                    ),
                    ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: connectorsOfCharger.length,
                      shrinkWrap: true,
                      physics: const ClampingScrollPhysics(),
                      itemBuilder: (context, idx) {
                        final connectorType = Get.find<AppCtrl>()
                            .connectorTypes
                            .firstWhereOrNull((element) =>
                                element.id == connectorsOfCharger[idx].typeId);
                        final capacity = Get.find<AppCtrl>()
                            .chargerCapacities
                            .firstWhereOrNull(
                                (element) => element.id == charger.capacityId);
                        bool isChargingOfConnector =
                            chargingOfCharger?.connector_no ==
                                connectorsOfCharger[idx].connector_number;
                        return Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 8.0.h,
                          ),
                          child: Material(
                            surfaceTintColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            color: Colors.white,
                            child: InkWell(
                              child: ListTile(
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 20.h, horizontal: 20.h),
                                shape: RoundedRectangleBorder(
                                  side: const BorderSide(
                                      color: Colors.grey, width: 1),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                leading: isChargingOfConnector
                                    ? Icon(CupertinoIcons.bolt_fill,
                                        color: Colors.green)
                                    : Image.asset(
                                        connectorType == null
                                            ? "assets/connectors/ccs2_dc.png"
                                            : "assets/connectors/${connectorType.name.replaceAll("/", ":").toLowerCase()}${capacity?.isDC ?? true ? "_dc" : ""}.png",
                                        height: 50.h,
                                        width: 50.h,
                                        errorBuilder: (context, error,
                                                stackTrace) =>
                                            const Icon(
                                                CupertinoIcons.circle_grid_hex),
                                      ),
                                title: Text(
                                    "Connector ${connectorsOfCharger[idx].connector_number}",
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w500,
                                    )),
                                // "${Get.find<AppCtrl>().connectorTypes.firstWhereOrNull((element) => connectorsOfCharger[idx].connectorConnectorTypesRelId == element.id)?.name ?? "-"} ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.charger_capacity?.toInt() ?? "-"}"),
                                // "${Get.find<AppCtrl>().connectorTypes.firstWhereOrNull((element) => connectorsOfCharger[idx].connectorConnectorTypesRelId == element.id)?.name ?? "-"} ${Get.find<AppCtrl>().chargerCapacities.firstWhereOrNull((element) => element.id == charger.chargerChargerCapacityRelId)?.charger_capacity ?? "-"}"),
                                subtitle: isChargingOfConnector
                                    ? Text(
                                        Get.find<AppCtrl>()
                                                .connectorTypes
                                                .firstWhereOrNull((element) =>
                                                    connectorsOfCharger[idx]
                                                        .typeId ==
                                                    element.id)
                                                ?.name ??
                                            "-",
                                        style: TextStyle(
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w600,
                                        ))
                                    : Text(
                                        (connectorsOfCharger[idx]
                                                .connectorStatus ??
                                            "-"),
                                        style: TextStyle(
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w600,
                                        )),
                                // subtitle: Text(charger.chargingPointId ?? ""),
                                // title: const Text("GUJ DC 024"),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        /*   const Text(""),
                                        Container(
                                          padding: const EdgeInsets.fromLTRB(
                                              2, 0, 5, 0),
                                          decoration: BoxDecoration(
                                              color: appColor.withOpacity(.1),
                                              borderRadius:
                                                  BorderRadius.circular(24)),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Image.asset(
                                                'assets/s.png',
                                                height: 18,
                                              ),
                                              Text(
                                                connectorsOfCharger[idx]
                                                        .connector_number
                                                        ?.toString() ??
                                                    "",
                                                style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                    color: appColor),
                                              ),
                                            ],
                                          ),
                                        ), */
                                        isChargingOfConnector
                                            ? Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 8.0),
                                                child: Text(
                                                  "${chargingOfCharger?.charging_percent ?? 0}%",
                                                  style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color: Colors.green),
                                                ),
                                              )
                                            : Text(
                                                Get.find<AppCtrl>()
                                                        .connectorTypes
                                                        .firstWhereOrNull(
                                                            (element) =>
                                                                connectorsOfCharger[
                                                                        idx]
                                                                    .typeId ==
                                                                element.id)
                                                        ?.name ??
                                                    "-",
                                                style: TextStyle(
                                                    fontSize: 10.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        Colors.grey.shade800)),
                                      ],
                                    ),
                                    isChargingOfConnector
                                        ? const Icon(
                                            Icons.arrow_forward_rounded,
                                            color: Colors.black87)
                                        : Radio(
                                            value: connectorsOfCharger[idx],
                                            groupValue: selectedConnector,
                                            onChanged: (value) => onSelect(
                                                value ??
                                                    connectorsOfCharger[idx])),
                                  ],
                                ),
                                onTap: isChargingOfConnector
                                    ? () => AppRoutes.goToChargingDetails(
                                        context, chargingOfCharger?.id ?? "")
                                    : () => onSelect(connectorsOfCharger[idx]),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
  }

  String getImage(String status) {
    switch (status) {
      case ChargerStatus.available:
        return 'assets/cs.png';
      case ChargerStatus.notAvailable:
        return 'assets/cs-grey.png';
      case ChargerStatus.busy:
        return 'assets/cs-orange.png';
      case ChargerStatus.faulted:
        return 'assets/cs-red.png';
      default:
        return 'assets/cs.png';
    }
  }
}
