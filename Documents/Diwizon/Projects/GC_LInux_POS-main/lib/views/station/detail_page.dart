import 'dart:async';
import 'package:amplify_api/amplify_api.dart' as amplify;
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/CPO.dart';
import 'package:go_charge/models/Charger.dart';
import 'package:go_charge/models/Connector.dart';
import 'package:go_charge/models/Station.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/methods.dart';
import 'package:go_charge/theme/theme.dart';
import 'package:go_charge/views/other/sheet_handle.dart';
import 'package:go_charge/views/station/step_selector.dart';
import '../../constants/const.dart';
import '../../controllers/booking_ctrl.dart';
import '../../controllers/vehicles_ctrl.dart';
import '../../models/Taxes.dart';
import '../../models/UserVehicle.dart';
import '../../shared/shimmer_tile.dart';
import '../../shared/snackbar.dart';
import '../../utils/payment_gateway.dart';
import 'bottom_bar.dart';
import 'charge_by_selector.dart';
import 'connector_selector.dart';
import 'wids.dart';
import 'top_buttons.dart';
import 'vehicle_selection.dart';

class DetailPage extends StatefulWidget {
  const DetailPage(
      {super.key,
      required this.station,
      this.chargingPointId,
      this.connectorNo});
  final Station station;
  final String? chargingPointId;
  final String? connectorNo;
  @override
  State<DetailPage> createState() => _DetailPageState();
}

class _DetailPageState extends State<DetailPage> {
  bool loading = false;
  bool fullLoader = false;
  StreamSubscription<amplify.GraphQLResponse<Charger>>? chargersStream;
  StreamSubscription<amplify.GraphQLResponse<Connector>>? connectorsStream;
  // StreamSubscription<QuerySnapshot<Charger>>? _chargersStream;
  // List<StreamSubscription<QuerySnapshot<Connector>>?> _connectorsStream = [];
  UserVehicle? selectedVehicle;
  List<Charger> chargers = <Charger>[];
  List<Connector> connectors = <Connector>[];
  int selectedViewPage = 0;
  Connector? selectedConnector;
  // bool loadingConnectors = true;
  // Charge By
  double estimatedDuration = defaultDuration;
  num estimatedPrice = 0;
  double estimatedUnits = 0;
  int selectedChargeBy = 0;
  double sliderVal = defaultDuration;
  late TextEditingController amountCtrl;
  late TextEditingController unitsCtrl;
  late TextEditingController gstNoCtrl;
  late TextEditingController gstNameCtrl;
  late TextEditingController contactCtrl;
  CPO? cpo;
  Taxes? taxModel;
  num taxRate = 18;
  bool loadingCpoTax = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: fullLoader
          ? const Center(child: CircularProgressIndicator())
          : GetBuilder<AppCtrl>(builder: (ctrl) {
              print(
                  "------------___------------${ctrl.connectors.map((e) => e.connectorStatus).toList()}");
              final currentStation = ctrl.stations.firstWhereOrNull(
                      (element) => element.id == widget.station.id) ??
                  widget.station;
              chargers = ctrl.charger != null ? [ctrl.charger!] : [];
              connectors = ctrl.connectors;
              return Column(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        // const SheetHandle(),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // STATUS & HEART
                              StationStatusAndHeart(
                                // available: connectors.isEmpty
                                //     ? null
                                //     : connectors
                                //         .map((e) =>
                                //             e.connectorStatus)
                                //         .contains(ChargerStatus
                                //             .available),
                                stationId: currentStation.id,
                                isInFav: ctrl.currentUserData?.favs
                                        ?.contains(currentStation.id) ??
                                    false,
                                status: currentStation.status ?? "",
                              ),
                              // NAME
                              NameAndDistance(
                                  widget: widget,
                                  distance: "",
                                  stationId: currentStation.id),
                              // SizedBox(height: 10.h),
                              // BUTTONS
                              // StationButtons(currentStation),
                              // SizedBox(height: 25.h),
                              // STEPS TITLE
                              // const StepSelectorText(),
                              // SizedBox(height: 25.h),
                              // STEPS CONNECTOR
                              // StepSelectorConnector(
                              //     callback: _stepSelectorCallback,
                              //     selectedPage: selectedViewPage),
                              // // STEPS TEXTS
                              // StepSelectorTexts(
                              //     callback: _stepSelectorCallback),
                              if (selectedViewPage == 0)
                                // CONNECTOR SELECION
                                ctrl.connectors.isEmpty
                                    ? const Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 8.0),
                                        child: ShimmerListBuilder(count: 2))
                                    : ConnectorSelector(
                                        onSelect: (i) {
                                          if (![
                                            ChargerStatus.available,
                                            ChargerStatus.preparing
                                          ].contains(i.connectorStatus)) {
                                            showAppShackBar(
                                                "Selected connector is ${i.connectorStatus ?? ChargerStatus.notAvailable}");
                                            return;
                                          }
                                          selectedConnector = i;
                                          if (mounted) {
                                            setState(() {});
                                          }
                                        },
                                        chargings: ctrl.chargings,
                                        connectors: connectors,
                                        chargers: chargers,
                                        selectedConnector: selectedConnector,
                                      )
                              // else if (selectedViewPage == 1)
                              //   // VEHICLE SELECTION
                              //   SelectorVehicle(
                              //     selectedVehicle: selectedVehicle,
                              //     callback: (p0) => mounted
                              //         ? setState(() {
                              //             selectedVehicle = p0;
                              //           })
                              //         : null,
                              //     skipCallback: () => mounted
                              //         ? setState(() {
                              //             selectedVehicle = null;
                              //             selectedViewPage = 2;
                              //           })
                              //         : null,
                              //   )
                              else
                                // CHARGE BY
                                SelectorChargedBy(
                                  sliderVal: sliderVal,
                                  duration: estimatedDuration,
                                  price: estimatedPrice,
                                  selectedChargeBy: selectedChargeBy,
                                  amountCtrl: amountCtrl,
                                  unitsCtrl: unitsCtrl,
                                  setSelectedChargeBy: (int v) => mounted
                                      ? setState(() => selectedChargeBy = v)
                                      : null,
                                  onDurationChange: handleDurationChange,
                                  onAmountChange: handleAmountChange,
                                  onUnitChange: handleUnitChange,
                                  units: estimatedUnits,
                                )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }),
      bottomNavigationBar: StationBottombar(
        pageIndex: selectedViewPage,
        buttonActive: selectedConnector != null,
        loading: loading,
        callback: _greenButonCallbacks,
        onBookSlot: () => setState(() {
          goToSelector(0);
        }),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    amountCtrl = TextEditingController();
    unitsCtrl = TextEditingController();
    gstNoCtrl = TextEditingController();
    gstNameCtrl = TextEditingController();
    contactCtrl = TextEditingController();
    clearSnacks();
    // getChargers();
    setDefaultSelectedvehicle();
    getCpo();
    if (widget.chargingPointId != null && widget.connectorNo != null) {
      if (mounted) setState(() => fullLoader = true);
    }
  }

  getCpo() async {
    try {
      final request = ModelQueries.get(
          CPO.classType, CPOModelIdentifier(id: widget.station.cpoId ?? ""));
      final result = await Amplify.API.query(request: request).response;
      cpo = result.data;
      if (cpo != null) {
        final request = ModelQueries.get(
            Taxes.classType, TaxesModelIdentifier(id: cpo!.taxId ?? ""));
        final result = await Amplify.API.query(request: request).response;
        taxModel = result.data;
        taxRate = taxModel?.rate ?? taxRate;
      }
      if (mounted) setState(() => loadingCpoTax = false);
    } catch (e) {
      debugPrint(e.toString());
      if (mounted) setState(() => loadingCpoTax = false);
    }
  }

  setDefaultSelectedvehicle() =>
      selectedVehicle = Get.find<VehiclesCtrl>().defauldVehicle;

  getChargers() async {
    try {
      debugPrint("Getting chargers....${widget.station.id}");
      final request = amplify.ModelQueries.list(Charger.classType,
          where: Charger.STATIONID.eq(widget.station.id));
      final result = await Amplify.API.query(request: request).response;
      //
      safePrint('${result.data?.items.length} Chargers Found!');
      chargers.addAll(result.data?.items.whereType<Charger>() ?? []);
      getConnectors();
      observeStationChargers();
      observeStationConnectors();
      if (mounted) setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void observeStationChargers() {
    final subscriptionRequest = amplify.ModelSubscriptions.onUpdate(
        Charger.classType,
        where: Charger.STATIONID.eq(widget.station.id));
    // Stream
    final Stream<amplify.GraphQLResponse<Charger>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    chargersStream?.cancel();
    chargersStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received charger: ${event.data}');
        if (event.data != null) {
          chargers[chargers.indexWhere(
              (element) => element.id == event.data?.id)] = event.data!;
          // getConnectors();
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  getConnectors() async {
    try {
      debugPrint("Getting connectors....");
      connectors.clear();
      List<Connector> connectorsTemp = <Connector>[];
      for (var charger in chargers) {
        final request = amplify.ModelQueries.list(Connector.classType,
            where: Connector.CHARGERID.eq(charger.id));
        final result = await Amplify.API.query(request: request).response;
        safePrint('${result.data?.items.length} Connectors Found!');
        // print(connectors.length);
        connectorsTemp.addAll(result.data?.items.whereType<Connector>() ?? []);
        // connectors.addAll(result.data?.items.whereType<Connector>() ?? []);
        // result.data?.items.whereType<Connector>().forEach((element) {
        //   final conInLst =
        //       connectors.firstWhereOrNull((ele) => ele.id == element.id);
        //   connectors.addIf(conInLst != null, conInLst!);
        // });
      }
      // loadingConnectors = false;
      connectors = connectorsTemp;
      if (mounted) setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void observeStationConnectors() {
    final subscriptionRequest =
        amplify.ModelSubscriptions.onUpdate(Connector.classType,
            where: QueryPredicateGroup(
                QueryPredicateGroupType.or,
                List.generate(
                  chargers.length,
                  (index) => Connector.CHARGERID.eq(chargers[index].id),
                )));
    // Stream
    final Stream<amplify.GraphQLResponse<Connector>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established connectors'),
    );
    // Listner
    connectorsStream?.cancel();
    connectorsStream = operation.listen(
      (event) async {
        safePrint('Subscription event data received connectors: ${event.data}');
        if (event.data != null) {
          connectors[connectors.indexWhere(
              (element) => element.id == event.data?.id)] = event.data!;
          if (selectedConnector?.id == event.data?.id) {
            selectedConnector = event.data;
          }
          if (![ChargerStatus.available, ChargerStatus.preparing]
              .contains(selectedConnector?.connectorStatus)) {
            selectedConnector = null;
          }
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) =>
          safePrint('Error in subscription stream connectors: $e'),
    );
  }

  handleUnitChange(v) {
    final price = _unitsToPrice(v);
    // print(price);
    if (price != null) setEstimationsFromPrice(price, original: true);
  }

  handleAmountChange(String v) {
    final price = num.tryParse(v);
    if (price != null) setEstimationsFromPrice(price, original: true);
  }

  handleDurationChange(v) {
    sliderVal = v;
    estimatedDuration = v;
    final price = _durationToPrice(v);
    if (price != null) setEstimationsFromPrice(price, original: true);
  }

  void setEstimationsFromPrice(num price, {bool original = false}) {
    final originalCost = original ? price : price * 100 / (100 + taxRate);
    final capacityPerMin = _getConnectorCapacity();
    if (capacityPerMin != null) {
      estimatedUnits = originalCost / (selectedCharger?.pricePerKW ?? 20);
      estimatedDuration = estimatedUnits / capacityPerMin;
      // unitsCtrl.text = estimatedUnits.toStringAsFixed(1);
      sliderVal = estimatedDuration;
      final priceCalculated =
          estimatedUnits * (selectedCharger?.pricePerKW ?? 20);
      final taxToPay = (taxModel?.rate ?? 0) / 100 * priceCalculated;
      estimatedPrice = original ? price : priceCalculated + taxToPay;
      // amountCtrl.text = estimatedPrice.toStringAsFixed(2);
      if (mounted) setState(() {});
    }
  }

  num? _unitsToPrice(v) {
    // Entered Amount
    num enteredUnits = num.tryParse(v) ?? 20;
    estimatedUnits = enteredUnits.toDouble();
    // Selected Charger
    final selecttedCharger = chargers.firstWhereOrNull(
        (element) => element.id == selectedConnector?.chargerId);
    if (selecttedCharger != null) {
      // Charger Capacity
      final capacity = Get.find<AppCtrl>()
          .chargerCapacities
          .firstWhereOrNull(
              (element) => element.id == selecttedCharger.capacityId)
          ?.charger_capacity;
      if (capacity != null) {
        final priceCalculated = enteredUnits * (selecttedCharger.pricePerKW);
        // final taxToPay = (taxes?.rate ?? 0) / 100 * priceCalculated;
        return priceCalculated;
      }
    }
    return null;
  }

  num? _durationToPrice(v) {
    sliderVal = v;
    estimatedDuration = v;
    final connectorCapacity = _getConnectorCapacity();
    if (connectorCapacity != null) {
      // Estimated Units
      final estimatedUnits = connectorCapacity * estimatedDuration;
      // Estimated Price
      final priceCalculated = estimatedUnits * (selectedCharger!.pricePerKW);
      // final taxToPay = (taxes?.rate ?? 0) / 100 * priceCalculated;
      return priceCalculated;
    }
    return null;
  }

  num? _getConnectorCapacity() {
    if (selectedCharger != null) {
      // Charger Capacity
      final capacity = Get.find<AppCtrl>()
          .chargerCapacities
          .firstWhereOrNull(
              (element) => element.id == selectedCharger!.capacityId)
          ?.charger_capacity;
      if (capacity != null) {
        // One connector capacity
        final connectorCapacity = capacity /
            connectors
                .where((element) => element.chargerId == selectedCharger!.id)
                .length;
        // Capacity Per Min
        return (connectorCapacity / 60);
      }
    }
    return null;
  }

  Charger? get selectedCharger => chargers.firstWhereOrNull(
      (element) => element.id == selectedConnector?.chargerId);

  // CALLBACK WHEN SELECTOR IS TAPPED
  _stepSelectorCallback(i) {
    switch (i) {
      case 1: // TAPPED ON VEHICLE
        _onVehicleTap();
        break;
      case 2: // TAPPED ON CHARGE BY
        _onChargeByTap();
        break;
      default:
        _onConnectorTap();
    }
  }

  // BOTTOM BAR BUTTONS CALLBACK
  _greenButonCallbacks() {
    if (![ChargerStatus.available, ChargerStatus.preparing]
        .contains(selectedConnector?.connectorStatus)) {
      selectedConnector = null;
      showAppShackBar(
          "Selected connector is ${selectedConnector?.connectorStatus ?? ChargerStatus.notAvailable}");
      return null;
    }
    switch (selectedViewPage) {
      case 0: // BCOZ NEXT IS VEHICLE
        _onVehicleTap();
        break;
      // case 1: // BCOZ NEXT IS CHARGE BY
      //   _onChargeByTap();
      //   break;
      default:
        _onChargeNow();
    }
  }

  _onChargeNow() async {
    if (estimatedPrice <= 0) {
      showAppShackBar("Please enter a valid amount!");
      return;
    }
    if (![ChargerStatus.available, ChargerStatus.preparing]
        .contains(selectedConnector?.connectorStatus)) {
      selectedConnector = null;
      showAppShackBar(
          "Selected connector is ${selectedConnector?.connectorStatus ?? ChargerStatus.notAvailable}");
      return null;
    }
    AppRoutes.goToCheckout(
      context,
      station: widget.station,
      selectedVehicle: selectedVehicle,
      selectedCharge: chargers.firstWhereOrNull(
          (element) => element.id == selectedConnector?.chargerId),
      selectedConnector: selectedConnector,
      estimatedTime: estimatedDuration,
      estimatedUnits: estimatedUnits,
      estimatedAmount: estimatedPrice,
      paymentCallback: onPayNow,
      walletCallback: onWallet,
      taxPercent: taxRate,
      gstNoCtrl: gstNoCtrl,
      gstNameCtrl: gstNameCtrl,
      contactCtrl: contactCtrl,
    );
    return;
  }

  onWallet() async {
    try {
      final ctrl = Get.find<AppCtrl>();
      if ((ctrl.currentUserData?.balance ?? 0) == 0) {
        showAppShackBar("Insufficient Balance");
        return;
      }
      if ((ctrl.currentUserData?.balance ?? 0) < estimatedPrice) {
        showAppShackBar("Insufficient Balance");
        return;
      }
      final taxAmount = (taxModel?.rate ?? 0) / 100 * estimatedPrice;
      final payableAmount = estimatedPrice;
      // if (selectedVehicle != null) {
      final pgTransRef = getRandomId(34).toUpperCase();
      final bId = await Get.find<BookingCtrl>().createNewBooking2(
        pgTransRef: pgTransRef,
        taxPercent: taxRate,
        charger: chargers.firstWhereOrNull(
            (element) => element.id == selectedConnector?.chargerId),
        userVehicle: selectedVehicle,
        startAt: DateTime.now(),
        estimatedDuration: estimatedDuration,
        selectedConnector: selectedConnector,
        station: widget.station,
        bookingType: getBoookingTypeString(selectedChargeBy),
        estimatedPrice: estimatedPrice,
        estimatedUnits: estimatedUnits,
        taxAmount: taxAmount,
        amountFromWallet: payableAmount,
        fromWallet: true,
        gstNo: gstNoCtrl.text,
        gstName: gstNameCtrl.text,
        cpo: cpo,
        contact: contactCtrl.text.isEmpty ? null : contactCtrl.text,
      );
      if (bId != null) {
        debugPrint("Boooking done");
        if (mounted) AppRoutes.goToChargingDetails(context, bId);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  onPayNow(num amountFromWallet) async {
    try {
      if (loading) return;
      if (estimatedPrice == 0 ||
          estimatedUnits == 0 ||
          estimatedDuration == 0) {
        showAppShackBar("Please, select valid option!");
        return;
      }

      final taxAmount = (taxModel?.rate ?? 0) / 100 * estimatedPrice;
      // if (selectedVehicle != null) {
      final pgTransRef = getRandomId(34).toUpperCase();
      final bId = await Get.find<BookingCtrl>().createNewBooking2(
        contact: contactCtrl.text.isEmpty ? null : contactCtrl.text,
        pgTransRef: pgTransRef,
        taxPercent: taxRate,
        cpo: cpo,
        charger: chargers.firstWhereOrNull(
            (element) => element.id == selectedConnector?.chargerId),
        userVehicle: selectedVehicle,
        startAt: DateTime.now(),
        estimatedDuration: estimatedDuration,
        selectedConnector: selectedConnector,
        station: widget.station,
        bookingType: getBoookingTypeString(selectedChargeBy),
        estimatedPrice: estimatedPrice,
        estimatedUnits: estimatedUnits,
        taxAmount: taxAmount,
        amountFromWallet: 0, //amountFromWallet,
        fromWallet: false,
        gstNo: gstNoCtrl.text,
        gstName: gstNameCtrl.text,
      );
      // return;
      if (bId != null) {
        //
        PaymentGateway.initPhonePePayment(
          amount: (amountFromWallet > 0
                  ? (estimatedPrice -
                      (Get.find<AppCtrl>().currentUserData?.balance ?? 0))
                  : estimatedPrice.toInt()) *
              100,
          receipt: pgTransRef,
          mobileNumber: Get.find<AppCtrl>().currentUserData?.contact ?? "",
          isWalletTopup: false,
          handlePaymentErrorResponse: () {
            if (mounted) setState(() => loading = false);
            showAppShackBar("Payment Failed!");
            if (mounted) setState(() => loading = false);
          },
          handlePaymentSuccessResponse: () {
            if (mounted) setState(() => loading = false);
            showAppShackBar("Payment was successfull!");
            AppRoutes.goToChargingDetails(context, bId);
            // getTransaction();
          },
        );
        // if (mounted) AppRoutes.goToChargingDetails(context, bId);
        // _resetFields();
        // return;

        // PaymentGateway.initPhonePePayment(
        //   isWalletTopup: false,
        //   handlePaymentErrorResponse: () {
        //     showAppShackBar("Payment Failed!");
        //     if (mounted) setState(() => loading = false);
        //   },
        //   handlePaymentSuccessResponse: () {
        //     if (mounted) setState(() => loading = false);
        //     showAppShackBar("Payment was successfull!");
        //     AppRoutes.goToChargingDetails(context, bId);
        //   },
        //   receipt: pgTransRef,
        //   amount: (amountFromWallet > 0
        //           ? (estimatedPrice -
        //               (Get.find<AppCtrl>().currentUserData?.balance ?? 0))
        //           : estimatedPrice.toInt()) *
        //       100,
        //   mobileNumber: Get.find<AppCtrl>().currentUserData?.contact ?? "",
        // );
        _resetFields();
        /* final resp = await APIManager.createPGOrder(
          amount: (amountFromWallet > 0
                  ? (estimatedPrice -
                      (Get.find<AppCtrl>().currentUserData?.balance ?? 0))
                  : estimatedPrice.toInt()) *
              100,
          receipt: bId,
          bId: bId,
          uId: Get.find<AppCtrl>().currentAuthUser!.userId,
          type: false ? "Wallet" : "Booking",
        );
        if (resp != null) {
          RazorPayPG.initRazorPayment(
              pgOrder: resp,
              isWalletTopup: false,
              handlePaymentErrorResponse: (d) {
                showAppShackBar("Payment Failed!");
                if (mounted) setState(() => loading = false);
              },
              handlePaymentSuccessResponse: (d) {
                if (mounted) setState(() => loading = false);
                showAppShackBar("Payment was successfull!");
                AppRoutes.goToChargingDetails(context, bId);
              },
              handleExternalWalletSelected: (d) {
                showAppShackBar("External Wallet Selected");
                if (mounted) setState(() => loading = false);
              });
        } */
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  bool isConnectorSelected() {
    final isIt = selectedConnector != null;
    if (!isIt) _showConnectorNotSelected();
    return isIt;
  }

  bool isVehicleSelected() {
    if (keepVehicleOptional) return true;
    final isIt = selectedVehicle != null;
    if (!isIt) _showVehicleNotSelected();
    return isIt;
  }

  // BACK TO CONNECTORS
  _onConnectorTap() {
    goToSelector(0);
  }

  // GOING TO VEHICLES
  _onVehicleTap() {
    if (!_selectedChargerIsCapacity()) return;
    if (isConnectorSelected()) goToSelector(1);
    handleDurationChange(estimatedDuration);
  }

  // SELECTED CHARGER HAS CAPACITY
  _selectedChargerIsCapacity() {
    if (selectedCharger?.capacityId == null) {
      showAppShackBar("Can't charge with selected charger!");
      return false;
    }
    return true;
  }

  // GOING TO CHARGE BY
  _onChargeByTap() {
    if (!_selectedChargerIsCapacity()) return;
    if (isConnectorSelected()) if (isVehicleSelected()) goToSelector(2);
  }

  // SNACK FOR VEHICLE NOT SELECTED
  _showVehicleNotSelected() => showAppShackBar("Please, choose your vehicle!");

  // SNACK FOR CONNECTOR NOT SELECTED
  _showConnectorNotSelected() =>
      showAppShackBar("Please, select a connector type!");

  // NEXT
  goToNextSelector() => mounted ? setState(() => selectedViewPage++) : null;

  // TO SPECIFIC SELECTOR
  goToSelector(int i) => mounted ? setState(() => selectedViewPage = i) : null;

  _resetFields() {
    amountCtrl.clear();
    unitsCtrl.clear();
    gstNameCtrl.clear();
    gstNoCtrl.clear();
    contactCtrl.clear();
    goToSelector(0);
  }

  @override
  void dispose() {
    super.dispose();
    amountCtrl.dispose();
    unitsCtrl.dispose();
    gstNoCtrl.dispose();
    gstNameCtrl.dispose();
    contactCtrl.dispose();
    chargersStream?.cancel();
    connectorsStream?.cancel();
  }
}

String getBoookingTypeString(int selectedChargeByType) {
  switch (selectedChargeByType) {
    case 0:
      return ChargByTypes.amount;
    case 1:
      return ChargByTypes.unit;
    case 2:
      return ChargByTypes.duration;
    default:
      return ChargByTypes.amount;
  }
}

class ChargerStatus {
  static const available = "Available";
  static const preparing = "Preparing";
  static const charging = "Charging";
  static const notAvailable = "Not Available";
  static const busy = "Busy";
  static const faulted = "Faulted";
}

Color getStatusColor(String str) {
  switch (str) {
    case ChargerStatus.busy:
      return Colors.orange;
    case ChargerStatus.faulted:
      return Colors.red;
    case ChargerStatus.notAvailable:
      return Colors.grey.shade700;
    default:
      return appColor;
  }
}
