import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/shared/loader.dart';
import 'package:go_charge/shared/snackbar.dart';
import '../../theme/theme.dart';
import 'detail_page.dart';

class StationStatusAndHeart extends StatelessWidget {
  const StationStatusAndHeart(
      {super.key,
      required this.stationId,
      required this.isInFav,
      // required this.available,
      required this.status});

  final String stationId;
  final bool isInFav;
  // final bool? available;
  final String status;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.power_settings_new,
          color: getStatusColor(status),
          size: 24.h,
        ),
        SizedBox(width: 3.h),
        Text(
          status,
          style: TextStyle(
              color: getStatusColor(status), fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        // _FavouriteButton(stationId, isInFav),
      ],
    );
  }
}

class _FavouriteButton extends StatefulWidget {
  const _FavouriteButton(this.stationId, this.isInFav);
  final String stationId;
  final bool isInFav;

  @override
  State<_FavouriteButton> createState() => _FavouriteButtonState();
}

class _FavouriteButtonState extends State<_FavouriteButton> {
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return IconButton(
        onPressed: () async {
          if (loading) return;
          if (mounted) setState(() => loading = true);
          final res = await toggleFavourite(widget.stationId);
          if (res == true) {
            showAppShackBar(!widget.isInFav
                ? "Station added to favoutites!"
                : "Station removed from favoutites!");
          }
          if (mounted) setState(() => loading = false);
        },
        icon: loading
            ? const AppLoader()
            : Icon(
                !widget.isInFav
                    ? CupertinoIcons.heart
                    : CupertinoIcons.heart_fill,
                color: appColor,
              ));
  }
}

class CustomGreenCircle extends StatelessWidget {
  const CustomGreenCircle({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 18.h,
      width: 18.h,
      decoration: BoxDecoration(
          border: Border.all(color: appColor),
          borderRadius: BorderRadius.circular(15.r)),
      child: Center(
        child: Icon(
          Icons.circle,
          size: 15.h,
          color: appColor,
        ),
      ),
    );
  }
}

class NameAndDistance extends StatelessWidget {
  const NameAndDistance({
    super.key,
    required this.widget,
    required this.distance,
    required this.stationId,
  });

  final DetailPage widget;
  final String distance;
  final String stationId;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 250.w,
          child: Text(
            widget.station.station_name,
            style: TextStyle(fontSize: 20.h, fontWeight: FontWeight.w600),
          ),
        ),
        SizedBox(height: 10.h),
        // Hero(
        //   tag: '${stationId}d',
        //   child: Material(
        //     surfaceTintColor: Colors.white,
        //     color: Colors.white,
        //     child: Text(
        //       "Distance $distance",
        //       style: const TextStyle(color: Color(0xff828282)),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}

const whiteButton =
    ButtonStyle(backgroundColor: WidgetStatePropertyAll(Colors.white));


/* 
/* Amplify Params - DO NOT EDIT
	ENV
	REGION
Amplify Params - DO NOT EDIT */import { default as fetch, Request } from 'node-fetch';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

import AWS from 'aws-sdk';
import razorpay from 'razorpay';

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();


/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
let statusCode = 200;
export const handler = async (event) => {
  console.log(`Stop trans EVENT: ${JSON.stringify(event)}`);

  let body,
    response,
    data,
    MeterValue,
    document_Id,
    meterTime,
    booking_id,
    chargingId,
    pgTransRef,
    consumedWatt,
    transactionId,
    totalAmount,
    MeterStart,
    pricePerKw,
    dueAmount,
    chargerId,
    startPercent, stopPercent, stoppedAt, payment_Id, differenceAmount, charging_fee;
  let refundResponse, refrespon, amountFromWallet, userId, balance;

  try {

    const msgData = event.MSG_BODY;
    // let { CP_ID } = event;
    MeterValue = msgData.meterStop;
    booking_id = msgData.idTag;
    transactionId = msgData.transactionId;
    stoppedAt = msgData.timestamp;

    // Check for the presence of transactionData and process it
    if (msgData.transactionData && msgData.transactionData.length > 0) {
      msgData.transactionData[0].sampledValue.forEach(sampledValue => {
        if (sampledValue.context === "Transaction.Begin") {
          startPercent = sampledValue.value;
        } else if (sampledValue.context === "Transaction.End") {
          stopPercent = sampledValue.value;
        }
      });
    }


    console.log("MsG DatA", msgData);
    const params = {
      TableName: 'ChargingTable-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#transactionId = :transactionId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#transactionId': 'transactionId',
      },
      ExpressionAttributeValues: {
        ':transactionId': transactionId,
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    data = await dynamodb.scan(params).promise();
    console.log(data);
    if (data.Items.length > 0) {
      body = data.Items[0];
      chargingId = body.id;
      pgTransRef = body.pgTransRef;
      chargerId = body.chargerId;
      // if (body.status == "Inactive") return {
      //   statusCode,
      //   body: JSON.stringify(body)
      // };
      document_Id = body.id;
      MeterStart = body.MeterStartWatt;
      consumedWatt = (parseInt(MeterValue, 10) - parseInt(MeterStart, 10)) / 1000;
      pricePerKw = body.pricePerKw;
      payment_Id = body.payment_Id;
      charging_fee = body.charging_fee;
      amountFromWallet = body.amountFromWallet;
      userId = body.user_id;
    }

    totalAmount = parseFloat(consumedWatt * pricePerKw);

  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }

  differenceAmount = parseFloat(charging_fee - totalAmount);
  dueAmount = parseFloat(totalAmount - charging_fee);
  dueAmount = dueAmount > 0 ? dueAmount : 0;
  const originalCost = (totalAmount ?? 0) *
    100 /
    (100 + (body.taxPercent ?? 0));
  const appliedTaxAmount = (totalAmount ?? 0) - originalCost;

  console.log(originalCost, '-', appliedTaxAmount);

  const invoiceDetails = await getSetInvoiceNo(chargerId);

  const variables = {
    id: document_Id, MeterEndWatt: MeterValue, costOfConsump: totalAmount,
    status: 'Completed', unitsBurned: consumedWatt, dueAmount: dueAmount,
    invoiceNo: invoiceDetails['no'], invoiceId: invoiceDetails['id']
  };
  if (body.isIgst) {
    variables.igstAmount = appliedTaxAmount;
    variables.sgstAmount = 0;
    variables.cgstAmount = 0;
  } else {
    variables.igstAmount = 0;
    variables.sgstAmount = appliedTaxAmount / 2;
    variables.cgstAmount = appliedTaxAmount / 2;
  }

  // Conditionally add startPercent and stopPercent if not undefined
  if (startPercent !== undefined) {
    variables.startedAtPercent = startPercent;
  }
  if (stopPercent !== undefined) {
    variables.stopedAtPercent = stopPercent;
    variables.charging_percent = stopPercent.toString();
  }

  const query = startPercent !== undefined && stopPercent !== undefined ? /* GraphQL */`
  mutation MyMutation($id: ID!, $MeterEndWatt: Int!, $costOfConsump: Float!,$igstAmount: Float!,$sgstAmount: Float!,$cgstAmount: Float!, $status: String!, $startedAtPercent: Int!, $stopedAtPercent: Int!, $dueAmount: Float!,$unitsBurned: Float, $charging_percent: String!, $invoiceNo: Int, $invoiceId: String) {
    updateChargingTable(
      input: {id: $id, MeterEndWatt: $MeterEndWatt, costOfConsump: $costOfConsump,igstAmount: $igstAmount,sgstAmount: $sgstAmount,cgstAmount: $cgstAmount, status: $status, startedAtPercent: $startedAtPercent, stopedAtPercent: $stopedAtPercent, unitsBurned: $unitsBurned, dueAmount: $dueAmount,charging_percent: $charging_percent, invoiceNo: $invoiceNo,invoiceId: $invoiceId}
    ) {
      createdAt
      updatedAt
      id
      booking_id
      start_time
      end_time
      status
      connector_no
      CurrentMeterWatt
      city
      charging_fee
      payment_status
      createdAt
      tax_amount
      vehical_number
      chargePointId
      user_id
      station_id
      vehicle_id
      charging_percent
      MeterStartWatt
      MeterEndWatt
      booking_type
      compareValue
      pricePerKw
      geoState
      isPaid
      chargerId
      transactionId
      estimatedDuration
      estimatedUnits
      startedAtPercent
      stopedAtPercent
      unitsBurned
      costOfConsump
      refundedAmount
      startedAtTime
      stopedAtTime
      amountFromWallet
      transDocId
      payment_Id
      paymentTime
      lastCommand
      gstin
      userName
      userContact
      overchargeDueCleared
      invoiceNo
      dueAmount
      igstAmount
      sgstAmount
      cgstAmount
    }
  }`: /* GraphQL */`
  mutation MyMutation($id: ID!, $MeterEndWatt: Int!, $costOfConsump: Float!,$igstAmount: Float!,$sgstAmount: Float!,$cgstAmount: Float!, $status: String!, $dueAmount: Float!,$unitsBurned: Float, $invoiceNo: Int, $invoiceId: String) {
    updateChargingTable(
      input: {id: $id, MeterEndWatt: $MeterEndWatt, costOfConsump: $costOfConsump,igstAmount: $igstAmount,sgstAmount: $sgstAmount,cgstAmount: $cgstAmount, status: $status, unitsBurned: $unitsBurned, dueAmount: $dueAmount, invoiceNo: $invoiceNo,invoiceId: $invoiceId}
    ) {
      createdAt
      updatedAt
      id
      booking_id
      start_time
      end_time
      status
      connector_no
      CurrentMeterWatt
      city
      charging_fee
      payment_status
      createdAt
      tax_amount
      vehical_number
      chargePointId
      user_id
      station_id
      vehicle_id
      charging_percent
      MeterStartWatt
      MeterEndWatt
      booking_type
      compareValue
      pricePerKw
      geoState
      isPaid
      chargerId
      transactionId
      estimatedDuration
      estimatedUnits
      startedAtPercent
      stopedAtPercent
      unitsBurned
      costOfConsump
      refundedAmount
      startedAtTime
      stopedAtTime
      amountFromWallet
      transDocId
      payment_Id
      paymentTime
      lastCommand
      gstin
      userName
      userContact
      overchargeDueCleared
      invoiceNo
      dueAmount
      igstAmount
      sgstAmount
      cgstAmount
    }
  }`;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);

  try {
    console.log("Running Mutation...")
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.error(body);
  }

  const keyId = "***********************"; // "rzp_test_cn6p8HqmFvpWSW"
  const keySecret = "2IsMZvfk9kPz2iB4loELYUfj"; // "kouKRBYkLFI51GTS9ntQUX8f"
  const razorpayInstance = new razorpay({
    key_id: keyId,
    key_secret: keySecret
  });

  console.log("RefundedAmount is : ", body.refundedAmount, "-", body.refundedAmount != null, body.refundedAmount !== undefined, typeof body.refundedAmount);
  console.log("amountFromWallet !== null ", amountFromWallet !== null, "amountFromWallet !== 0", amountFromWallet !== 0)
  if (differenceAmount > 0 && !body.refundedAmount) {

    if (amountFromWallet !== null && amountFromWallet != 0) {
      console.log("In refund wallet..", differenceAmount, amountFromWallet);
      balance = differenceAmount + amountFromWallet;
      refrespon = await updateUserWallet(userId, differenceAmount, booking_id);
    } else {

      refrespon = await initRefund(pgTransRef, payment_Id, booking_id, differenceAmount);
    }
    console.log("Refund Response: ", refrespon);
  }

  return {
    statusCode,
    body: JSON.stringify(body)
  };
};




// const tmpStop = {
//   "idTag": "Brenbdra5kpk",
//   "meterStop": 11410,
//   "timestamp": "2024-04-30T09:08:22Z",
//   "transactionId": 81544895,
//   "reason": "Remote",
//   "transactionData": [
//     {
//       "sampledValue": [
//         {
//           "context": "Transaction.Begin",
//           "location": "EV",
//           "measurand": "SoC",
//           "unit": "Percent",
//           "value": "94"
//         },
//         {
//           "context": "Transaction.End",
//           "location": "EV",
//           "measurand": "SoC",
//           "unit": "Percent",
//           "value": "97"
//         }
//       ],
//       "timestamp": "2024-04-30T09:08:22Z"
//     }
//   ]
// };


async function initRefund(pgTransRef, payment_Id, receipt, differenceAmount) {
  try {
    const keyId = "***********************"; // "rzp_test_cn6p8HqmFvpWSW"
    const keySecret = "2IsMZvfk9kPz2iB4loELYUfj"; // "kouKRBYkLFI51GTS9ntQUX8f"
    const razorpayInstance = new razorpay({
      key_id: keyId,
      key_secret: keySecret
    });

    let refundResponse;

    refundResponse = await razorpayInstance.payments.refund(payment_Id, {
      "amount": differenceAmount * 100,
      "speed": "normal",
      "notes": {
        // "haha": "haha"
      },
      "receipt": receipt
    });
  } catch (error) {
    console.log(error);
  }

  // return refundResponse;

  // PhonePe
  console.log("Need to refund in bank!!");
  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    body: JSON.stringify({
      amount: differenceAmount,
      transRef: pgTransRef,
      pg: "PhonePe",
    })
  };

  const requestRefund = new Request("https://442aqpovec6i4iikhpxtkrfrsa0jlcry.lambda-url.ap-south-1.on.aws/", options);

  try {
    console.log("Calling Refund Order...")
    let response = await fetch(requestRefund);
    console.log(response);
    let body = await response.json();
    if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    statusCode = 400;
    console.error(body);
  }
}

async function updateUserWallet(userId, remainingAmt, booking_id) {
  // let userId = userId;
  let body, response, msg;

  const params = {
    TableName: "EndUser-r6cw5zqo7zb37hhq7w4ympiugy-prod",
    FilterExpression: '#id = :id',
    ExpressionAttributeNames: {
      '#id': 'id'
    },
    ExpressionAttributeValues: {
      ':id': userId
    }
  };

  console.log("USER ID is", userId, "remaining amount", remainingAmt);

  // Call DynamoDB get operation to read data for the specified ID

  try {
    const data = await dynamodb.scan(params).promise();
    console.log(data);
    if (data.Items.length > 0) {
      body = data.Items[0];
      console.log("OLD Balance is ", body.balance);
      const newBalance = body.balance + remainingAmt;

      const variables = { id: userId, newBalance: newBalance };
      console.log(variables);

      const query = /* GraphQL */ `
        mutation MyMutation($id: ID!,$newBalance: Float!) {
          updateEndUser(
            input: {id: $id, balance: $newBalance}
              ) {
                updatedAt
                createdAt
                id
                user_fullname
                dob
                joining_date
                email
                contact
                balance
                default_vehicle_id
                favs
                uId
                deviceId
              }
            }`;

      /** @type {import('node-fetch').RequestInit} */
      const transacOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

      console.log("Running Upate end user Mutation...");
      const response2 = await fetch(transacReq);
      console.log(response2);
      const body2 = await response2.json();
      if (body2.errors) statusCode = 400;
      console.log(body2);
      await createRefundTransaction(userId, booking_id, remainingAmt, body.user_fullname, body.contact, body.balance);



      // console.log("Running Wallet Update Mutation...")
      // response = await fetch(request);
      // console.log(response);
      // body = await response.json();
      // if (body.errors) statusCode = 400;
      // console.log(body);
      // msg = body;
    }
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.error(body);
  }
  return body;
}

async function createRefundTransaction(userId, booking_id, amountToRefund, uName, contact, userBalance) {
  let statusCode = 200;
  let response;
  try {
    let variables = {
      uId: userId, method: "Refund", dateTime: new Date(), bookingId: booking_id, currentBalance: userBalance,
      amount: amountToRefund, status: "Successful", reason: "Wallet", userName: uName, userContact: contact
    };

    console.log("In create transaction....", userId, variables);

    const query = /* GraphQL */ `
      mutation MyMutation($uId: String!,$method: String, $dateTime: AWSDateTime, $bookingId: String, $amount: Float,$currentBalance: Float, $status: String, $reason: String, $userName: String, $userContact: String) {
        createTransaction(
          input: {uId: $uId, method: $method, dateTime: $dateTime, bookingId: $bookingId, amount: $amount,currentBalance: $currentBalance, status: $status, reason :$reason, userName :$userName, userContact :$userContact }
        ) {
          createdAt
          updatedAt
          id
          amount
          method
          reason
          bookingId
          uId
          dateTime
          transRef
          pgTransRef
          status
          walletAmountUsed
          currentBalance
          userName
          userContact
          note
        }
      }
      `;

    /** @type {import('node-fetch').RequestInit} */
    const transacOptions = {
      method: 'POST',
      headers: {
        'x-api-key': GRAPHQL_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables })
    };

    const transacReq = new Request(GRAPHQL_ENDPOINT, transacOptions);

    console.log("Running create transaction Mutation...");
    response = await fetch(transacReq);
    console.log(response);
    const body = await response.json();
    // if (body.errors) statusCode = 400;
    console.log(body);
  } catch (error) {
    console.log("Inside create Transaction Mutation Catch");
    console.error(error);
  }
}

async function getSetInvoiceNo(chargerId) {
  try {

    const params2 = {
      TableName: "Charger-r6cw5zqo7zb37hhq7w4ympiugy-prod",
      FilterExpression: '#id = :id',
      ExpressionAttributeNames: {
        '#id': 'id'
      },
      ExpressionAttributeValues: {
        ':id': chargerId
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    console.log("...1");
    const data2 = await dynamodb.scan(params2).promise();
    console.log("...2");
    console.log(data2);
    if (data2.Items.length > 0) {
      console.log("...3");
      const chargerData = data2.Items[0];
      console.log(chargerData);
      //
      let oldInvoiceNo = chargerData.invoiceNo;
      oldInvoiceNo ??= 0;
      let newInvoiceNo = oldInvoiceNo + 1;
      console.log(newInvoiceNo);
      console.log("...4");
      //
      const finDateUpdated = chargerData.currentFinYear != null ? checkCurrentFinYearDate(chargerData.currentFinYear) : true;
      console.log("finDateUpdated", finDateUpdated);
      //
      newInvoiceNo = finDateUpdated ? newInvoiceNo : 1;

      const variables = {
        id: chargerData.id, invoiceNo: newInvoiceNo, currentFinYear: finDateUpdated ? (chargerData.currentFinYear == null ? getCurrentFinancialYearStartDate() : chargerData.currentFinYear) : getCurrentFinancialYearStartDate()
      };
      console.log(variables);

      console.log("...5");
      const query = /* GraphQL */ `
          mutation MyMutation2($id: ID!, $invoiceNo: Int, $currentFinYear: AWSDateTime) {
            updateCharger(
              input: {id: $id, invoiceNo: $invoiceNo, currentFinYear: $currentFinYear}
            ) {
              id
              invoiceNo
              currentFinYear
            }
          }`;
      console.log("...6");

      const cpoOptions = {
        method: 'POST',
        headers: {
          'x-api-key': GRAPHQL_API_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query, variables })
      };

      const cpoReq = new Request(GRAPHQL_ENDPOINT, cpoOptions);
      console.log("...7");

      console.log("Running Update Mutation...");
      const response2 = await fetch(cpoReq);
      console.log("...8");
      console.log(response2);
      const body2 = await response2.json();
      if (body2.errors) statusCode = 400;
      console.log(body2);
      return { "no": newInvoiceNo, "id": generateInvoiceIdString(chargerData.invoicePrefix, 2, 6) };
    }
  } catch (error) {
    console.log("Inside getSetInvoiceNo Update Mutation Catch");
    console.error(error);
  }
}

function generateInvoiceIdString(prefix, number, totalLength) {
  // Convert the number to a string and pad with leading zeros
  const paddedNumber = number.toString().padStart(totalLength - prefix.length, '0');
  // Combine the prefix and padded number
  return `${prefix}${paddedNumber}`;
}

function checkCurrentFinYearDate(currentFinYear) {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  // Determine the financial year start date
  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  // Convert currentFinYear to a Date object if it's not already one
  const currentFinYearDate = new Date(currentFinYear);

  // Check if currentFinYearDate is the same as finYearStart
  const finDateUpdated = currentFinYearDate.getTime() === finYearStart.getTime();

  return finDateUpdated;
}


function getCurrentFinancialYearStartDate() {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  let finYearStart;
  if (currentMonth < 3) { // If the current month is before April (0-indexed: Jan = 0, Feb = 1, Mar = 2)
    finYearStart = new Date(currentYear - 1, 3, 1); // April 1st of the previous year
  } else {
    finYearStart = new Date(currentYear, 3, 1); // April 1st of the current year
  }

  return finYearStart;
}



/* SELECT topic(1) as CP_ID, get(*, 3).meterStop as meterStop, get(*, 3).timestamp as timestamp, get(*, 3).idTag as idTag, get(*, 3) as MSG_BODY FROM '+/in' where get(*, 2) = 'StopTransaction' */

 */