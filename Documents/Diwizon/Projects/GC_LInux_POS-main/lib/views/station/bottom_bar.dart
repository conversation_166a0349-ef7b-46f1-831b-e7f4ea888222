import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/views/station/wids.dart';
import '../../theme/theme.dart';

class StationBottombar extends StatelessWidget {
  const StationBottombar(
      {super.key,
      required this.buttonActive,
      required this.pageIndex,
      required this.loading,
      required this.callback,
      required this.onBookSlot});

  final int pageIndex;
  final bool buttonActive;
  final bool loading;
  final Function callback;
  final Function onBookSlot;

  @override
  Widget build(BuildContext context) {
    return BottomAppBar(
        padding: EdgeInsets.zero,
        elevation: 12,
        shadowColor: Colors.black,
        surfaceTintColor: Colors.white,
        color: Colors.white,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey.shade100))),
          child: Row(children: [
            if (pageIndex == 1)
              Expanded(
                child: OutlinedButton(
                    onPressed: () => onBookSlot(),
                    style: whiteButton.copyWith(
                        padding: WidgetStatePropertyAll(
                            EdgeInsets.symmetric(vertical: 20.h)),
                        shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5)))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.arrow_back, size: 20.h),
                        SizedBox(width: 6),
                        Text(
                          "Back",
                          style: TextStyle(
                              color: Colors.black,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500),
                        ),
                      ],
                    )),
              ),
            if (pageIndex == 1) const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                  onPressed: () => callback(),
                  style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 20.h),
                      elevation: buttonActive ? null : 0,
                      backgroundColor:
                          buttonActive ? appColor : Colors.grey.shade100,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5))),
                  child: loading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(Colors.white),
                            strokeWidth: 1.2,
                          ),
                        )
                      : Text(
                          pageIndex < 2 ? "Next" : "Charge Now",
                          style: TextStyle(
                              fontSize: 16.sp,
                              color: buttonActive ? Colors.white : Colors.grey,
                              fontWeight: FontWeight.bold),
                        )),
            )
          ]),
        ));
  }
}
