import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/theme/theme.dart';
import 'package:go_charge/views/charging/charging_page.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/auth_ctrl.dart';
import '../../routers/routers.dart';

class CheckoutPage extends StatefulWidget {
  const CheckoutPage({
    super.key,
    required this.station,
    required this.selectedVehicle,
    required this.selectedCharge,
    required this.selectedConnector,
    required this.estimatedTime,
    required this.estimatedUnits,
    required this.estimatedAmount,
    required this.walletCallback,
    required this.paymentNowCallback,
    required this.taxPercent,
    required this.gstNoCtrl,
    required this.gstNameCtrl,
    required this.contactCtrl,
  });

  final Station station;
  final UserVehicle? selectedVehicle;
  final Charger? selectedCharge;
  final Connector? selectedConnector;
  final num estimatedTime;
  final num estimatedUnits;
  final num estimatedAmount;
  final num taxPercent;
  final Function walletCallback;
  final Function(num amountFromWallet) paymentNowCallback;
  final TextEditingController gstNoCtrl;
  final TextEditingController gstNameCtrl;
  final TextEditingController contactCtrl;

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  bool loading = false;
  bool fromWallet = false;
  bool includeWallet = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Payment",
          style: TextStyle(fontSize: 18.sp),
        ),
      ),
      body: GetBuilder<AppCtrl>(builder: (_) {
        final originalCost =
            widget.estimatedAmount * 100 / (100 + widget.taxPercent);
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade500),
                    borderRadius: BorderRadius.circular(4)),
                child: Column(
                  children: [
                    Text(
                      "ORDER DETAILS",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18.sp,
                      ),
                    ),
                    const DetailTextTile(
                      name: "Start Time",
                      value: "Now",
                    ),
                    DetailTextTile(
                        name: "Estimated Time",
                        value: '${widget.estimatedTime.toStringAsFixed(2)} m'),
                    DetailTextTile(
                        name: "Estimated Units",
                        value: widget.estimatedUnits.toStringAsFixed(2)),
                    DetailTextTile(
                        name: "GST (${widget.taxPercent}%)",
                        value:
                            '₹${(widget.estimatedAmount - originalCost).toStringAsFixed(1)}'),
                    DetailTextTile(
                        name: "Total Amount",
                        value: '₹${widget.estimatedAmount.toStringAsFixed(1)}'),
                  ],
                ),
              ),
              SizedBox(height: 28.sp),
              Padding(
                padding: const EdgeInsets.only(left: 4.0),
                child: Row(
                  children: [
                    Text(
                      "Complete Payment Of ",
                      style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade900),
                    ),
                    Text(
                      "₹${widget.estimatedAmount.toStringAsFixed(1)}",
                      style: TextStyle(
                          fontSize: 20.sp,
                          color: appColor,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 18.sp),
              Container(
                // padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  // border: Border.all(color: Colors.grey.shade100),
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.white,
                  boxShadow: const [
                    BoxShadow(color: Colors.black12, blurRadius: 8),
                  ],
                ),
                child: Column(
                  children: [
                    SizedBox(height: 12.sp),
                    /*    RadioListTile(
                      value: true,
                      fillColor: const WidgetStatePropertyAll(appColor),
                      groupValue: fromWallet,
                      onChanged: (value) =>
                          mounted ? setState(() => fromWallet = true) : null,
                      subtitle: Text(
                        "Current Balance is ₹${_.currentUserData?.balance?.toStringAsFixed(1) ?? 0}",
                        style: const TextStyle(
                            // fontSize: 16,
                            // color: appColor,
                            // fontWeight: FontWeight.bold,
                            ),
                      ),
                      title: const Row(
                        children: [
                          Text(
                            "Pay using Wallet",
                            style: TextStyle(fontSize: 16),
                          ),
                          // Text(
                          //   "(₹${_.currentUserData?.balance?.toStringAsFixed(1) ?? 0})",
                          //   style: const TextStyle(
                          //       fontSize: 16,
                          //       // color: appColor,
                          //       fontWeight: FontWeight.bold),
                          // ),
                        ],
                      ),
                    ),
                  */
                    RadioListTile(
                      value: false,
                      toggleable: true,
                      fillColor: const WidgetStatePropertyAll(appColor),
                      groupValue: fromWallet,
                      onChanged: (value) =>
                          mounted ? setState(() => fromWallet = false) : null,
                      title: Text(
                        "Pay using Gateway",
                        style: TextStyle(fontSize: 18.sp),
                      ),
                      subtitle: Text(
                        "UPI-Card-Bank",
                        style: TextStyle(fontSize: 16.sp),
                      ),
                    ),
                    SizedBox(height: 8.sp),
                    if (!fromWallet &&
                        (_.currentUserData?.balance ?? 0) <
                            widget.estimatedAmount &&
                        (_.currentUserData?.balance ?? 0) > 0)
                      InkWell(
                        onTap: loading
                            ? null
                            : () => mounted
                                ? setState(() => includeWallet = !includeWallet)
                                : null,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Row(
                            children: [
                              Checkbox(
                                value: includeWallet,
                                shape: ContinuousRectangleBorder(
                                    borderRadius: BorderRadius.circular(12)),
                                onChanged: loading
                                    ? null
                                    : (value) => mounted
                                        ? setState(() => includeWallet = value!)
                                        : null,
                              ),
                              Text(
                                "Use ₹${_.currentUserData?.balance?.toStringAsFixed(1) ?? 0} from wallet",
                                style: TextStyle(fontSize: 16.sp),
                              )
                            ],
                          ),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24.0, vertical: 0),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 15.h),
                                    backgroundColor: appColor,
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(4))),
                                onPressed: () async {
                                  // if (await Get.find<AuthCtrl>().isLoggedIn() !=
                                  //         true &&
                                  //     context.mounted) {
                                  //   AppRoutes.pushLoginPage(context,
                                  //       popTillName: ModalRoute.of(context)
                                  //           ?.settings
                                  //           .name);
                                  //   return;
                                  // }
                                  if (mounted) setState(() => loading = true);
                                  if (fromWallet) {
                                    await widget.walletCallback();
                                  } else {
                                    await widget.paymentNowCallback(
                                        includeWallet
                                            ? _.currentUserData?.balance ?? 0
                                            : 0);
                                  }
                                  if (mounted) setState(() => loading = false);
                                },
                                child: loading
                                    ? SizedBox(
                                        height: 28.sp,
                                        width: 28.sp,
                                        child: const CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              Colors.white),
                                          strokeWidth: 1.2,
                                        ))
                                    : Text(
                                        "Confirm and Pay ₹${includeWallet ? (widget.estimatedAmount - (_.currentUserData?.balance ?? 0)).toStringAsFixed(1) : widget.estimatedAmount.toStringAsFixed(1)}",
                                        style: GoogleFonts.roboto(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18.sp,
                                        ),
                                      )),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.sp),
                  ],
                ),
              ),
              SizedBox(height: 20.sp),
              ExpansionTile(
                initiallyExpanded: true,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6)),
                backgroundColor: Colors.grey.shade100,
                title: Text(
                  "GST Details & More",
                  style: TextStyle(fontSize: 20.sp),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: TextField(
                      controller: widget.gstNoCtrl,
                      style: TextStyle(fontSize: 18.sp),
                      decoration: InputDecoration(
                        labelStyle: TextStyle(fontSize: 16.sp),
                        alignLabelWithHint: true,
                        labelText: "GST No. (Optional)",
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: TextField(
                      controller: widget.gstNameCtrl,
                      style: TextStyle(fontSize: 18.sp),
                      decoration: InputDecoration(
                        labelStyle: TextStyle(fontSize: 16.sp),
                        alignLabelWithHint: true,
                        labelText: "GST Name. (Optional)",
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: TextField(
                      controller: widget.contactCtrl,
                      style: TextStyle(fontSize: 18.sp),
                      decoration: InputDecoration(
                        labelStyle: TextStyle(fontSize: 16.sp),
                        prefixText: "+91",
                        alignLabelWithHint: true,
                        labelText: "Contact No. (Optional)",
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }),
    );
  }
}
