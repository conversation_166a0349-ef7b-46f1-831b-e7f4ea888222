import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../controllers/vehicles_ctrl.dart';
import '../../models/UserVehicle.dart';
import '../../routers/routers.dart';
import '../../theme/theme.dart';
import '../Vehicle/my_vehicle_tile.dart';
import '../Vehicle/vehicle_page.dart';

class SelectorVehicle extends StatelessWidget {
  const SelectorVehicle(
      {super.key,
      this.callback,
      required this.selectedVehicle,
      required this.skipCallback});
  final Function(UserVehicle)? callback;
  final Function skipCallback;
  final UserVehicle? selectedVehicle;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        OutlinedButton(
            style: ButtonStyle(
                shape: WidgetStatePropertyAll(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                    side: const BorderSide(color: appColor),
                  ),
                ),
                side:
                    const WidgetStatePropertyAll(BorderSide(color: appColor))),
            onPressed: () async => AppRoutes.goToAddNewVehicle(context).then(
                (selected) => selected != null && context.mounted
                    ? takeGaadiNo(context, selected).then((value) =>
                        value != null
                            ? Get.find<VehiclesCtrl>()
                                .addToMyVehicles(selected, value)
                            : null)
                    : null),
            child: Padding(
              padding: const EdgeInsets.all(15.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add,
                    color: appColor,
                    size: 20.h,
                  ),
                  Text(
                    " Add Vehicle",
                    style: TextStyle(
                        color: appColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 16.h),
                  )
                ],
              ),
            )),
        SizedBox(height: 8.h),
        GetBuilder<VehiclesCtrl>(
          builder: (_) {
            return ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                itemCount: _.myVehicles.length,
                itemBuilder: (context, index) => MyVehicleTile(
                      myVehicle: _.myVehicles[index],
                      callback: callback,
                      groupValue: selectedVehicle,
                    ));
          },
        ),
        SizedBox(height: 10.h),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                  style: ButtonStyle(
                      side: const WidgetStatePropertyAll(BorderSide(
                          color: Color.fromARGB(255, 213, 207, 207))),
                      shape: WidgetStateProperty.all(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5.r))),
                      backgroundColor:
                          const WidgetStatePropertyAll(Colors.white)),
                  onPressed: () => skipCallback(),
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.0.h),
                    child: Text(
                      "Skip for Now",
                      style: TextStyle(
                          color: const Color(0xff828282),
                          fontSize: 18.h,
                          fontWeight: FontWeight.w600),
                    ),
                  )),
            ),
          ],
        ),
        SizedBox(height: 10.h),
      ],
    );
  }
}
