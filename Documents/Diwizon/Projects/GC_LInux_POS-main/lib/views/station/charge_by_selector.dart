import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/constants/const.dart';
import '../../theme/theme.dart';
import 'wids.dart';

class SelectorChargedBy extends StatefulWidget {
  const SelectorChargedBy({
    super.key,
    required this.duration,
    required this.sliderVal,
    required this.units,
    required this.price,
    required this.selectedChargeBy,
    required this.amountCtrl,
    required this.unitsCtrl,
    required this.setSelectedChargeBy,
    required this.onDurationChange,
    required this.onAmountChange,
    required this.onUnitChange,
  });

  final double duration;
  final num price;
  final num units;
  final int selectedChargeBy;
  final double sliderVal;
  final TextEditingController amountCtrl;
  final TextEditingController unitsCtrl;
  final Function(int) setSelectedChargeBy;
  final Function(double) onDurationChange;
  final Function(String) onAmountChange;
  final Function(String) onUnitChange;

  @override
  State<SelectorChargedBy> createState() => _SelectorChargedByState();
}

class _SelectorChargedByState extends State<SelectorChargedBy> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Row(
        //   children: [
        //     Expanded(
        //       child: SegmentedButton<int>(
        //         segments: <ButtonSegment<int>>[
        //           ButtonSegment<int>(
        //               value: 0,
        //               label: Text('Amount', style: TextStyle(fontSize: 10.sp)),
        //               icon: Icon(Icons.currency_rupee_rounded)),
        //           // ButtonSegment<int>(
        //           //     value: 1,
        //           //     label: Text('Units', style: TextStyle(fontSize: 10.sp)),
        //           //     icon: Icon(CupertinoIcons.gauge)),
        //           // ButtonSegment<int>(
        //           //     value: 2,
        //           //     label:
        //           //         Text('Duration', style: TextStyle(fontSize: 10.sp)),
        //           //     icon: Icon(CupertinoIcons.timer)),
        //         ],
        //         selected: <int>{widget.selectedChargeBy},
        //         onSelectionChanged: (Set<int> newSelection) {
        //           widget.setSelectedChargeBy(newSelection.first);
        //         },
        //       ),
        //     ),
        //   ],
        // ),
        SizedBox(height: 10.h),
        // [
        _byAmount(),
        //   _byUnits(),
        //   _byDuration(),
        // ][widget.selectedChargeBy]
      ],
    );
  }

  Row _estmPrice() {
    return Row(
      children: [
        const Text("Estimated Price (Incl GST)", style: textStyle),
        const Spacer(),
        Text(
          "₹ ${widget.price.toStringAsFixed(1)}",
          style: selectorCalcValues,
        ),
      ],
    );
  }

  Row _estmDuration() {
    return Row(
      children: [
        const Text("Estimated Duration", style: textStyle),
        const Spacer(),
        Text(
          "${widget.duration.toStringAsFixed(1)} min",
          style: selectorCalcValues,
        ),
      ],
    );
  }

  Row _estmConsumption() {
    return Row(
      children: [
        const Text("Consumption (kwh)", style: textStyle),
        const Spacer(),
        Text(
          widget.units.toStringAsFixed(1),
          style: selectorCalcValues,
        ),
      ],
    );
  }

  Column _byUnits() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: widget.unitsCtrl,
                keyboardType: const TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                onChanged: (value) => widget.onUnitChange(value),
                decoration: _inputDecor()
                    .copyWith(prefixText: "@", hintText: "Enter Units Here"),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        Container(
          decoration: BoxDecoration(
              border: Border.all(color: const Color.fromARGB(24, 52, 51, 51)),
              borderRadius: BorderRadius.circular(5)),
          child: Padding(
            padding: EdgeInsets.all(10.0.h),
            child: Column(
              children: [
                _estmConsumption(),
                SizedBox(height: 20.h),
                _estmDuration(),
                // SizedBox(height: 20.h),
                // _estmPrice(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Column _byAmount() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                controller: widget.amountCtrl,
                keyboardType: const TextInputType.numberWithOptions(
                    signed: true, decimal: true),
                textInputAction: TextInputAction.done,
                onChanged: (value) => widget.onAmountChange(value),
                decoration: _inputDecor().copyWith(
                  prefixText: "₹",
                  hintText: "Enter Amount Here",
                ),
              ),
            ),
          ],
        ),
        // SizedBox(height: 20.h),
        // Container(
        //     decoration: BoxDecoration(
        //         border: Border.all(
        //           color: const Color.fromARGB(24, 52, 51, 51),
        //         ),
        //         borderRadius: BorderRadius.circular(5.r)),
        //     child: Padding(
        //       padding: EdgeInsets.all(10.0.h),
        //       child: Column(
        //         children: [
        //           // _estmPrice(),
        //           // SizedBox(height: 20.h),
        //           _estmDuration(),
        //           SizedBox(height: 20.h),
        //           _estmConsumption(),
        //         ],
        //       ),
        //     )),
      ],
    );
  }

  InputDecoration _inputDecor() {
    return InputDecoration(
      filled: true,
      fillColor: Colors.grey.shade100,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(5)),
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(color: Colors.grey.shade200)),
    );
  }

  Column _byDuration() {
    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Slider(
              max: maxDuration,
              min: minDuration,
              divisions: durationDivs,
              value: widget.sliderVal > maxDuration
                  ? maxDuration
                  : widget.sliderVal,
              activeColor: appColor,
              onChanged: (dynamic value) {
                widget.onDurationChange(value);
              },
              // Display tooltip value
              label: widget.sliderVal.toString(),
            ),
            // SfSlider(
            //   //tooltipShape: const SfPaddleTooltipShape(),
            //   min: 0.0,
            //   max: 100.0,
            //   edgeLabelPlacement: EdgeLabelPlacement.inside,
            //   activeColor: const appColor,
            //   value: _value,
            //   interval: 10,
            //   showDividers: true,
            //   enableTooltip: true,
            //   minorTicksPerInterval: 0,
            //   onChanged: (dynamic value) {
            //     setState(() {
            //       _value = value;
            //       duration = value;
            //     });
            //   },
            // ),
            const IgnorePointer(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 14),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomGreenCircle(),
                    CustomGreenCircle(),
                  ],
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
        Container(
            decoration: BoxDecoration(
                border: Border.all(
                  color: const Color.fromARGB(24, 52, 51, 51),
                ),
                borderRadius: BorderRadius.circular(5.r)),
            child: Padding(
              padding: EdgeInsets.all(10.0.h),
              child: Column(
                children: [
                  _estmDuration(),
                  SizedBox(height: 20.h),
                  // _estmPrice(),
                  // SizedBox(height: 20.h),
                  _estmConsumption(),
                ],
              ),
            )),
      ],
    );
  }
}

const textStyle = TextStyle(
    // fontSize: 14.h,
    color: Color(0xff4F4F4F),
    fontWeight: FontWeight.w500);

const selectorCalcValues = TextStyle(
    // fontSize: 14.h,
    fontWeight: FontWeight.w600,
    color: Color(0xff333333));

class ChargByTypes {
  static const duration = "Duration";
  static const amount = "Amount";
  static const unit = "Unit";
}
