import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_charge/constants/const.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../models/Station.dart';
import '../../shared/methods.dart';
import 'wids.dart';

class StationButtons extends StatelessWidget {
  const StationButtons(this.station, {super.key});

  final Station station;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          OutlinedButton(
              onPressed: () {
                navigateTo(num.tryParse(station.latitude ?? "0"),
                    num.tryParse(station.longitude ?? "0"));
              },
              style: whiteButton.copyWith(
                  padding: WidgetStatePropertyAll(EdgeInsets.all(10.h))),
              child: Row(
                children: [
                  Icon(
                    Icons.navigation,
                    size: 20.h,
                    color: const Color(0xff828282),
                  ),
                  SizedBox(width: 5.h),
                  Text(
                    "Direction",
                    style: TextStyle(
                        fontSize: 16.h, color: const Color(0xff828282)),
                  ),
                ],
              )),
          SizedBox(width: 20.h),
          OutlinedButton(
              onPressed: () {
                try {
                  launchUrlString("tel:${station.contact_no}");
                } catch (e) {
                  debugPrint(e.toString());
                }
              },
              style: whiteButton.copyWith(
                  padding: WidgetStatePropertyAll(EdgeInsets.all(10.h))),
              child: Row(
                children: [
                  Icon(
                    Icons.call,
                    size: 20.h,
                    color: const Color(0xff828282),
                  ),
                  SizedBox(width: 5.h),
                  Text(
                    "Call Operator",
                    style: TextStyle(
                        fontSize: 16.h, color: const Color(0xff828282)),
                  ),
                ],
              )),
          SizedBox(width: 20.h),
          OutlinedButton(
              onPressed: () {
                try {
                  Share.share('Explore this amazing app: $shareLink',
                      subject: 'Go Charge | Elcop Controls');
                } catch (e) {
                  debugPrint(e.toString());
                }
              },
              style: whiteButton.copyWith(
                  padding: WidgetStatePropertyAll(EdgeInsets.all(10.h))),
              child: Row(
                children: [
                  Icon(
                    Icons.share,
                    size: 20.h,
                    color: const Color(0xff828282),
                  ),
                  SizedBox(width: 5.h),
                  Text(
                    "Share",
                    style: TextStyle(
                        fontSize: 16.h, color: const Color(0xff828282)),
                  ),
                ],
              )),
        ],
      ),
    );
  }
}
