import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../theme/theme.dart';

class StepSelectorText extends StatelessWidget {
  const StepSelectorText({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      "Selector",
      style: TextStyle(fontSize: 16.h, fontWeight: FontWeight.w600),
    );
  }
}

class StepSelectorConnector extends StatelessWidget {
  const StepSelectorConnector(
      {super.key, required this.callback, required this.selectedPage});
  final int selectedPage;
  final Function(int i) callback;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 23.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ...List.generate(
              2,
              (index) => index > 1
                  ? _circleAndLine(index)
                  : Expanded(child: _circleAndLine(index))),
        ],
      ),
    );
  }

  Row _circleAndLine(int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          child: Container(
            height: 18,
            width: 18,
            decoration: BoxDecoration(
                border: Border.all(
                    color: selectedPage >= index
                        ? const Color(0xff33A63F)
                        : Colors.grey),
                borderRadius: BorderRadius.circular(15)),
            child: Center(
                child: Container(
                    height: 13,
                    width: 13,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: selectedPage >= index
                            ? const Color(0xff33A63F)
                            : Colors.grey,
                        shape: BoxShape.circle),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(color: Colors.white, fontSize: 10),
                    ))),
          ),
          onTap: () => callback(index),
        ),
        if (index < 1)
          Expanded(
            child: Container(
              color: selectedPage > index ? appColor : Colors.grey,
              height: 2,
              // width: 139.h,
            ),
          ),
      ],
    );
  }
}

class StepSelectorTexts extends StatelessWidget {
  const StepSelectorTexts({super.key, required this.callback});
  final Function(int i) callback;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
            onTap: () => callback(0),
            child: const Padding(
              padding: EdgeInsets.only(top: 10.0, bottom: 24),
              child: Text("Connector"),
            )),
        const Spacer(),
        // InkWell(
        //     onTap: () => callback(1),
        //     child: const Padding(
        //       padding: EdgeInsets.only(top: 10.0, bottom: 24),
        //       child: Text("Vehicle"),
        //     )),
        // const Spacer(),
        InkWell(
            onTap: () => callback(2),
            child: const Padding(
              padding: EdgeInsets.only(top: 10.0, bottom: 24),
              child: Text("Charge by"),
            )),
      ],
    );
  }
}
