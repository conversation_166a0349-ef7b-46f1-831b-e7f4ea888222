import 'dart:async';

import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/auth_ctrl.dart';
import 'package:go_charge/utils/extentions.dart';
import 'package:go_charge/views/other/loader_page.dart';
import 'package:go_charge/views/other/login_button.dart';
import '../../constants/const.dart';
import '../../controllers/app_ctrl.dart';
import '../../models/EndUser.dart';
import '../../models/ModelProvider.dart';
import '../../models/Transaction.dart';
import '../../shared/methods.dart';
import '../../shared/snackbar.dart';
import '../../theme/theme.dart';
import '../../utils/payment_gateway.dart';
import 'package:amplify_api/amplify_api.dart' as amplify;

const _topContainerHeight = 240.0;

class WalletPage extends StatefulWidget {
  const WalletPage({super.key});

  @override
  State<WalletPage> createState() => _WalletPageState();
}

class _WalletPageState extends State<WalletPage> {
  StreamSubscription<amplify.GraphQLResponse<Transaction>>? _transStream;
  List<Transaction>? transactions;
  bool loading = false;
  bool loadingTrans = false;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool?>(
        future: Get.find<AuthCtrl>().isLoggedIn(),
        builder: (context, snapshot) {
          // print(snapshot.hasError);
          if (snapshot.data == true) {
            return GetBuilder<AppCtrl>(builder: (_) {
              return Stack(
                children: [
                  loadingTrans
                      ? const Center(child: CircularProgressIndicator())
                      : ListView.builder(
                          padding: const EdgeInsets.only(
                              top: _topContainerHeight + 4),
                          shrinkWrap: true,
                          itemCount: (transactions?.length ?? 0),
                          itemBuilder: (context, index) {
                            final trans = transactions?[index];
                            return TransactionTile(trans: trans);
                          },
                        ),
                  Container(
                      decoration: const BoxDecoration(boxShadow: [
                        BoxShadow(color: Colors.black12, blurRadius: 6)
                      ], color: Colors.white),
                      height: _topContainerHeight,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          // crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                                height: MediaQuery.paddingOf(context).top + 20),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 1),
                              decoration: BoxDecoration(
                                  color: appColorLite,
                                  borderRadius: BorderRadius.circular(45)),
                              child: const Text("Balance"),
                            ),
                            Text(
                              '₹${_.currentUserData?.balance?.toStringAsFixed(1) ?? 0}',
                              style: const TextStyle(fontSize: 48),
                            ),
                            const SizedBox(height: 12),
                            loading
                                ? const CircularProgressIndicator()
                                : OutlinedButton.icon(
                                    style: OutlinedButton.styleFrom(
                                        backgroundColor:
                                            appColor.withOpacity(.04),
                                        side:
                                            const BorderSide(color: appColor)),
                                    onPressed: () => _addMoneyButton(context),
                                    icon: const Icon(
                                      CupertinoIcons.add,
                                      color: appColor,
                                    ),
                                    label: const Text(
                                      "Add Money",
                                      style: TextStyle(color: appColor),
                                    ))
                          ],
                        ),
                      )),
                ],
              );
            });
          } else if (snapshot.data == false) {
            return LoginButton(refresh: () => setState(() {}));
          }
          return const LoaderPage();
        });
  }

  @override
  void initState() {
    super.initState();
    getTransaction();
  }

  void getTransaction() async {
    try {
      safePrint("Fethcing Global Vehicles...");
      if (mounted) setState(() => loading = true);
      final request = ModelQueries.list(Transaction.classType,
          limit: unlimitedLimit,
          where: Transaction.UID
              .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
              .and(Transaction.REASON
                  .contains("Wallet")
                  .or(Transaction.METHOD.contains("Wallet"))));
      // BY GRAPHQL API
      final result = await Amplify.API.query(request: request).response;
      safePrint('Transaction: ${result.data?.items.length}');
      // manufacturers = result;
      transactions = result.data?.items.whereType<Transaction>().toList() ?? [];
      transactions?.sort((a, b) =>
          b.createdAt?.compareTo(a.createdAt ?? TemporalDateTime.now()) ?? 0);
      if (mounted) setState(() => loading = false);
      observeTrans();
    } on amplify.ApiException catch (e) {
      if (mounted) setState(() => loading = false);
      safePrint('Something went wrong querying Transaction: ${e.message}');
      return null;
    } catch (e) {
      if (mounted) setState(() => loading = false);
      safePrint('Something went wrong querying Transaction: $e');
      return null;
    }
  }

  void observeTrans() {
    final subscriptionRequest = amplify.ModelSubscriptions.onCreate(
        Transaction.classType,
        where: Transaction.UID
            .eq(Get.find<AppCtrl>().currentAuthUser?.userId)
            .and(Transaction.REASON
                .contains("Wallet")
                .or(Transaction.METHOD.contains("Wallet"))));
    // Stream
    final Stream<amplify.GraphQLResponse<Transaction>> operation =
        Amplify.API.subscribe(
      subscriptionRequest,
      onEstablished: () => safePrint('Subscription established'),
    );
    // Listner
    _transStream = operation.listen(
      (event) async {
        safePrint(
            'Subscription event data received Transaction: ${event.data}');
        if (event.data != null) {
          transactions?.insert(0, event.data!);
          if (mounted) setState(() {});
        }
      },
      onError: (Object e) => safePrint('Error in subscription stream: $e'),
    );
  }

  _addMoneyButton(BuildContext context) async {
    try {
      final amountCtrl = TextEditingController();
      final amount = await showDialog<int>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text("Wallet Recharge"),
          content: TextField(
            controller: amountCtrl,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
                labelText: "Enter Amount",
                hintText: "₹",
                alignLabelWithHint: true),
          ),
          actions: [
            TextButton(
                onPressed: () =>
                    Navigator.of(context).pop(num.tryParse(amountCtrl.text)),
                child: const Text("Confirm"))
          ],
        ),
      );
      if (amount != null) {
        if (mounted) setState(() => loading = true);
        // GET USER DATA
        final userDataRequest = ModelQueries.get(
            EndUser.classType,
            EndUserModelIdentifier(
                id: Get.find<AppCtrl>().currentUserData?.id ?? ""));
        final userDataResponse =
            await Amplify.API.mutate(request: userDataRequest).response;
        final pgTransRef = getRandomId(34).toUpperCase();
        final transDoc = Transaction(
          currentBalance: userDataResponse.data?.balance,
          pgTransRef: pgTransRef,
          // id: uuid(),
          walletAmountUsed: null,
          bookingId: "",
          transRef: "",
          status: TransactionStatus.pending,
          amount: (amount).toDouble(),
          dateTime: TemporalDateTime.now(),
          reason: "Wallet",
          method: "-",
          uId: Get.find<AppCtrl>().currentAuthUser?.userId,
          userName: Get.find<AppCtrl>().currentUserData?.user_fullname,
          userContact: Get.find<AppCtrl>().currentUserData?.contact,
        );
        final request = ModelMutations.create(transDoc);
        final transResponse =
            await Amplify.API.mutate(request: request).response;
        if (transResponse.data != null) {
          debugPrint(transResponse.data.toString());
          showAppShackBar(transResponse.data?.id.toString() ?? 'null');
          PaymentGateway.initPhonePePayment(
            amount: amount.toInt() * 100,
            receipt: pgTransRef,
            mobileNumber: Get.find<AppCtrl>().currentUserData?.contact ?? "",
            isWalletTopup: true,
            handlePaymentErrorResponse: () {
              showAppShackBar("Payment Failed!");
              if (mounted) setState(() => loading = false);
            },
            handlePaymentSuccessResponse: () {
              if (mounted) setState(() => loading = false);
              showAppShackBar("Payment was successfull!");
              getTransaction();
            },
          );
          /*  final resp = await APIManager.createPGOrder(
            amount: amount.toInt() * 100,
            receipt: transResponse.data!.id,
            bId: transResponse.data!.id,
            uId: Get.find<AppCtrl>().currentAuthUser!.userId,
            type: true ? "Wallet" : "Booking",
          );
          debugPrint(resp?.amount.toString());
          // return;
          if (resp != null) {
            RazorPayPG.initRazorPayment(
                pgOrder: resp,
                isWalletTopup: true,
                handlePaymentErrorResponse: (d) {
                  showAppShackBar("Payment Failed!");
                  if (mounted) setState(() => loading = false);
                },
                handlePaymentSuccessResponse: (d) {
                  if (mounted) setState(() => loading = false);
                  showAppShackBar("Payment was successfull!");
                  getTransaction();
                },
                handleExternalWalletSelected: (d) {
                  showAppShackBar("External Wallet Selected");
                  if (mounted) setState(() => loading = false);
                });
          } */
        }
      }
    } catch (e) {
      debugPrint(e.toString());
      if (mounted) setState(() => loading = false);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _transStream?.cancel();
  }
}

class TransactionTile extends StatelessWidget {
  const TransactionTile({
    super.key,
    required this.trans,
  });

  final Transaction? trans;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () => onTransactionTap(context, trans),
      leading: CircleAvatar(
        backgroundColor: trans?.status == TransactionStatus.pending
            ? Colors.grey.shade200
            : trans?.reason == "Wallet"
                ? appColorLite
                : Colors.grey.shade200,
        child: Icon(
          trans?.status == TransactionStatus.pending
              ? CupertinoIcons.exclamationmark_circle
              : trans?.reason == "Wallet"
                  ? CupertinoIcons.arrow_down_right
                  : CupertinoIcons.arrow_up_left,
          color: trans?.status == TransactionStatus.pending
              ? Colors.red.shade400
              : Colors.grey.shade800,
        ),
      ),
      title: Text(trans?.reason ?? "-"),
      subtitle: Text(
          '${trans?.dateTime?.getDateTimeInUtc().toLocal().goodDayDate() ?? "-"} ${trans?.dateTime?.getDateTimeInUtc().toLocal().goodTime() ?? "-"}'),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Text(trans?.id ?? "-"),
          Text(
            (trans?.walletAmountUsed ?? -0) > 0
                ? '₹${trans?.walletAmountUsed?.toStringAsFixed(1)} & '
                    '₹${((trans?.amount ?? 0)).toStringAsFixed(1)}'
                : '₹${((trans?.amount ?? 0)).toStringAsFixed(1)}',
            style: TextStyle(
                fontSize: 14,
                color: trans?.status == TransactionStatus.pending
                    ? Colors.red.shade400
                    : trans?.reason == "Wallet"
                        ? appColor
                        : Colors.grey.shade800,
                fontWeight: FontWeight.bold),
          ),
          Text(
            trans?.method ?? "-",
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }
}

class TransactionStatus {
  static const pending = "Pending";
  static const successful = "Successful";
}

onTransactionTap(BuildContext context, Transaction? transaction) async {
  if (transaction == null) return;
  return showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 12),
            const Text(
              "Transaction Detials",
              style: TextStyle(fontSize: 20),
            ),
            const Divider(),
            SingleChildScrollView(
              padding: const EdgeInsets.only(left: 14, right: 14, bottom: 12),
              child: ListBody(
                children: <Widget>[
                  // _buildDetailRow('ID', transaction.id),
                  _buildDetailRow('Amount', transaction.amount?.toString()),
                  _buildDetailRow('Previous Balance',
                      transaction.currentBalance?.toString()),
                  _buildDetailRow('Method', transaction.method),
                  _buildDetailRow('Reason', transaction.reason),
                  // _buildDetailRow('Booking ID', transaction.bookingId),
                  // _buildDetailRow('User ID', transaction.uId),
                  // _buildDetailRow('DateTime', transaction.dateTime?.format()),
                  _buildDetailRow('DateTime',
                      '${transaction.dateTime?.getDateTimeInUtc().toLocal().goodDayDate() ?? "-"} ${transaction.dateTime?.getDateTimeInUtc().toLocal().goodTime() ?? "-"}'),

                  _buildDetailRow(
                      'Transaction Reference', transaction.transRef),
                  _buildDetailRow('Status', transaction.status),
                  _buildDetailRow('Wallet Amount Used',
                      transaction.walletAmountUsed?.toString()),
                  _buildDetailRow('Created At',
                      '${transaction.createdAt?.getDateTimeInUtc().toLocal().goodDayDate() ?? "-"} ${transaction.createdAt?.getDateTimeInUtc().toLocal().goodTime() ?? "-"}'),
                  const Divider(),
                  _buildDetailRow("Booking Id", transaction.bookingId),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Widget _buildDetailRow(String label, String? value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      children: <Widget>[
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        Expanded(
          child: Text(value ?? 'N/A'),
        ),
      ],
    ),
  );
}

/* SELECT 'out_'+topic(1)+timestamp()+get(*, 1) as id, topic(1) AS cpId, "Out Log" AS Event, get(*, 3) AS Log, timeStamp() as TimeStamp, get(*, 1) AS logSequence FROM '+/out' */


