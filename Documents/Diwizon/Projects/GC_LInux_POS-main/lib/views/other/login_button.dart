import 'package:flutter/material.dart';
import 'package:go_charge/routers/routers.dart';

class LoginButton extends StatelessWidget {
  const LoginButton({super.key, this.refresh});
  final Function? refresh;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: TextButton.icon(
        onPressed: () async {
          await AppRoutes.pushLoginPage(context);
          if (refresh != null) refresh!();
        },
        label: const Text("Login/ SignUp"),
        icon: const Icon(Icons.logout_rounded),
      ),
    );
  }
}
// #d34b3e
// #f33614
// #ee2a07