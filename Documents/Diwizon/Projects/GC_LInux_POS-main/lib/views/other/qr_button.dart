import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/shared/snackbar.dart';
import '../../controllers/auth_ctrl.dart';
import '../../routers/routers.dart';

class QrScanButton extends StatelessWidget {
  const QrScanButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
        onPressed: () async {
          if (await Get.find<AuthCtrl>().isLoggedIn() == true &&
              context.mounted) {
            // AppRoutes.goToQRScanner(context);
          } else {
            showAppShackBar("Please, login for quick charge!");
          }
        },
        icon: const Icon(
          Icons.qr_code_scanner_rounded,
          color: Colors.black,
          size: 28,
        ));
  }
}
