import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SheetHandle extends StatelessWidget {
  const SheetHandle({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.only(top: 10.0.h, bottom: 10.h),
        child: Container(
          height: 6.h,
          width: 80.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: const Color(0xffD9D9D9)),
        ),
      ),
    );
  }
}
