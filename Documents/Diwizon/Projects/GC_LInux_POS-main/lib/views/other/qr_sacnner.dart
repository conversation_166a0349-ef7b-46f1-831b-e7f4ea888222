/* import 'dart:io';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/routers/routers.dart';
import 'package:go_charge/shared/loader.dart';
import 'package:go_charge/shared/snackbar.dart';
import 'package:go_charge/views/station/charge_by_selector.dart';
import 'package:string_validator/string_validator.dart';
import '../../controllers/app_ctrl.dart';
import '../../controllers/booking_ctrl.dart';
import '../../shared/methods.dart';
import '../../theme/theme.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:scanning_effect/scanning_effect.dart';

class QRCodeScanner extends StatefulWidget {
  const QRCodeScanner({super.key});

  @override
  State<QRCodeScanner> createState() => _QRCodeScannerState();
}

class _QRCodeScannerState extends State<QRCodeScanner> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  // String? scannedId;
  QRViewController? controller;
  bool loading = false;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller?.pauseCamera();
    } else if (Platform.isIOS) {
      controller?.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Quick Charge"),
        centerTitle: false,
        iconTheme: IconThemeData(color: loading ? Colors.black : Colors.white),
        backgroundColor: Colors.transparent,
      ),
      extendBodyBehindAppBar: true,
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              fit: StackFit.expand,
              children: <Widget>[
                QRView(key: qrKey, onQRViewCreated: _onQRViewCreated),
                const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Spacer(),
                    AspectRatio(
                      aspectRatio: 1,
                      child: ScanningEffect(
                        scanningColor: appColor,
                        borderLineColor: Colors.black,
                        child: Center(
                            child: Text(
                          "GO Charge QR",
                          style: TextStyle(color: Colors.white),
                        )),
                        // child: Center(child: Text("GO Charge QR")),
                      ),
                    ),
                    Spacer(),
                  ],
                ),
                if (loading)
                  const Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(80.0),
                        child: AppLoader(size: 50),
                      ),
                    ],
                  )
              ],
            ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      // scannedId = scanData.code;
      final splits = scanData.code?.split("&");
      if (splits?.length != 3 || !isUUID(splits?.last ?? "")) {
        return;
      }
      fetchData(scanData.code);
    });
  }

  fetchData(String? code) async {
    try {
      if (code == null || loading) return;
      final data = code.split("&");
      if (data.length < 3) return;
      final ctrl = Get.find<AppCtrl>();
      if ((ctrl.currentUserData?.balance ?? 0) == 0) {
        showAppShackBar("Insufficient Balance");
        controller?.stopCamera();
        AppRoutes.popAll(context);
        return;
      }
      controller?.pauseCamera();
      if (mounted) setState(() => loading = true);
      // Get Station
      final stationRequest = ModelQueries.get(
          Station.classType, StationModelIdentifier(id: data.last));
      final stationResult =
          await Amplify.API.query(request: stationRequest).response;
      if (stationResult.data != null && mounted) {
        // Get Charger
        final chargerRequest = ModelQueries.list(Charger.classType,
            where: Charger.CHARGINGPOINTID.eq(data.first));
        final chargerResult =
            await Amplify.API.query(request: chargerRequest).response;
        if (chargerResult.data?.items.isNotEmpty ?? false) {
          // Get Connector
          final connectorRequest = ModelQueries.list(Connector.classType,
              where: Connector.CONNECTOR_NUMBER.eq(data[1]).and(
                  Connector.CHARGERID.eq(chargerResult.data?.items[0]?.id)));
          final connectorResult =
              await Amplify.API.query(request: connectorRequest).response;
          if (connectorResult.data?.items.isNotEmpty ?? false) {
            showAppShackBar(stationResult.data?.cpoId ?? "");
            // Get CPO
            final cpoRequest = ModelQueries.get(CPO.classType,
                CPOModelIdentifier(id: stationResult.data?.cpoId ?? ""));
            final cpoResult =
                await Amplify.API.query(request: cpoRequest).response;
            if (cpoResult.data != null) {
              // Get TAX
              final taxRequest = ModelQueries.get(Taxes.classType,
                  TaxesModelIdentifier(id: cpoResult.data?.taxId ?? ""));
              final taxResult =
                  await Amplify.API.query(request: taxRequest).response;
              if (taxResult.data != null) {
                final quickChargeLimit =
                    cpoResult.data?.quickChargeLimit ?? 700;
                // Calc Amount
                final estimatedPrice =
                    (ctrl.currentUserData?.balance ?? 0) > quickChargeLimit
                        ? quickChargeLimit
                        : (ctrl.currentUserData?.balance ?? 0);
                num estimatedDuration = 0;
                num estimatedUnits = 0;
                final taxAmount =
                    (taxResult.data?.rate ?? 0) / 100 * estimatedPrice;
                final payableAmount = estimatedPrice;
                // if (selectedVehicle != null) {
                final pgTransRef = getRandomId(34).toUpperCase();
                final bId = await Get.find<BookingCtrl>().createNewBooking2(
                  contact: null,
                  pgTransRef: pgTransRef,
                  taxPercent: taxResult.data?.rate ?? 0,
                  charger: chargerResult.data?.items.first,
                  cpo: cpoResult.data,
                  userVehicle: null,
                  startAt: DateTime.now(),
                  estimatedDuration: estimatedDuration,
                  selectedConnector: connectorResult.data?.items.first,
                  station: stationResult.data!,
                  bookingType: ChargByTypes.amount,
                  estimatedPrice: estimatedPrice,
                  estimatedUnits: estimatedUnits,
                  taxAmount: taxAmount,
                  amountFromWallet: payableAmount,
                  fromWallet: true,
                  gstNo: null,
                  gstName: null,
                );
                if (bId != null) {
                  debugPrint("Boooking done");
                  if (mounted) AppRoutes.goToChargingDetails(context, bId);
                }
              }
            }
          }
        }
        // showAppShackBar("Go Charge Station Scanned!");
        // AppRoutes.popAndGoToStationDetails(
        //   context,
        //   station: stationResult.data!,
        //   chargingPointId: data.first,
        //   connectorNo: data[1],
        // );
      } else {
        // showAppShackBar("Invalid Scanner Input");
      }
      if (mounted) setState(() => loading = false);
    } catch (e) {
      showAppShackBar("Something went wrong!");
      debugPrint(e.toString());
      if (mounted) setState(() => loading = false);
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
 */