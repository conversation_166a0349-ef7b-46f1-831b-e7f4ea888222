import 'package:flutter/material.dart';
import 'package:go_charge/views/other/app_logo.dart';
import '../../shared/loader.dart';

class LoaderPage extends StatelessWidget {
  const LoaderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
        body: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        <PERSON><PERSON>(),
        <PERSON><PERSON><PERSON><PERSON>(width: 50),
        <PERSON><PERSON>(),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(size: 50),
        <PERSON><PERSON>(flex: 2),
      ],
    ));
  }
}
