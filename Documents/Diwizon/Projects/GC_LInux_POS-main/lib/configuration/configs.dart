import 'dart:io';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_linux_webview/flutter_linux_webview.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:webview_flutter/webview_flutter.dart';
import '../amplifyconfiguration.dart';
import '../models/ModelProvider.dart';

late bool firstTime;
late String? localChargerId = "E01";
late BuildContext contextOfHome;

// configureLinux() {
//   // Run `LinuxWebViewPlugin.initialize()` first before creating a WebView.
//   LinuxWebViewPlugin.initialize(options: <String, String?>{
//     'user-agent': 'UA String',
//     'remote-debugging-port': '8888',
//     'autoplay-policy': 'no-user-gesture-required',
//   });

//   // Configure [WebView] to use the [LinuxWebView].
//   WebView.platform = LinuxWebView();
// }

Future<void> configureHive() async {
  try {
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocumentsDir.path);
    final box = await Hive.openBox('sets');
    firstTime = box.get('firstTime') ?? true;
    box.put('firstTime', false);
    localChargerId = box.get('localChargerId');
    print(localChargerId);
  } catch (e) {
    debugPrint(e.toString());
  }
}

Future<void> configureAmplify() async {
  try {
    // Create the API plugin.
    final api = AmplifyAPI(
        options: APIPluginOptions(modelProvider: ModelProvider.instance));

    // Create the Auth plugin.
    // final auth = <AmplifyPluginInterface>[
    //   // AmplifyAuthCognito(),
    //   // AmplifyAPI(modelProvider: ModelProvider.instance)
    // ];

    // Amplify configs

    // Add plugins to amplify
    await Amplify.addPlugins([api]);

    // Init Configure
    await Amplify.configure(amplifyconfig);
  } on AmplifyAlreadyConfiguredException {
    debugPrint(
        'Tried to reconfigure Amplify; this can occur when your app restarts on Android.');
  } catch (e) {
    debugPrint('Error Configuring Amplify: $e');
  }
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.mouse,
        PointerDeviceKind.touch,
        PointerDeviceKind.stylus,
        PointerDeviceKind.unknown,
        PointerDeviceKind.trackpad,
      };
}
