/* import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/phonepe_ctrl.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo PhonePe',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'Flutter Demo PhonePe PG'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final controller = Get.put(PGPaymentController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Obx(
        () => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Text(
                'PhonePe Payment Gateway Integration',
              ),
              MaterialButton(
                onPressed: () {
                  controller.initPayment(
                      customerMobile: '+919876543210', amount: 100);
                },
                child: Text(
                  'Pay Now',
                ),
              ),
              Visibility(
                visible: !controller.isVisible.value,
                child: MaterialButton(
                  onPressed: () {
                    controller.checkPaymentStatus();
                  },
                  color: Colors.green,
                  child: Text(
                    'Check Status',
                  ),
                ),
              ),
            ],
          ),
        ),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
 */

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_linux_webview/flutter_linux_webview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/controllers/booking_ctrl.dart';
import 'package:go_charge/controllers/vehicles_ctrl.dart';
import 'package:go_charge/configuration/configs.dart';
import 'package:go_charge/views/pos/pos.dart';
import 'package:go_charge/views/pos/temp.dart';
import 'theme/theme.dart';
import 'shared/globals.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // BASE SETUPS //
  await configureHive();
  await configureAmplify();
  Get.put(AppCtrl());
  Get.put(VehiclesCtrl());
  Get.put(BookingCtrl());

  // LINUX CONFIGS //
  // if (Platform.isLinux) await configureLinux();
  // LAUNCH START HERE //
  runApp(const GoChargeApp());
}

class GoChargeApp extends StatelessWidget {
  const GoChargeApp({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent));
    return ScreenUtilInit(
      designSize: const Size(430, 932),
      minTextAdapt: true,
      builder: (_, child) => MaterialApp(
        title: 'Go Charge',
        scaffoldMessengerKey: snackbarKey,
        debugShowCheckedModeBanner: false,
        theme: appTheme,
        home: child,
        scrollBehavior: MyCustomScrollBehavior(),
      ),
      // child: PaymentScreen(),
      child: const POSWid(),
    );
  }
}
