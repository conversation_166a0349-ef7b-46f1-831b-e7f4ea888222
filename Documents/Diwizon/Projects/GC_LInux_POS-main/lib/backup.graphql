type OutLog @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  data: String
  date: String
  charger_id: String
}

type InLog @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  data: AWSJSON
  date: AWSDate
  charger_id: String
}

type LocationType @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  place: String
  is_active: Boolean
}

type RFIDSchema @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  rfidValue: String
  expires: AWSDate
  isactive: Boolean
  userID: ID! @index(name: "byEndUser")
}

type ChargerCapacity @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  charger_capacity: String
  date: AWSDate
  is_active: Boolean
}

type ChargingTable @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  booking_id: String
  booking_start_time: AWSDate
  booking_end_time: AWSDate
  status: String
  user_name: String
  station_name: String
  role: String
  connector_id: String
  current_meter_value: String
  city: String
  vehicle: String
  charging_fee: String
  payment_status: String
  date: String
  tax_amount: String
  vehical_number: String
}

type PaymentRequestTable @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  gateway: String
  emailId: String
  phoneNo: String
  customerId: String
  OrderId: String
  transactionAmount: String
  callbackurl: String
}

type ManufacturerTable @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  manufacturer_name: String
  manufacturer_type: String
  status: Boolean
  contact_person: String
  contact_number: String
  email_id: String
  contact_person_dept: String
  address: String
  country: String
  state: String
  city: String
  postcode: String
  contact_person_designation: String
  is_preferred: String
}

type Manufacturer @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  manufacture_name: String
  manufacture_type: String
  status: String
  is_preferred: String
}

type Issues @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  category: String
  sub_category: String!
  priority: String!
  description: String!
  mobile_no: String!
  attachments: String!
  status: Boolean!
  operator_name: String!
}

type Configuration @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
}

type Vehicle @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  company: String
  model: String
}

type ConnectorType @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  name: String!
  image: String!
  ConfigurationRel: Configuration @hasOne
}

type Transaction @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  amount: Int
  method: String
  BookingRel: Booking @hasOne
}

type Booking @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  booking_data: String
  start_time: AWSDateTime
  end_time: AWSDateTime
  create_data: AWSDate
  status: String
  StationRel: Station @hasOne
  UserVehicleRel: UserVehicle @hasOne
  ChargingPointRel: ChargingPoint @hasOne
}

type ChargingPoint @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  connector_type: String
  last_heart_beat: AWSDateTime
  StationRel: Station @hasOne
  ConnectorTypeRel: ConnectorType @hasOne
}

type Station @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  station_name: String
  latitude: String
  longitude: String
  landmark: String
  pincode: String
  franchiseownerID: ID! @index(name: "byFranchiseOwner")
}

type UserVehicle @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  vehical_name: String
  vehical_number: String
  connector_type: String
  EndUserRel: EndUser @hasOne
}

type FranchiseOwner @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  last_name: String
  joining_date: AWSDate
  email: String
  contact: String
  FranchiseStations: [Station] @hasMany(indexName: "byFranchiseOwner", fields: ["id"])
}

enum WalletType {
  DEBIT
  CREDIT
}

type Wallet @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  last_amount_added: Int
  type: WalletType
  method: String
  timeStamp: AWSDateTime
  EndUserRel: EndUser @hasOne
}

enum PostStatus {
  ACTIVE
  INACTIVE
}

type EndUser @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  user_fullname: String
  dob: AWSDate
  joining_date: AWSDate
  email: String
  contact: String
  balance: Int
  rfidRel: [RFIDSchema] @hasMany(indexName: "byEndUser", fields: ["id"])
}

type Post @model @auth(rules: [{allow: private, provider: iam}]) {
  id: ID!
  title: String
  rating: Int
  status: PostStatus
  owner: String
}