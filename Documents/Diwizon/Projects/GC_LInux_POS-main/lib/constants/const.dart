import 'package:flutter/material.dart';

const testMode = false;
const productionMode = true;
// const testMode = true;
const unlimitedLimit = 999999;
const testingNo = "+91635381770426353817704";
const bgColor = Color(0xffE1FFE4);

const gkForNow = "AIzaSyCdEZN319f6HBV6w_wat4P-B50y1eBObug";

const myLocationMarkerId = "currentPositon";

const shareLink =
    "https://play.google.com/store/apps/details?id=com.elcop.techperspect";

const keepVehicleOptional = true;
const defaultDuration = 0.0;
// const defaultDuration = 30.0;
const maxDuration = 100.0;
const minDuration = 0.0;
const durationDivs = 20;
const heartbeatInerval = 120;

const autoCancelDuration = 2;
const veryLateDuration = 4;

class ChargerEvents {
  static const remoteStopTransaction = "RemoteStopTransaction";
  static const reset = "Reset";
  static const getConfiguration = "GetConfiguration";
  static const remoteStartTransaction = "RemoteStartTransaction";
}

class PoliciesLink {
  // static const pricing = "https://gc-policies.netlify.app/pricing.html";
  static const privacy = "https://www.gochargeindia.com/privacy-policy.html";
  static const contactUs = "https://www.gochargeindia.com/contact-us.html";
  static const tandc = "https://www.gochargeindia.com/terms-conditions.html";
  static const cancellation =
      "https://www.gochargeindia.com/cancellation-policy.html";
  static const refund =
      "https://www.gochargeindia.com/return-refund-policy.html";
}
