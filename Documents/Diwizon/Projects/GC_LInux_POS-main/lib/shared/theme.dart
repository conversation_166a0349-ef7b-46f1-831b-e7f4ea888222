/* 


import crypto from '@aws-crypto/sha256-js';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { SignatureV4 } from '@aws-sdk/signature-v4';
import { HttpRequest } from '@aws-sdk/protocol-http';
import { default as fetch, Request } from 'node-fetch';
import { Razorpay } from 'razorpay';

const GRAPHQL_ENDPOINT = process.env.API_GOCHARGE_GRAPHQLAPIENDPOINTOUTPUT;
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';
const { Sha256 } = crypto;


// This razorpayInstance will be used to 
// access any resource from razorpay 
const razorpayInstance = new Razorpay({
  key_id: rzp_test_cn6p8HqmFvpWSW,
  key_secret: kouKRBYkLFI51GTS9ntQUX8f
});


const query = /* GraphQL */ `
  query LIST_TODOS {
    listTodos {
      items {
        id
        name
        description
      }
    }
  }
`;

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

export const handler = async (event) => {
  console.log("Creating order....");
  const resp = await razorpayInstance.orders.create({
    "amount": 50000,
    "currency": "INR",
    "receipt": "receipt#1",
    "partial_payment": false,
    "notes": {
      "key1": "value3",
      "key2": "value2",
    }
  })

  console.log(resp);
  return {
    body: JSON.stringify(resp)
  };
}; */


/* import { default as fetch, Request } from 'node-fetch';
import AWS from 'aws-sdk';

const GRAPHQL_ENDPOINT = "https://nm6tgofdrjhqhirkywmne2audy.appsync-api.ap-south-1.amazonaws.com/graphql";
const GRAPHQL_API_KEY = "da2-3diwchio3vd57mogcjqkpgreoe";

// Create a new DynamoDB DocumentClient
const dynamodb = new AWS.DynamoDB.DocumentClient();

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
export const handler = async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);

  let statusCode = 200;
  let body;
  let response;
  let data;
  let connectorStatus;
  let version;
  let document_Id;
  try {
    // Extract the ChargePoint ID from the event object
    let { CP_ID } = event;
    connectorStatus = event.status;
    let connectorId = event.connectorId;

    console.log(`Charger ID and other attributes: ${CP_ID} and ${connectorId}`);
    // Define parameters for DynamoDB get operation
    const params = {
      TableName: 'Connector-r6cw5zqo7zb37hhq7w4ympiugy-prod', // Specify your DynamoDB table name

      FilterExpression: '#chargingPointId = :chargingPointId AND #connectorId = :connectorId', // Filter expression to check if givenDate is between startDate and endDate
      ExpressionAttributeNames: {
        '#chargingPointId': 'connector_name',
        '#connectorId': 'connector_number'
      },
      ExpressionAttributeValues: {
        ':chargingPointId': CP_ID,
        ':connectorId': connectorId
      }
    };

    // Call DynamoDB get operation to read data for the specified ID
    data = await dynamodb.scan(params).promise();

    // Return the retrieved data
    /*return {
        statusCode: 200,
        body: JSON.stringify(data.Item)
    };*/
    if (data.Items.length > 0) {
      console.log(data);
      body = data.Items[0];
      version = body._version;
      document_Id = body.id;
    }
    console.log(`Database Data returned: ${body} and ${document_Id}`);
  } catch (error) {
    // Return error response if an error occurs
    console.error('Error reading data from DynamoDB:', error);
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
  }


  const variables = {

    id: document_Id,
    connectorStatus: connectorStatus,
    _version: version,

  };


  //Get values from Event and Charge Table to update the connector status
  const query = /* GraphQL */`
    mutation MyMutation($id: ID!, $connectorStatus: String!, $_version: Int!) {
  updateConnector(input: {  # Add the input argument here
    id: $id,
    connectorStatus: $connectorStatus,
    _version: $_version
  }) {
    connectorStatus
    connector_name
    chargerID
    _version
  }
}`;


  /** @type {import('node-fetch').RequestInit} */
  const options = {
    method: 'POST',
    headers: {
      'x-api-key': GRAPHQL_API_KEY,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables })
  };

  const request = new Request(GRAPHQL_ENDPOINT, options);



  try {
    console.log("IN TRY");
    response = await fetch(request);
    console.log(response);
    body = await response.json();
    console.log(body);
    if (body.errors) statusCode = 400;
  } catch (error) {
    statusCode = 400;
    body = {
      errors: [
        {
          status: response.status,
          message: error.message,
          stack: error.stack
        }
      ]
    };
    console.log(`MUTATION ERROR: ${body} and ${error}`)
  }

  console.log("EXIT............");
  return {
    statusCode,
    body: JSON.stringify(body)
  };
}; */