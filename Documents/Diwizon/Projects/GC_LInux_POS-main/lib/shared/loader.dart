import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import '../theme/theme.dart';

class AppLoader extends StatelessWidget {
  const AppLoader({super.key, this.white = false, this.size = 20});
  final bool white;
  final double size;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: LoadingAnimationWidget.threeArchedCircle(
      size: size,
      color: white ? Colors.white : appColor,
    ));
  }
}
