import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_charge/shared/globals.dart';
import 'package:url_launcher/url_launcher.dart';
import '../controllers/app_ctrl.dart';
import '../models/Station.dart';

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

void navigateTo(num? lat, num? lng) async {
  var uri =
      Uri.parse('https://www.google.com/maps/search/?api=1&query=$lat,$lng');
  try {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } catch (e) {
    debugPrint(e.toString());
  }
}

clearSnacks() async {
  try {
    snackbarKey.currentState?.clearSnackBars();
  } catch (e) {
    debugPrint(e.toString());
  }
}

String generateId(int len) {
  String id = "";
  Random random = Random();
  for (int i = 0; i < len; i++) {
    if (random.nextBool()) {
      id += getChar();
    } else {
      id += getInt();
    }
  }
  return id;
}

String getChar() {
  Random random = Random();
  List<int> tmp = List<int>.generate(26, (int index) => index + 65);
  var str = String.fromCharCodes(tmp);
  return str.split("")[random.nextInt(26)].toString();
}

String getInt() {
  Random random = Random();
  return random.nextInt(10).toString();
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
