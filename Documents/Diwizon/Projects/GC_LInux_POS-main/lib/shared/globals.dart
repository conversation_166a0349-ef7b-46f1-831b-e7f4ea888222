import 'package:flutter/material.dart';

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();

const supportOptions = {
  "App Usage": [
    "Login",
    "Operating the App",
    "App not working",
    "Slow Performance",
    "I have some other issue"
  ],
  "Charger": [
    "Operating Charger",
    "Locate the QR Code",
    "Emergency Stop",
    "I have some other issue"
  ],
  "Charging": [
    "Quick charge/ QR Code",
    "Book a slot",
    "Start charging from app",
    "Stop charging from app",
    "Get OTP for Charging",
    "I have some other issue"
  ],
  "Payments": [
    "Pay using payment gateway",
    "Pay using wallet",
    "Recharge in account",
    "Get the refund of last transaction"
  ],
  "Other": ["Other"],
};
