// import 'dart:convert';
// import 'package:crypto/crypto.dart';
// import 'package:flutter/material.dart';
// import 'package:go_charge/utils/http_handler.dart';
// import 'package:go_charge/views/pos/pg_checkout.dart';
// import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:url_launcher/url_launcher_string.dart';
// import 'crypto.dart';
// import 'payment_page.dart';
// import 'package:http/http.dart' as http;

// class PaymentModes {
//   static const upi = "UPI_QR";
//   static const payPage = "PAY_PAGE";
//   static const upiCollect = "UPI_COLLECT";
// }

// class PaymentHandler {
//   static Future<String?> initiatePhonePePayment({
//     required BuildContext context,
//     required String transactionId,
//     required double amount,
//     required String mobileNumber,
//     String? vpa,
//     String mode = PaymentModes.payPage,
//   }) async {
//     try {
//       final response = await http.post(
//         Uri.parse(
//             // 'https://o893evnl71.execute-api.ap-south-1.amazonaws.com/default'),
//             'https://guqw4emxlljmjynmw2al4dztry0uutpm.lambda-url.ap-south-1.on.aws/'),
//         headers: {
//           // 'Content-Type': 'application/json',
//         },
//         body: json.encode({
//           'transactionId': transactionId,
//           'amount': amount,
//           'mobileNumber': mobileNumber,
//         }),
//       );
//       debugPrint(response.body);

//       final responseData = json.decode(response.body);
//       debugPrint(response.toString());

//       if (responseData != null && responseData['success'] == true) {
//         final paymentUrl =
//             responseData['data']['instrumentResponse']['redirectInfo']['url'];
//         final uri = Uri.parse(paymentUrl);
//         if (await canLaunchUrl(uri)) {
//           debugPrint('Launching $paymentUrl');
//           if (context.mounted) {
//             Navigator.push(
//               context,
//               MaterialPageRoute(
//                   builder: (context) =>
//                       PhonePeCheckoutWeb(tokenUrl: paymentUrl)),
//             );
//           }
//           // await launchUrl(uri);
//           return paymentUrl;
//         } else {
//           debugPrint('Could not launch $paymentUrl');
//         }
//       }
//       return null;
//     } catch (e) {
//       debugPrint('Error initiating PhonePe payment: ${e.toString()}');
//       return null;
//     }
//   }

//   static initSdkPayment(
//       {required String transId,
//       required BuildContext context,
//       required String mode,
//       required String mobileNumber,
//       required String callbackUrl,
//       String? vpa,
//       required double amount}) async {
//     try {
//       Map<String, dynamic> payload = {
//         "merchantId": PhonePe.merchantId,
//         "merchantTransactionId": transId,
//         "merchantUserId": "GOCHARGE123",
//         "amount": amount * 100, // 100, // TODO:
//         "redirectUrl": PhonePe.redirectUrl,
//         "redirectMode": "POST",
//         "callbackUrl": PhonePe.callback,
//         "mobileNumber": mobileNumber,
//         // "paymentScope": "PHONEPE",
//         "deviceContext": {"phonePeVersionCode": "303391"},
//         "paymentScope": "ALL_UPI_APPS",
//         "openIntentWithApp": "com.phonepe.app",
//         "paymentInstrument": {
//           "type": mode,
//           // "targetApp": "com.phonepe.app",
//           if (mode == PaymentModes.upiCollect) "vpa": vpa
//         }
//       };
//       final initResp = await PhonePePaymentSdk.init(
//               "PRODUCTION", "appId", PhonePe.merchantId, true)
//           .then((val) {
//         debugPrint(val.toString());
//       }).catchError((error) {
//         debugPrint(error.toString());
//         return <dynamic>{};
//       });
//       print(initResp);
//       final encodedPayload = Crypto.getBase64(json.encode(payload));
//       debugPrint(encodedPayload.toString());
//       final sha256Encoded = Crypto.getSHA256(
//           '$encodedPayload${PhonePe.paymentRoute}${PhonePe.phonePeKey}');
//       final xVerify = '$sha256Encoded###${PhonePe.phonePeKeyIndex}';
//       debugPrint(xVerify);
//       try {
//         var response = PhonePePaymentSdk.startTransaction(
//             encodedPayload, "", xVerify, null);
//         response.then((val) {
//           debugPrint(val.toString());
//         }).catchError((error) {
//           debugPrint(error.toString());
//           return <dynamic>{};
//         });
//       } catch (error) {
//         debugPrint(error.toString());
//       }
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//   }

//   static Future<PGResponse?> initPayment(
//       {required String transId,
//       required BuildContext context,
//       required String mode,
//       required String mobileNumber,
//       required String callbackUrl,
//       String? vpa,
//       required double amount}) async {
//     try {
//       debugPrint('>>>> $transId');
//       Map<String, dynamic> payload = {
//         "merchantId": PhonePe.merchantId,
//         "merchantTransactionId": transId,
//         "merchantUserId": "GOCHARGE12332",
//         "amount": amount * 100, // 100, // TODO:
//         "redirectUrl": PhonePe.redirectUrl,
//         "redirectMode": "POST",
//         "callbackUrl": PhonePe.callback,
//         "mobileNumber": mobileNumber,
//         // "paymentScope": "PHONEPE",
//         // "deviceContext": {"phonePeVersionCode": "303391"},
//         // "paymentScope": "ALL_UPI_APPS",
//         // "openIntentWithApp": "com.phonepe.app",
//         "paymentInstrument": {
//           "type": mode,
//           // "targetApp": "com.phonepe.app",
//           // if (mode == PaymentModes.upiCollect) "vpa": vpa
//         }
//       };
//       final encodedPayload = Crypto.getBase64(json.encode(payload));
//       debugPrint(encodedPayload.toString());
//       final sha256Encoded = Crypto.getSHA256(
//           '$encodedPayload${PhonePe.paymentRoute}${PhonePe.phonePeKey}');
//       final xVerify = '$sha256Encoded###${PhonePe.phonePeKeyIndex}';
//       debugPrint(xVerify);
//       // Generate Payment Link //
//       var headers = {"Content-Type": "application/json", "X-VERIFY": xVerify};
//       var body = {"request": encodedPayload};
//       // return;
//       final response = await HttpHandler.post(
//           PhonePe.paymentUrl, headers, json.encode(body));
//       if (response != null) {
//         if (response["success"] == true) {
//           if (mode == PaymentModes.upi) {
//             // UPI
//             final launchUrl =
//                 response["data"]["instrumentResponse"]["intentUrl"];
//             // final qrData = response["data"]["instrumentResponse"]["qrData"];
//             launchUrlString(launchUrl);
//             return PGResponse.fromJson(true, response);
//           } else if (mode == PaymentModes.payPage) {
//             // Pay Page
//             final paymentUrl =
//                 response["data"]["instrumentResponse"]["redirectInfo"]["url"];
//             /*   if (context.mounted) {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                     builder: (context) => PaymentPage(paymentUrl: paymentUrl)),
//               );
//             } */
//             // Get.to(() => PaymentPage(paymentUrl: paymentUrl));
//             return PGResponse.fromJson(false, response);
//           }
//         }
//       }
//       return null;
//     } catch (e) {
//       debugPrint(e.toString());
//       return null;
//     }
//   }

//   static Future<void> initiatePhonePeV1Payment(
//       {required String transactionId,
//       required String
//           amountInPaisa, // Amount should be in paisa (e.g., "1000" for ₹10)
//       required BuildContext context}) async {
//     // 1. Prepare the payment request data (as per PhonePe V1 API)
//     Map<String, dynamic> paymentData = {
//       'merchantId': PhonePe.merchantId,
//       'merchantTransactionId': transactionId,
//       'amount': amountInPaisa,
//       'callbackUrl': PhonePe.callback,
//       'redirectUrl': PhonePe
//           .redirectUrl, // Redirect URL was often the same as callback in V1
//       'paymentInstrument': {
//         'type': 'PAY_PAGE', // For initiating via PhonePe's payment page
//       },
//     };

//     // 2. **CRITICAL: Checksum Generation MUST happen on your backend.**
//     // The following is a simplified ILLUSTRATION and IS NOT SECURE for production.
//     String dataToHash =
//         '/pg/v1/pay' + jsonEncode(paymentData) + PhonePe.phonePeKey;
//     Digest checksum = sha256.convert(utf8.encode(dataToHash));
//     String finalChecksum =
//         checksum.toString() + '###1'; // Append salt index (often 1 in V1)

//     // 3. Construct the PhonePe payment URL (V1 format - may be different now)
//     String phonePeUrl = 'https://mercury-uat.phonepe.com/v1/pay' +
//         '?merchantId=${PhonePe.merchantId}' +
//         '&merchantTransactionId=$transactionId' +
//         '&amount=$amountInPaisa' +
//         '&callbackUrl=${PhonePe.callback}' +
//         '&redirectUrl=${PhonePe.redirectUrl}' +
//         '&paymentInstrument=' +
//         Uri.encodeComponent(jsonEncode(paymentData['paymentInstrument'])) +
//         '&checksum=$finalChecksum';

//     // 4. Launch the URL
//     if (await canLaunchUrl(Uri.parse(phonePeUrl))) {
//       print('Launching $phonePeUrl');

//       // await launchUrl(Uri.parse(phonePeUrl));
//       Navigator.push(
//         context,
//         MaterialPageRoute(
//             builder: (context) => PhonePeCheckoutWeb(tokenUrl: phonePeUrl)),
//       );
//     } else {
//       print('Could not launch $phonePeUrl');
//       // Handle error appropriately in your UI
//     }

//     // 5. **Backend should handle the callback and verify the payment status.**
//   }
// }

// /*
// {
//   "success": true,
//   "code": "PAYMENT_INITIATED",
//   "message": "Payment Initiated",
//   "data": {
//     "merchantId": "MERCHANTUAT",
//     "merchantTransactionId": "MT7850590068188104",
//         "instrumentResponse": {
//             "type": "UPI_INTENT",
//             "intentUrl": "upi://pay?pa=MERCHANTUAT@ybl&pn=MerchantUAT&am=3.00&mam=3.00&tr=OD620471739210623&tn=Payment%20for%OD620471739210623&mc=5311&mode=04&purpose=00&utm_campaign=DEBIT&utm_medium=FKRT&utm_source=OD620471739210623"
//     }
//     }
// }
// {
//   "success": true,
//   "code": "PAYMENT_INITIATED",
//   "message": "Payment Iniiated",
//   "data": {
//     "merchantId": "MERCHANTUAT",
//     "merchantTransactionId": "MT7850590068188104",
//     "instrumentResponse": {
//         "type": "PAY_PAGE",
//             "redirectInfo": {
//             "url": "https://mercury-uat.phonepe.com/transact?token=MjdkNmQ0NjM2MTk5ZTlmNDcxYjY3NTAxNTY5MDFhZDk2ZjFjMDY0YTRiN2VhMjgzNjIwMjBmNzUwN2JiNTkxOWUwNDVkMTM2YTllOTpkNzNkNmM2NWQ2MWNiZjVhM2MwOWMzODU0ZGEzMDczNA",
//         "method": "GET"
//       }
//     }
//   }
// } */

// class PGResponse {
//   final bool upi;
//   final bool success;
//   final String code;
//   final String message;
//   final String merchantId;
//   final String merchantTransactionId;
//   final String type;
//   final String url;
//   final DateTime createdAt;

//   PGResponse(
//       {required this.success,
//       required this.upi,
//       required this.code,
//       required this.message,
//       required this.merchantId,
//       required this.merchantTransactionId,
//       required this.type,
//       required this.createdAt,
//       required this.url});

//   static PGResponse fromJson(bool upi, json) {
//     return PGResponse(
//       createdAt: DateTime.now(),
//       upi: upi,
//       success: json["success"],
//       code: json["code"],
//       message: json["message"],
//       merchantId: json["data"]["merchantId"],
//       merchantTransactionId: json["data"]["merchantTransactionId"],
//       type: json["data"]["instrumentResponse"]["type"],
//       url: upi
//           ? json["data"]["instrumentResponse"]["intentUrl"]
//           : json["data"]["instrumentResponse"]["redirectInfo"]["url"],
//     );
//   }
// }

// class PhonePe {
//   static const paymentUrl = "https://api.phonepe.com/apis/hermes/pg/v1/pay";
//   // static const paymentUrl =
//   // "https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay";
//   static const redirectUrl = "https://www.gochargeindia.com/";
//   static const callback =
//       "https://jzxu2xbx6iynepswahdepjqmz40pgqcq.lambda-url.ap-south-1.on.aws/";
//   // "https://vn5vhy6dm5a636rky3creedy2u0chkej.lambda-url.ap-south-1.on.aws/";
//   // "https://gmzr2nagobl2zn7y3jtgwjz4nm0pdsba.lambda-url.ap-south-1.on.aws/";
//   static const merchantId = "M22861WGHBNS0";
//   static const paymentRoute = "/pg/v1/pay";
//   static const phonePeKey = "3dc974a1-27cc-49bc-b19c-055b606845cf"; // PROD
//   // static const phonePeKey = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399";
//   static const phonePeKeyIndex = 1;
// }
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/utils/crypto.dart';
import 'package:go_charge/utils/http_handler.dart';
import 'package:go_charge/views/pos/pg_checkout.dart';
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import 'package:url_launcher/url_launcher_string.dart';

import 'payment_page.dart';

class PaymentModes {
  static const upi = "UPI_QR";
  static const payPage = "PAY_PAGE";
  static const upiCollect = "UPI_COLLECT";
}

class PaymentHandler {
  static initSdkPayment(
      {required String transId,
      required BuildContext context,
      required String mode,
      required String mobileNumber,
      required String callbackUrl,
      String? vpa,
      required double amount}) async {
    try {
      Map<String, dynamic> payload = {
        "merchantId": PhonePe.merchantId,
        "merchantTransactionId": transId,
        "merchantUserId": "GOCHARGE123",
        "amount": amount * 100, // 100, // TODO:
        "redirectUrl": PhonePe.redirectUrl,
        "redirectMode": "POST",
        "callbackUrl": PhonePe.callback,
        "mobileNumber": mobileNumber,
        // "paymentScope": "PHONEPE",
        "deviceContext": {"phonePeVersionCode": "303391"},
        "paymentScope": "ALL_UPI_APPS",
        "openIntentWithApp": "com.phonepe.app",
        "paymentInstrument": {
          "type": mode,
          // "targetApp": "com.phonepe.app",
          if (mode == PaymentModes.upiCollect) "vpa": vpa
        }
      };
      final initResp = await PhonePePaymentSdk.init(
              "PRODUCTION", "appId", PhonePe.merchantId, true)
          .then((val) {
        debugPrint(val.toString());
      }).catchError((error) {
        debugPrint(error.toString());
        return <dynamic>{};
      });
      print(initResp);
      final encodedPayload = Crypto.getBase64(json.encode(payload));
      debugPrint(encodedPayload.toString());
      final sha256Encoded = Crypto.getSHA256(
          '$encodedPayload${PhonePe.paymentRoute}${PhonePe.phonePeKey}');
      final xVerify = '$sha256Encoded###${PhonePe.phonePeKeyIndex}';
      debugPrint(xVerify);
      try {
        var response = PhonePePaymentSdk.startTransaction(
            encodedPayload, "", xVerify, null);
        response.then((val) {
          debugPrint(val.toString());
        }).catchError((error) {
          debugPrint(error.toString());
          return <dynamic>{};
        });
      } catch (error) {
        debugPrint(error.toString());
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  static Future<PGResponse?> initPayment(
      {required String transId,
      required BuildContext context,
      required String mode,
      required String mobileNumber,
      required String callbackUrl,
      String? vpa,
      required double amount}) async {
    try {
      debugPrint('>>>> $transId');
      Map<String, dynamic> payload = {
        "merchantId": PhonePe.merchantId,
        "merchantTransactionId": transId,
        "merchantUserId": "GOCHARGE123",
        "amount": amount * 100, // 100, // TODO:
        "redirectUrl": PhonePe.redirectUrl,
        "redirectMode": "POST",
        "callbackUrl": PhonePe.callback,
        "mobileNumber": mobileNumber,
        // "paymentScope": "PHONEPE",
        // "deviceContext": {"phonePeVersionCode": "303391"},
        // "paymentScope": "ALL_UPI_APPS",
        // "openIntentWithApp": "com.phonepe.app",
        "paymentInstrument": {
          "type": mode,
          // "targetApp": "com.phonepe.app",
          if (mode == PaymentModes.upiCollect) "vpa": vpa
        }
      };
      final encodedPayload = Crypto.getBase64(json.encode(payload));
      debugPrint(encodedPayload.toString());
      final sha256Encoded = Crypto.getSHA256(
          '$encodedPayload${PhonePe.paymentRoute}${PhonePe.phonePeKey}');
      final xVerify = '$sha256Encoded###${PhonePe.phonePeKeyIndex}';
      debugPrint(xVerify);
      // Generate Payment Link //
      var headers = {"Content-Type": "application/json", "X-VERIFY": xVerify};
      var body = {"request": encodedPayload};
      // return;
      final response = await HttpHandler.post(
          PhonePe.paymentUrl, headers, json.encode(body));
      if (response != null) {
        if (response["success"] == true) {
          if (mode == PaymentModes.upi) {
            // UPI
            final launchUrl =
                response["data"]["instrumentResponse"]["intentUrl"];
            // final qrData = response["data"]["instrumentResponse"]["qrData"];
            launchUrlString(launchUrl);
            return PGResponse.fromJson(true, response);
          } else if (mode == PaymentModes.payPage) {
            // Pay Page
            final paymentUrl =
                response["data"]["instrumentResponse"]["redirectInfo"]["url"];
            if (context.mounted) {
              // launchUrlString(paymentUrl);
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => PaymentPage(paymentUrl: paymentUrl)),
              );
            }
            // Get.to(() => PaymentPage(paymentUrl: paymentUrl));
            return PGResponse.fromJson(false, response);
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}

/* 
{
  "success": true,
  "code": "PAYMENT_INITIATED",
  "message": "Payment Initiated",
  "data": {
    "merchantId": "MERCHANTUAT",
    "merchantTransactionId": "MT7850590068188104",
        "instrumentResponse": {
            "type": "UPI_INTENT",
            "intentUrl": "upi://pay?pa=MERCHANTUAT@ybl&pn=MerchantUAT&am=3.00&mam=3.00&tr=OD620471739210623&tn=Payment%20for%OD620471739210623&mc=5311&mode=04&purpose=00&utm_campaign=DEBIT&utm_medium=FKRT&utm_source=OD620471739210623"
    }   
    }
}
{
  "success": true,
  "code": "PAYMENT_INITIATED",
  "message": "Payment Iniiated",
  "data": {
    "merchantId": "MERCHANTUAT",
    "merchantTransactionId": "MT7850590068188104",
    "instrumentResponse": {
        "type": "PAY_PAGE",
            "redirectInfo": {
            "url": "https://mercury-uat.phonepe.com/transact?token=MjdkNmQ0NjM2MTk5ZTlmNDcxYjY3NTAxNTY5MDFhZDk2ZjFjMDY0YTRiN2VhMjgzNjIwMjBmNzUwN2JiNTkxOWUwNDVkMTM2YTllOTpkNzNkNmM2NWQ2MWNiZjVhM2MwOWMzODU0ZGEzMDczNA",
        "method": "GET"
      }
    }
  }
} */

class PGResponse {
  final bool upi;
  final bool success;
  final String code;
  final String message;
  final String merchantId;
  final String merchantTransactionId;
  final String type;
  final String url;
  final DateTime createdAt;

  PGResponse(
      {required this.success,
      required this.upi,
      required this.code,
      required this.message,
      required this.merchantId,
      required this.merchantTransactionId,
      required this.type,
      required this.createdAt,
      required this.url});

  static PGResponse fromJson(bool upi, json) {
    return PGResponse(
      createdAt: DateTime.now(),
      upi: upi,
      success: json["success"],
      code: json["code"],
      message: json["message"],
      merchantId: json["data"]["merchantId"],
      merchantTransactionId: json["data"]["merchantTransactionId"],
      type: json["data"]["instrumentResponse"]["type"],
      url: upi
          ? json["data"]["instrumentResponse"]["intentUrl"]
          : json["data"]["instrumentResponse"]["redirectInfo"]["url"],
    );
  }
}

class PhonePe {
  static const paymentUrl = "https://api.phonepe.com/apis/hermes/pg/v1/pay";
  // static const paymentUrl =
  // "https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay";
  static const redirectUrl = "https://www.gochargeindia.com/";
  static const callback =
      "https://jzxu2xbx6iynepswahdepjqmz40pgqcq.lambda-url.ap-south-1.on.aws/";
  // "https://vn5vhy6dm5a636rky3creedy2u0chkej.lambda-url.ap-south-1.on.aws/";
  // "https://gmzr2nagobl2zn7y3jtgwjz4nm0pdsba.lambda-url.ap-south-1.on.aws/";
  static const merchantId = "M22861WGHBNS0";
  static const paymentRoute = "/pg/v1/pay";
  static const phonePeKey = "3dc974a1-27cc-49bc-b19c-055b606845cf"; // PROD
  // static const phonePeKey = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399";
  static const phonePeKeyIndex = 1;
}
