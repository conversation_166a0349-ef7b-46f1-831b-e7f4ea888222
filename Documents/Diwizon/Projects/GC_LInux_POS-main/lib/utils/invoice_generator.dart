import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/services.dart';
import 'package:go_charge/utils/extentions.dart';
import 'package:indian_currency_to_word/indian_currency_to_word.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../models/ModelProvider.dart';
import '../shared/snackbar.dart';

Future<void> generateInvoice(ChargingTable charging) async {
  try {
    final pdf = pw.Document();

    // Load the logo from the assets
    final ByteData bytes = await rootBundle.load('assets/Logo.png');
    final Uint8List logoData = bytes.buffer.asUint8List();

    // Load the custom font
    final fontData = await rootBundle.load('assets/NotoSans-Regular.ttf');
    final ttf = pw.Font.ttf(fontData);

    // Get CPO Data
    final request = ModelQueries.get(Station.classType,
        StationModelIdentifier(id: charging.station_id ?? ""));
    final response = await Amplify.API.query(request: request).response;
    Station? station = response.data;

    // Get CPO Data
    final cpoRequest = ModelQueries.get(
        CPO.classType, CPOModelIdentifier(id: station?.cpoId ?? ""));
    final cpoResponse = await Amplify.API.query(request: cpoRequest).response;
    CPO? cpo = cpoResponse.data;

    // Get CPO Data
    final taxRequest = ModelQueries.get(
        Taxes.classType, TaxesModelIdentifier(id: cpo?.taxId ?? ""));
    final taxResponse = await Amplify.API.query(request: taxRequest).response;
    Taxes? tax = taxResponse.data;
    final originalCost = (charging.costOfConsump ?? 0) *
        100 /
        (100 + (charging.taxPercent ?? 0));
    num appliedTaxAmount = (charging.costOfConsump ?? 0) - originalCost;
    // print(charging.isIgst == true);
    pdf.addPage(
      pw.MultiPage(
        margin: const pw.EdgeInsets.all(32),
        build: (context) => [
          pw.Header(
            padding: const pw.EdgeInsets.only(bottom: 8),
            outlineColor: PdfColors.grey200,
            decoration: const pw.BoxDecoration(
                border:
                    pw.Border(bottom: pw.BorderSide(color: PdfColors.grey))),
            level: 0,
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Container(
                  height: 80,
                  width: 120,
                  child: pw.Image(pw.MemoryImage(logoData)),
                ),
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text('Tax Invoice',
                        style: pw.TextStyle(
                            fontSize: 24,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green)),
                    pw.Text(
                        charging.invoiceId != null
                            ? 'Invoice No: ${charging.invoiceId}'
                            : 'Invoice No: E${(charging.invoiceNo ?? "").toString().padLeft(8, "0")}',
                        style: pw.TextStyle(fontSize: 14, font: ttf)),
                    pw.Text(
                        'Dated: ${charging.start_time?.getDateTimeInUtc().toLocal().goodDayDate()}',
                        style: pw.TextStyle(fontSize: 14, font: ttf)),
                  ],
                ),
              ],
            ),
          ),
          pw.Table(
            children: [
              pw.TableRow(
                children: [
                  pw.Text('Invoice From',
                      style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green,
                          font: ttf)),
                  pw.Text('Invoice To',
                      style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green,
                          font: ttf)),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Name: ${cpo?.name ?? ""}',
                            style: pw.TextStyle(font: ttf)),
                        pw.Text('Mobile Number: ${cpo?.contact ?? ""}',
                            style: pw.TextStyle(font: ttf)),
                        pw.Text('GSTIN: ${tax?.gstno ?? ""}',
                            style: pw.TextStyle(font: ttf)),
                        // pw.Text('CIN: ${tax?.cinNo ?? ""}',
                        //     style: pw.TextStyle(font: ttf)),
                        pw.Text('Address: ${cpo?.city ?? ""}',
                            style: pw.TextStyle(font: ttf)),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Name: ${charging.userName}',
                            style: pw.TextStyle(font: ttf)),
                        pw.Text('Mobile No: ${charging.userContact}',
                            style: pw.TextStyle(font: ttf)),
                        if (charging.gstName?.isNotEmpty ?? false)
                          pw.Text('GST Name: ${charging.gstName ?? ""}',
                              style: pw.TextStyle(font: ttf)),
                        if (charging.gstin?.isNotEmpty ?? false)
                          pw.Text('GST No: ${charging.gstin ?? ""}',
                              style: pw.TextStyle(font: ttf)),
                        // pw.Text('Address:', style: pw.TextStyle(font: ttf)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          // pw.SizedBox(height: 12),
          // pw.Divider(color: PdfColors.grey, thickness: 1),
          // pw.SizedBox(height: 6),
          pw.SizedBox(height: 10),
          pw.Text('Charging Station Details',
              style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green,
                  font: ttf)),
          pw.Text('Station Name: ${station?.station_name}',
              style: pw.TextStyle(font: ttf)),
          pw.Text('Address: ${station?.address}',
              style: pw.TextStyle(font: ttf)),
          pw.Text('Phone: ${station?.contact_no}',
              style: pw.TextStyle(font: ttf)),
          // pw.Text('Email: ${station?.email}',
          // style: pw.TextStyle(font: ttf)),
          // pw.SizedBox(height: 12),
          // pw.Divider(color: PdfColors.grey, thickness: 1),
          // pw.SizedBox(height: 6),
          pw.SizedBox(height: 10),
          pw.Text('Invoice Details',
              style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green,
                  font: ttf)),
          pw.Text('Booking Id: ${charging.booking_id}',
              style: pw.TextStyle(font: ttf)),
          pw.Text(
              'Invoice Amount: ₹ ${charging.costOfConsump?.toStringAsFixed(1)}',
              style: pw.TextStyle(font: ttf)),
          pw.Text(
              'Amount in Words: ${AmountToWords().convertAmountToWords(charging.costOfConsump ?? 0)}',
              style: pw.TextStyle(font: ttf)),
          pw.Text('Energy Consumption: ${charging.unitsBurned} kW',
              style: pw.TextStyle(font: ttf)),
          pw.Text(
              'Start Time: ${charging.startedAtTime?.getDateTimeInUtc().toLocal().goodDayDate() ?? ""} PM',
              style: pw.TextStyle(font: ttf)),
          pw.Text(
              'End Time: ${charging.stopedAtTime?.getDateTimeInUtc().toLocal().goodDayDate() ?? ""} PM',
              style: pw.TextStyle(font: ttf)),
          if (charging.vehical_number?.isNotEmpty ?? false)
            pw.Text('Vehicle No: ${charging.vehical_number ?? ""}',
                style: pw.TextStyle(font: ttf)),
          if (charging.startedAtTime != null && charging.stopedAtTime != null)
            pw.Text(
                'Duration: ${charging.startedAtTime!.getDateTimeInUtc().difference(charging.stopedAtTime!.getDateTimeInUtc()).inMinutes} m',
                style: pw.TextStyle(font: ttf)),
          // pw.SizedBox(height: 12),
          // pw.Divider(color: PdfColors.grey, thickness: 1),
          // pw.SizedBox(height: 6),
          pw.SizedBox(height: 10),
          pw.Text('Charges',
              style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green,
                  font: ttf)),
          pw.SizedBox(height: 4),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey),
            children: [
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  color: PdfColors.green100,
                ),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Description',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Amount',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, font: ttf)),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Charging Amount',
                        style: pw.TextStyle(font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('₹${(originalCost).toStringAsFixed(2)}',
                        // '₹${((charging.costOfConsump ?? 0) - (charging.tax_amount ?? 0)).toStringAsFixed(2)}',
                        style: pw.TextStyle(font: ttf)),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Tax Payable (CGST)',
                        style: pw.TextStyle(font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text(
                        '₹ ${((charging.isIgst != true ? appliedTaxAmount : 0) / 2).toStringAsFixed(2)}',
                        style: pw.TextStyle(font: ttf)),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Tax Payable (SGST)',
                        style: pw.TextStyle(font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text(
                        '₹ ${((charging.isIgst != true ? appliedTaxAmount : 0) / 2).toStringAsFixed(2)}',
                        style: pw.TextStyle(font: ttf)),
                  ),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Tax Payable (IGST)',
                        style: pw.TextStyle(font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text(
                        '₹ ${((charging.isIgst == true ? appliedTaxAmount : 0)).toStringAsFixed(2)}',
                        style: pw.TextStyle(font: ttf)),
                  ),
                ],
              ),
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  color: PdfColors.green100,
                ),
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text('Total',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, font: ttf)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(5),
                    child: pw.Text(
                        '₹ ${charging.costOfConsump?.toStringAsFixed(2)}',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, font: ttf)),
                  ),
                ],
              ),
            ],
          ),
          // pw.SizedBox(height: 20),
          // pw.Container(
          //   padding: const pw.EdgeInsets.all(10),
          //   color: PdfColors.green,
          //   child: pw.Text(
          //     'Total Payable by consumer: ₹ 67.50',
          //     style: pw.TextStyle(
          //       fontSize: 16,
          //       fontWeight: pw.FontWeight.bold,
          //       color: PdfColors.white,
          //       font: ttf,
          //     ),
          //   ),
          // ),
        ],
      ),
    );

    await Printing.layoutPdf(
        onLayout: (format) async => await pdf.save(),
        name: DateTime.now().microsecondsSinceEpoch.toString());

    // Uncomment the following lines if you want to share the PDF
    // await Printing.sharePdf(
    //     bytes: await pdf.save(),
    //     filename: '${DateTime.now().microsecondsSinceEpoch}.pdf');
  } catch (e) {
    safePrint(e);
    showAppShackBar("Something went wrong!");
  }
}
