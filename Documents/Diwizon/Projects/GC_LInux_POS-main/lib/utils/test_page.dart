// import 'dart:html' as html;
// import 'dart:ui_web' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_web_plugins/flutter_web_plugins.dart';

class IframeExample extends StatefulWidget {
  const IframeExample({super.key});

  @override
  _IframeExampleState createState() => _IframeExampleState();
}

class _IframeExampleState extends State<IframeExample> {
  // late Widget _iframeWidget;
  // final String _iframeUrl =
  // 'https://www.google.com'; // Replace with your desired URL

  @override
  void initState() {
    super.initState();
    // Only create the iframe element when running on the web
    // if (true) {
    //   // if (defaultTargetPlatform == TargetPlatform.web) {
    //   final html.IFrameElement iframe = html.IFrameElement()
    //     ..src = _iframeUrl
    //     ..style.border = 'none'
    //     ..style.width = '100%'
    //     ..style.height = '100%';

    //   // Register the iframe element with HtmlView
    //   _iframeWidget = HtmlElementView(viewType: 'iframe-view');
    //   // ignore: undefined_prefixed_name
    //   ui.platformViewRegistry.registerViewFactory(
    //     'iframe-view',
    //     (int viewId) => iframe,
    //   );
    // } else {
    //   _iframeWidget = const Text('Iframe is only supported on the web.');
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Iframe in Flutter Web'),
      ),
      // body: _iframeWidget != null
      body:
          // false
          //     ? SizedBox(
          //         width: 400,
          //         height: 300,
          //         child: _iframeWidget,
          //       )
          // :
          const Center(child: Text('Not available on this platform.')),
    );
  }
}
