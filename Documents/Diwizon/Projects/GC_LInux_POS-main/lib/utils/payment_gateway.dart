import 'dart:convert';
import 'package:amplify_api/amplify_api.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_charge/controllers/app_ctrl.dart';
import 'package:go_charge/models/ModelProvider.dart';
import 'package:go_charge/utils/crypto.dart';
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import 'payment_handler.dart';

const keyId = "***********************";
const keySecret = "2IsMZvfk9kPz2iB4loELYUfj";
// const keyId = "rzp_test_cn6p8HqmFvpWSW";
// const keySecret = "kouKRBYkLFI51GTS9ntQUX8f";
const testKeyId = "rzp_test_cn6p8HqmFvpWSW";
const testKeySecret = "kouKRBYkLFI51GTS9ntQUX8f";

class PaymentGateway {
  /*  static Razorpay? initRazorPayment(
      {required PgOrder pgOrder,
      required bool isWalletTopup,
      required Function handlePaymentErrorResponse,
      required Function handlePaymentSuccessResponse,
      required Function handleExternalWalletSelected}) {
    try {
      // print(pgOrder.amount);
      // print(pgOrder.id);
      // print(pgOrder.receipt);
      Razorpay razorpay = Razorpay();
      Map<String, dynamic> options = {
        'key': testMode ? testKeyId : keyId,
        'amount': pgOrder.amount,
        "send_sms_hash": true,
        "remember_customer": true,
        'name':
            Get.find<AppCtrl>().currentUserData?.user_fullname ?? 'Go Charge',
        'description': 'Booking',
        'order_id': pgOrder.id,
        // 'order': pgOrder.toJson(),
        'theme': {'color': '#33A63F'},
        'currency': 'INR',
        'method': {'upi': true},
        'prefill': {
          'contact': Get.find<AppCtrl>().currentUserData?.contact ?? "",
          'email': Get.find<AppCtrl>().currentUserData?.email ?? ""
        },
        'external': {
          'wallets': ['paytm']
        },
        'notes': {
          'bId': pgOrder.receipt,
          'uId': Get.find<AppCtrl>().currentAuthUser?.userId,
          'type': isWalletTopup ? "Wallet" : "Booking",
        }
      };
      razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, handlePaymentSuccessResponse);
      razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, handlePaymentErrorResponse);
      razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, handleExternalWalletSelected);
      razorpay.open(options);
      return razorpay;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  } */

  static initPhonePePayment({
    required String receipt,
    required String mobileNumber,
    required bool isWalletTopup,
    required num amount,
    required Function handlePaymentErrorResponse,
    required Function handlePaymentSuccessResponse,
  }) async {
    try {
      // print(pgOrder.amount);
      // print(pgOrder.id);
      print(receipt);
      final transDoc = PaymentData(
          uId: Get.find<AppCtrl>().currentAuthUser?.userId,
          type: isWalletTopup ? "Wallet" : "Booking",
          bId: receipt);
      final request = ModelMutations.create(transDoc);
      final transResponse = await Amplify.API.mutate(request: request).response;
      print(transResponse.errors);
      if (transResponse.data != null) {
        Map<String, dynamic> payload = {
          "merchantId": PhonePe.merchantId,
          "merchantTransactionId": receipt,
          "merchantUserId": "GOCHARGE123",
          "amount": amount, // 100, // TODO:
          "redirectUrl": PhonePe.redirectUrl,
          "redirectMode": "POST",
          "callbackUrl": PhonePe.callback,
          "mobileNumber": mobileNumber,
          // "paymentScope": "PHONEPE",
          "deviceContext": {"phonePeVersionCode": "303391"},
          "paymentScope": "ALL_UPI_APPS",
          "openIntentWithApp": "com.phonepe.app",
          "paymentInstrument": {
            "type": PaymentModes.payPage,
          }
        };
        // INIT PG
        await PhonePePaymentSdk.init(
                "PRODUCTION", null, PhonePe.merchantId, true)
            .then((val) {
          debugPrint(val.toString());
        }).catchError((error) {
          debugPrint(error.toString());
          return;
        });
        final encodedPayload = Crypto.getBase64(json.encode(payload));
        debugPrint(encodedPayload.toString());
        final sha256Encoded = Crypto.getSHA256(
            '$encodedPayload${PhonePe.paymentRoute}${PhonePe.phonePeKey}');
        final xVerify = '$sha256Encoded###${PhonePe.phonePeKeyIndex}';
        debugPrint(xVerify);
        try {
          PhonePePaymentSdk.startTransaction(encodedPayload, "", xVerify, null)
              .then((response) {
            debugPrint(response.toString());
            if (response != null) {
              String status = response['status'].toString();
              String error = response['error'].toString();
              debugPrint(error);
              if (status == 'SUCCESS') {
                // "Flow Completed - Status: Success!";
                handlePaymentSuccessResponse();
              } else {
                // "Flow Completed - Status: $status and Error: $error";
                handlePaymentErrorResponse();
              }
            } else {
              handlePaymentErrorResponse();
              // "Flow Incomplete";
            }
          }).catchError((error) {
            handlePaymentErrorResponse();
            debugPrint(error.toString());
            return;
          });
        } catch (error) {
          debugPrint(error.toString());
        }
      }
      return;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
