// /* import 'dart:async';
// // Required to use AppExitResponse for Fluter 3.10 or later
// import 'dart:ui';
// import 'package:flutter/material.dart';
// import 'package:webview_flutter/webview_flutter.dart';
// // import 'package:flutter_linux_webview/flutter_linux_webview.dart';

// class WebViewExample extends StatefulWidget {
//   const WebViewExample({super.key, required this.paymentUrl});

//   final String paymentUrl;

//   @override
//   _WebViewExampleState createState() => _WebViewExampleState();
// }

// class _WebViewExampleState extends State<WebViewExample>
//     with WidgetsBindingObserver {
//   final Completer<WebViewController> _controller =
//       Completer<WebViewController>();

//   /// Prior to Flutter 3.10, comment out the following code since
//   /// [WidgetsBindingObserver.didRequestAppExit] does not exist.
//   // ===== begin: For Flutter 3.10 or later =====
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addObserver(this);
//   }

//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     super.dispose();
//   }

//   // @override
//   // Future<AppExitResponse> didRequestAppExit() async {
//   //   await LinuxWebViewPlugin.terminate();
//   //   return AppExitResponse.exit;
//   // }
//   // ===== end: For Flutter 3.10 or later =====

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Complete Payment'),
//       ),
//       body: WebView(
//         initialUrl: widget.paymentUrl,
//         onWebViewCreated: (WebViewController webViewController) {
//           _controller.complete(webViewController);
//         },
//         javascriptMode: JavascriptMode.unrestricted,
//       ),
//     );
//   }
// }
//  */

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:webview_flutter/webview_flutter.dart';

// import 'payment_handler.dart';

// class WebViewExample extends StatefulWidget {
//   const WebViewExample({super.key, required this.paymentUrl});
//   final String paymentUrl;
//   @override
//   State<WebViewExample> createState() => _WebViewExampleState();
// }

// class _WebViewExampleState extends State<WebViewExample> {
//   WebViewController? controller;
//   @override
//   void initState() {
//     super.initState();
//     // init Web Controller
//     controller = WebViewController()
//       // ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color(0x00000000))
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onProgress: (int progress) {},
//           onPageStarted: (String url) {},
//           onPageFinished: (String url) {
//             if (url == PhonePe.redirectUrl) Get.back();
//           },
//           onWebResourceError: (WebResourceError error) {},
//         ),
//       )
//       ..loadRequest(Uri.parse(widget.paymentUrl));
//   }

//   Future<bool> _onBackPressed(BuildContext context) async {
//     return (await showDialog(
//           context: context,
//           builder: (context) => AlertDialog(
//             title: const Text("Confirm!"),
//             content: const Text("Go back to app?"),
//             actions: [
//               TextButton(
//                   onPressed: () => Navigator.of(context).pop(false),
//                   child: const Text("No")),
//               TextButton(
//                   onPressed: () => Navigator.of(context).pop(true),
//                   child: const Text("Yes")),
//             ],
//           ),
//         )) ??
//         false;
//   }

//   closeAll() async {
//     try {
//       await Future.delayed(const Duration(seconds: 1));
//       Navigator.of(context).pop();
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBar(),
//         body: HtmlElementView(
//           viewType: 'webview',
//           onPlatformViewCreated: (id) {},
//         ));
//     return WillPopScope(
//       onWillPop: () async => true,
//       // onWillPop: () async => _onBackPressed(context),
//       child: Scaffold(
//         appBar: AppBar(
//           title: const Text(
//             'Payment',
//             style: TextStyle(color: Colors.black),
//           ),
//           iconTheme: const IconThemeData(color: Colors.black),
//           backgroundColor: Colors.white,
//         ),
//         body: controller == null
//             ? const SizedBox()
//             : WebViewWidget(controller: controller!),
//       ),
//     );
//   }
// }
