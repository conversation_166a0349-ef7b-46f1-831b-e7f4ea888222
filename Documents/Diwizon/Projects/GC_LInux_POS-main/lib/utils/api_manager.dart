import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:go_charge/constants/const.dart';
import 'package:go_charge/model/pg_order.dart';
import 'package:go_charge/models/ChargingTable.dart';
import 'package:http/http.dart' as http;

class APIManager {
  static const createOrderUrl = productionMode
      ? "https://7ai7ovk67ms3geocgdylz3mcoy0jadzb.lambda-url.ap-south-1.on.aws/" // Amplify
      : "https://sj5fjho535pe56buz2ftmao2zi0hjhji.lambda-url.ap-south-1.on.aws/";
  static const messageProcessorUrl = productionMode
      ? "https://5jjulse62zjpkrjqlkicpjelcm0uzysx.lambda-url.ap-south-1.on.aws/" // CDK
      // ? "https://ojjkwesnvit4kzytvjuzdrgeba0rsyql.lambda-url.ap-south-1.on.aws/" // CDK
      : "https://5usyuo2wyafeodfpsphzk65gfu0adzcl.lambda-url.ap-south-1.on.aws/";
  static const cancelChargingUrl = productionMode
      ? "https://2iaceon3qy2pmmdcjogq6k7bia0iedck.lambda-url.ap-south-1.on.aws/" // Amplify
      : "https://vn2wzu3r4h6fsqmwioaulhxqhy0ahwcr.lambda-url.ap-south-1.on.aws/"; // Amplify

  static const bookingCheckSchUrl =
      "https://o2h5atfbwwo4s3dkztzkku63de0ohonz.lambda-url.ap-south-1.on.aws/";

  static Future<PgOrder?> createPGOrder(
      {required double amount,
      required String receipt,
      required String bId,
      required String uId,
      required String type}) async {
    try {
      // print("Going to pay $amount - ${amount.toInt()} -- Reciep - $receipt");
      final resp = await http.post(Uri.parse(createOrderUrl),
          body: json.encode({
            "amount": amount.toInt(),
            "receipt": receipt,
            "bId": bId,
            "uId": uId,
            "type": type,
          }));
      if (resp.statusCode == 200) {
        // print(resp.body);
        return PgOrder.fromRawJson(resp.body);
      } else {
        return null;
      }
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  static Future<bool> cancelCharging(ChargingTable charging) async {
    try {
      print("Going to cancel ${charging.id}");
      final resp = await http.post(Uri.parse(cancelChargingUrl),
          body: json.encode({
            "bookingId": charging.id,
            "userId": charging.user_id,
            "user_id": charging.user_id,
            "userName": charging.userName,
            "userContact": charging.userContact,
          }));
      if (resp.statusCode == 200) {
        // print(resp.body);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  static Future<bool> bookingCheckSch(ChargingTable charging) async {
    try {
      debugPrint("Scheduling boking check ${charging.id}");
      final resp = await http.post(Uri.parse(bookingCheckSchUrl),
          body: json.encode(
              {"bookingId": charging.booking_id, "userId": charging.user_id}));
      if (resp.statusCode == 200) {
        // print(resp.body);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  static Future<PgOrder?> callMessageProcessor(
      {required int? connectorNo,
      required String? idTag,
      required int? transactionId,
      required String action,
      required String chargerId}) async {
    try {
      final response = await http.post(Uri.parse(messageProcessorUrl),
          body: json.encode({
            if (connectorNo != null) "connectorId": connectorNo,
            if (idTag != null) "idTag": idTag,
            if (transactionId != null) "transactionId": transactionId,
            "action": action,
            "chargerId": chargerId,
          })); // TODO: int client global
      if (response.statusCode == 200) {
        print(response.body);
      } else {
        print(response.reasonPhrase);
      }

/*       var headers = {'Content-Type': 'application/json'};
      var request = http.Request('POST', Uri.parse(messageProcessorUrl));
      request.body = json.encode({
        if (connectorNo != null) "connectorId": connectorNo,
        if (idTag != null) "idTag": idTag,
        if (transactionId != null) "transactionId": transactionId,
        "action": action,
        "chargerId": chargerId,
      });
      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200) {
        debugPrint(await response.stream.bytesToString());
      } else {
        debugPrint(response.reasonPhrase);
      } */
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
    return null;
  }
}
