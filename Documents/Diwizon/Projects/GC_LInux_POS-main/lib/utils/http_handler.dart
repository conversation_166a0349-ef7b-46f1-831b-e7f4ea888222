import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class HttpHandler {
  static Future<dynamic> post(
      String url, Map<String, String>? headers, Object? body) async {
    try {
      final response =
          await http.post(Uri.parse(url), headers: headers, body: body);
      debugPrint(response.body);
      final decoded = json.decode(response.body);
      debugPrint(decoded.toString());
      return decoded;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
