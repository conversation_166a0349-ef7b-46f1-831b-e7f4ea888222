import 'dart:convert';

class PgOrder {
  final int amount;
  final int amountPaid;
  final List<dynamic> notes;
  final int createdAt;
  final int amountDue;
  final String currency;
  final String receipt;
  final String id;
  final String entity;
  final dynamic offerId;
  final String status;
  final int attempts;

  PgOrder({
    required this.amount,
    required this.amountPaid,
    required this.notes,
    required this.createdAt,
    required this.amountDue,
    required this.currency,
    required this.receipt,
    required this.id,
    required this.entity,
    required this.offerId,
    required this.status,
    required this.attempts,
  });

  factory PgOrder.fromRawJson(String str) => PgOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PgOrder.fromJson(Map<String, dynamic> json) => PgOrder(
        amount: json["amount"],
        amountPaid: json["amount_paid"],
        notes: [],
        // notes: List<dynamic>.from(json["notes"].map((x) => x)),
        createdAt: json["created_at"],
        amountDue: json["amount_due"],
        currency: json["currency"],
        receipt: json["receipt"],
        id: json["id"],
        entity: json["entity"],
        offerId: json["offer_id"],
        status: json["status"],
        attempts: json["attempts"],
      );

  Map<String, dynamic> toJson() => {
        "amount": amount,
        "amount_paid": amountPaid,
        "notes": List<dynamic>.from(notes.map((x) => x)),
        "created_at": createdAt,
        "amount_due": amountDue,
        "currency": currency,
        "receipt": receipt,
        "id": id,
        "entity": entity,
        "offer_id": offerId,
        "status": status,
        "attempts": attempts,
      };
}
